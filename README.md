# BPO Admin Backend

> **🚨 CRITICAL FOR AI ASSISTANTS**: Before providing ANY development assistance, you MUST read the [AI Project Rules](./.trae/rules/project_rules.md) and follow the mandatory documentation review process.

A comprehensive FastAPI-based backend system for Business Process Outsourcing administration, focusing on talent lifecycle management, payroll processing, and operational efficiency.

## 📋 MANDATORY READING (For Developers & AI Assistants)

**STOP! READ THESE FIRST** before starting any development work:

### 🎯 Essential Documentation (Read in Order)
1. **[MANDATORY_READING.md](./MANDATORY_READING.md)** - Critical pre-development requirements
2. **[Memory Bank README](./memory-bank/README.md)** - Complete project context and documentation structure
3. **[Project Brief](./memory-bank/projectbrief.md)** - Core requirements and business goals
4. **[Active Context](./memory-bank/activeContext.md)** - Current development status and priorities
5. **[Development Rules](./DEVELOPMENT_RULES.md)** - Mandatory coding standards and practices

### 🤖 For AI Assistants
- **[AI Project Rules](./.trae/rules/project_rules.md)** - Mandatory AI assistant requirements and guidelines
- **[Quick Reference](./QUICK_REFERENCE.md)** - Essential project information at a glance

## ✨ Key Features

### 🎯 Core Functionality
- **Talent Management**: Complete lifecycle from onboarding to offboarding
- **Payroll Processing**: Mexican tax compliance and banking integration
- **Document Management**: Secure document upload and tracking
- **Health Information**: Chronic conditions and medical history tracking
- **Emergency Contacts**: Comprehensive contact management
- **Master Data**: Roles, clients, locations, and equipment management

### 🏗️ Technical Features
- **Type-Safe Development**: Full SQLModel integration with comprehensive type hints
- **Layered Architecture**: Clean separation of API, Service, Repository, and Database layers
- **JWT Authentication**: Secure role-based access control
- **Structured Logging**: Comprehensive logging with Loguru
- **Auto-Documentation**: Interactive API docs with Swagger UI and ReDoc
- **Database Migrations**: Version-controlled schema management with Alembic

## 🛠️ Tech Stack

### Core Framework
- **Backend Framework**: FastAPI 0.115.14+ with async/await patterns
- **Python Version**: 3.13+ with modern type hints
- **Package Manager**: uv for fast dependency resolution
- **Server**: Uvicorn ASGI server with hot reload

### Database & ORM
- **Database**: MySQL with optimized connection pooling
- **ORM**: SQLModel for type-safe database operations
- **Migrations**: Alembic for version-controlled schema management
- **Connection**: SQLAlchemy async engine with proper session management

### Development Tools
- **Type Checking**: basedpyright for static type analysis
- **Code Formatting**: Black for consistent code style
- **Logging**: Loguru for structured application logging
- **Documentation**: Auto-generated OpenAPI with Swagger UI and ReDoc

### Security & Authentication
- **Authentication**: JWT-based with role management
- **Password Hashing**: bcrypt for secure password storage
- **CORS**: Configurable cross-origin resource sharing
- **Input Validation**: Pydantic schemas with comprehensive validation

## 📁 Project Structure

```
backend/
├── .trae/
│   └── rules/
│       └── project_rules.md     # AI assistant rules
├── memory-bank/                 # Project documentation system
│   ├── README.md               # Documentation overview
│   ├── projectbrief.md         # Core requirements
│   ├── activeContext.md        # Current development status
│   ├── progress.md             # Implementation tracking
│   ├── systemPatterns.md       # Architecture patterns
│   ├── techContext.md          # Technology decisions
│   └── productContext.md       # Business context
├── app/
│   ├── api/v1/                 # API endpoints (versioned)
│   │   ├── auth_router.py      # Authentication endpoints
│   │   ├── profile_router.py   # User profile management
│   │   └── talent/             # Talent management APIs
│   │       ├── candidate_router.py      # Talent profiles
│   │       ├── banking_router.py        # Banking information
│   │       ├── payroll_router.py        # Payroll management
│   │       ├── wage_router.py           # Wage information
│   │       ├── emergency_contact_router.py
│   │       ├── documents_router.py      # Document management
│   │       └── health_routers/          # Health information
│   ├── core/                   # Core utilities
│   │   ├── config.py          # Application configuration
│   │   ├── jwt.py             # JWT authentication
│   │   ├── logs.py            # Logging utilities
│   │   ├── password.py        # Password utilities
│   │   └── security.py        # Security utilities
│   ├── db/                    # Database layer
│   │   ├── models.py          # SQLModel definitions (15+ tables)
│   │   ├── session.py         # Database session management
│   │   ├── init_db.py         # Database initialization
│   │   └── seeders/           # Database seeding scripts
│   ├── repositories/          # Data access layer
│   │   ├── talent/            # Talent-related repositories
│   │   ├── master/            # Master data repositories
│   │   ├── profile_repository.py
│   │   └── user_repository.py
│   ├── services/              # Business logic layer
│   │   ├── talent/            # Talent management services
│   │   ├── master/            # Master data services
│   │   ├── auth_service.py    # Authentication service
│   │   └── profile_service.py
│   ├── schemas/               # Pydantic schemas
│   │   ├── talent/            # Talent-related schemas
│   │   ├── master/            # Master data schemas
│   │   ├── base.py            # Base schema classes
│   │   ├── login_schema.py
│   │   ├── user.py
│   │   └── token_data_schema.py
│   ├── response_models/       # API response models
│   │   ├── general_response.py
│   │   └── talent_profile_response.py
│   ├── middlewares/           # Custom middleware
│   │   └── cors_middleware.py
│   └── main.py               # Application entry point
├── scripts/
│   └── format.py             # Code formatting utility
├── MANDATORY_READING.md      # Critical pre-development guide
├── DEVELOPMENT_RULES.md      # Coding standards and practices
├── PROJECT_DOCUMENTATION.md  # Complete project overview
├── QUICK_REFERENCE.md        # Developer quick reference
├── pyproject.toml           # Project configuration
├── pyrightconfig.json       # Type checker configuration
└── uv.lock                  # Dependency lock file
```

## 📊 Current Implementation Status

- ✅ **Database Models**: Complete (15+ interconnected tables)
- ✅ **API Layer**: Complete (all talent management endpoints)
- ✅ **Service Layer**: Complete (business logic implemented)
- ✅ **Repository Layer**: Complete (data access implemented)
- ✅ **Authentication**: Complete (JWT with role management)
- ✅ **Documentation**: Complete (comprehensive framework)
- ❌ **Testing**: Not implemented (immediate priority)

## 🚀 Quick Start

### Prerequisites
- **Python**: 3.13+ (required for modern type hints)
- **MySQL**: 8.0+ (for database operations)
- **uv**: Latest version (for package management)

### 📖 Before You Begin

**IMPORTANT**: Read the [MANDATORY_READING.md](./MANDATORY_READING.md) file first to understand the project structure and development guidelines.

### Installation

1. **Clone and Navigate**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install uv Package Manager**
   ```bash
   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   
   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
   ```

3. **Install Dependencies**
   ```bash
   uv sync
   ```

4. **Database Setup**
   ```bash
   # Create MySQL database
   mysql -u root -p
   ```
   ```sql
   CREATE DATABASE bpo_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'bpo_user'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON bpo_admin.* TO 'bpo_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

5. **Environment Configuration**
   
   Create a `.env` file in the root directory:
   ```env
   # Database Configuration
   DATABASE_URL=mysql+mysqlconnector://bpo_user:your_secure_password@localhost:3306/bpo_admin
   
   # Application Configuration
   APP_PORT=8000
   APP_HOST=0.0.0.0
   DEBUG=true
   
   # JWT Configuration
   JWT_SECRET_KEY=your_super_secret_jwt_key_here
   JWT_ALGORITHM=HS256
   JWT_EXPIRATION_HOURS=24
   
   # Logging Configuration
   LOG_LEVEL=INFO
   ```

6. **Database Migration**
   ```bash
   # Run all migrations to set up the database schema
   uv run alembic upgrade head
   ```

7. **Verify Installation**
   ```bash
   # Check if everything is set up correctly
   uv run python -c "from app.db.session import get_session; print('Database connection successful!')"
   ```

## 🏃‍♂️ Running the Application

### Development Mode (Recommended)

```bash
# Start development server with hot reload
uv run python -m app.main

# Alternative: Direct uvicorn command
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level info
```

### Production Mode

```bash
# Production server (no hot reload)
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 🌐 Application URLs

Once running, access the application at:

- **🏠 API Base**: http://localhost:8000/api/v1
- **📚 Interactive Docs (Swagger)**: http://localhost:8000/docs
- **📖 Alternative Docs (ReDoc)**: http://localhost:8000/redoc
- **🔍 Health Check**: http://localhost:8000/health
- **📊 OpenAPI Spec**: http://localhost:8000/openapi.json

### 🔐 Authentication

To access protected endpoints:

1. **Login** via `/api/v1/auth/login` with valid credentials
2. **Copy the JWT token** from the response
3. **Use the token** in the Authorization header: `Bearer <your-token>`
4. **Or use Swagger UI**: Click "Authorize" button and paste the token

## 📚 API Documentation & Testing

### Interactive Documentation

The application provides comprehensive API documentation:

- **🎯 Swagger UI**: http://localhost:8000/docs
  - Interactive API testing interface
  - Built-in authentication support
  - Request/response examples
  - Schema validation

- **📖 ReDoc**: http://localhost:8000/redoc
  - Clean, readable API documentation
  - Detailed schema descriptions
  - Code examples in multiple languages

- **🔧 OpenAPI Spec**: http://localhost:8000/openapi.json
  - Machine-readable API specification
  - For integration with external tools

### 🎯 Key API Endpoints

#### Authentication
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/refresh` - Token refresh
- `POST /api/v1/auth/logout` - User logout

#### Talent Management
- `GET/POST /api/v1/talent/candidates` - Talent profile management
- `GET/POST /api/v1/talent/banking` - Banking information
- `GET/POST /api/v1/talent/payroll` - Payroll management
- `GET/POST /api/v1/talent/wages` - Wage information
- `GET/POST /api/v1/talent/emergency-contacts` - Emergency contacts
- `GET/POST /api/v1/talent/documents` - Document management
- `GET/POST /api/v1/talent/health` - Health information

## 🗄️ Database Architecture

### Core Models (15+ Tables)

The system uses a comprehensive, normalized database schema:

#### 👤 User & Authentication
```python
class User(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    email: str = Field(unique=True, index=True)
    hashed_password: str
    is_active: bool = Field(default=True)
    role: str = Field(default="user")
    created_at: datetime = Field(default_factory=datetime.utcnow)
```

#### 🎯 Talent Profile (Central Entity)
```python
class TalentProfile(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    first_name: str
    last_name: str
    email: str = Field(unique=True)
    phone: str | None = None
    date_of_birth: date | None = None
    # ... additional fields
```

#### 💰 Financial Information
- **TalentBanking**: Bank account details and Mexican tax compliance
- **TalentPayroll**: Payroll processing information
- **TalentWage**: Wage history and current compensation

#### 📋 Personal Information
- **TalentEmergencyContact**: Emergency contact details
- **TalentDocuments**: Document management and tracking
- **TalentHealthChronic**: Chronic health conditions
- **TalentHealthPast**: Past health information
- **TalentHealthOngoing**: Ongoing health treatments

#### 🏢 Master Data
- **Role**: System roles and permissions
- **Client**: Client information and management
- **Location**: Office and work locations
- **Position**: Job positions and descriptions
- **Equipment**: Equipment assignment tracking
- **Software**: Software license management

## ⚙️ Configuration

### Environment Variables

The application uses environment-based configuration:

```env
# Database Configuration
DATABASE_URL=mysql+mysqlconnector://user:password@localhost:3306/bpo_admin

# Application Settings
APP_PORT=8000
APP_HOST=0.0.0.0
DEBUG=true

# JWT Authentication
JWT_SECRET_KEY=your_super_secret_key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
```

### Configuration Management

Configuration is managed through:
- **Environment variables** for deployment-specific settings
- **app/core/config.py** for application configuration
- **Type-safe settings** using Pydantic BaseSettings

## 🛠️ Development Guidelines

### 📖 Before Starting Development

**MANDATORY**: Always follow this sequence before any development work:

1. **Read Documentation** (in order):
   - [MANDATORY_READING.md](./MANDATORY_READING.md)
   - [Memory Bank README](./memory-bank/README.md)
   - [Development Rules](./DEVELOPMENT_RULES.md)
   - [Active Context](./memory-bank/activeContext.md)

2. **Understand Architecture**: Review [System Patterns](./memory-bank/systemPatterns.md)
3. **Check Current Status**: Review [Progress](./memory-bank/progress.md)
4. **Plan Changes**: Ensure alignment with existing patterns

### 🏗️ Architectural Guidelines

**Layer Separation** (STRICTLY ENFORCED):
```
┌─────────────────┐
│   API Layer     │  ← FastAPI routers, request/response handling
├─────────────────┤
│  Service Layer  │  ← Business logic, validation, orchestration
├─────────────────┤
│Repository Layer │  ← Data access, database operations
├─────────────────┤
│  Database Layer │  ← SQLModel models, database schema
└─────────────────┘
```

### 📦 Dependency Management

```bash
# Add production dependency
uv add package-name

# Add development dependency
uv add --group dev package-name

# Update all dependencies
uv sync --upgrade

# Remove dependency
uv remove package-name
```

### 🗄️ Database Operations

```bash
# Create new migration
uv run alembic revision --autogenerate -m "Description of changes"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1

# Check migration status
uv run alembic current

# View migration history
uv run alembic history
```

## 🔧 Development Commands

### Code Quality & Formatting

```bash
# Format entire codebase
python scripts/format.py app/

# Format specific file
python scripts/format.py app/main.py

# Type checking
uv run basedpyright app/

# Check formatting without changes
uv run black --check app/

# Lint code
uv run ruff check app/
```

### Testing Commands

```bash
# Run all tests (when implemented)
uv run pytest

# Run tests with coverage
uv run pytest --cov=app

# Run specific test file
uv run pytest tests/test_auth.py

# Run tests in watch mode
uv run pytest-watch
```

### Database Utilities

```bash
# Reset database (development only)
uv run python -c "from app.db.init_db import reset_database; reset_database()"

# Seed database with test data
uv run python -c "from app.db.seeders import seed_all; seed_all()"

# Backup database
mysqldump -u bpo_user -p bpo_admin > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 💻 IDE Configuration

### VS Code Settings

Recommended extensions and settings are in `.vscode/`:
- **Python**: Official Python extension
- **Pylance**: Advanced Python language support
- **Black Formatter**: Code formatting
- **Thunder Client**: API testing

### Manual Formatting Shortcuts

- **Format entire document**: `Ctrl+Cmd+L` (macOS) / `Ctrl+Alt+L` (Windows/Linux)
- **Format selection**: `Ctrl+Cmd+Shift+L` (macOS) / `Ctrl+Alt+Shift+L` (Windows/Linux)
- **Command line**: `python scripts/format.py app/`

## 🧪 Code Quality Standards

### Mandatory Requirements

- ✅ **Type Hints**: All functions must have complete type annotations
- ✅ **Error Handling**: Comprehensive try/catch blocks with logging
- ✅ **Response Patterns**: Use GeneralResponse for all API endpoints
- ✅ **Logging**: Structured logging for all operations
- ✅ **Documentation**: Docstrings for all public methods

### Quality Tools

- **basedpyright**: Static type checking
- **Black**: Code formatting
- **Ruff**: Fast Python linter
- **Loguru**: Structured logging

```bash
# Run all quality checks
uv run basedpyright app/ && python scripts/format.py app/ && uv run ruff check app/
```

## 🤝 Contributing

### Development Workflow

1. **Read Documentation**: Follow the mandatory reading sequence
2. **Create Feature Branch**: `git checkout -b feature/your-feature-name`
3. **Follow Architecture**: Respect layer separation and existing patterns
4. **Write Tests**: Add comprehensive test coverage
5. **Quality Checks**: Run type checking, formatting, and linting
6. **Update Documentation**: Update relevant memory bank files
7. **Submit PR**: Include clear description and testing instructions

### Code Review Checklist

- [ ] Follows architectural patterns
- [ ] Has complete type hints
- [ ] Includes error handling
- [ ] Uses consistent response formats
- [ ] Has appropriate logging
- [ ] Includes tests
- [ ] Updates documentation

## 📞 Support & Resources

### Getting Help

1. **Check Documentation**: Start with [MANDATORY_READING.md](./MANDATORY_READING.md)
2. **Review Memory Bank**: Check [memory-bank/](./memory-bank/) for context
3. **Quick Reference**: Use [QUICK_REFERENCE.md](./QUICK_REFERENCE.md)
4. **API Docs**: Test endpoints at http://localhost:8000/docs
5. **Contact Team**: Reach out to the development team

### Useful Resources

- **[FastAPI Documentation](https://fastapi.tiangolo.com/)**
- **[SQLModel Documentation](https://sqlmodel.tiangolo.com/)**
- **[Pydantic Documentation](https://docs.pydantic.dev/)**
- **[Alembic Documentation](https://alembic.sqlalchemy.org/)**

## 📄 License

This project is licensed under the MIT License. See the LICENSE file for details.

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Status**: Active Development  
**Next Priority**: Testing Implementation
