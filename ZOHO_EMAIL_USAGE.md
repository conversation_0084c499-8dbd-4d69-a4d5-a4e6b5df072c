# Zoho Email Service Usage Guide

This guide explains how to use the refactored Zoho email functionality for sending IT email creation requests.

## Overview

The refactored email service provides a clean, type-safe, and well-structured way to send professional HTML emails to IT personnel requesting the creation of new talent email accounts in Zoho Mail.

## Features

✅ **Type Safety**: Full type hints with Pydantic models  
✅ **Error Handling**: Comprehensive exception handling with structured logging  
✅ **HTML Templates**: Professional email templates using Jinja2  
✅ **Configuration**: Flexible configuration with environment variables  
✅ **Logging**: Structured logging with context information  
✅ **Testing**: Built-in test functionality  

## Quick Start

### 1. Environment Setup

Set the required environment variables:

```bash
export ZOHO_SENDER_EMAIL="<EMAIL>"
export ZOHO_SENDER_PASSWORD="your_app_specific_password"
```

**Important**: Use an app-specific password for Zoho Mail, not your regular password.

### 2. Basic Usage

```python
import asyncio
from app.emails.zoho_creation_email import send_zoho_creation_request

async def send_email_example():
    """Example of sending an IT email request."""
    success = await send_zoho_creation_request(
        it_person_name="Sa<PERSON>",
        talent_first_name="<PERSON>",
        talent_last_name="Doe",
        talent_email="<EMAIL>",
        it_email="<EMAIL>"  # Target email as requested
    )
    
    if success:
        print("✅ Email sent successfully!")
    else:
        print("❌ Failed to send email")

# Run the example
asyncio.run(send_email_example())
```

### 3. Advanced Usage with CC

```python
import asyncio
from app.emails.zoho_creation_email import send_zoho_creation_request

async def send_email_with_cc():
    """Example with CC recipients."""
    success = await send_zoho_creation_request(
        it_person_name="Sasi",
        talent_first_name="Jane",
        talent_last_name="Smith",
        talent_email="<EMAIL>",
        it_email="<EMAIL>",
        cc_emails=["<EMAIL>", "<EMAIL>"]
    )
    
    return success

asyncio.run(send_email_with_cc())
```

## Service Architecture

### Core Components

1. **ZohoEmailConfig**: Configuration model for SMTP settings
2. **ITEmailRequest**: Data model for email request information
3. **ZohoEmailService**: Main service class for email operations
4. **send_zoho_creation_request**: Convenience function for quick usage

### Class-Based Usage

```python
from app.emails.zoho_creation_email import (
    ZohoEmailService, 
    ZohoEmailConfig, 
    ITEmailRequest
)

# Create configuration
config = ZohoEmailConfig(
    sender_email="<EMAIL>",
    sender_password="your_password",
    sender_name="BPO Admin System"
)

# Create service
email_service = ZohoEmailService(config)

# Create request data
request_data = ITEmailRequest(
    it_person_name="Sasi",
    talent_first_name="John",
    talent_last_name="Doe",
    talent_email="<EMAIL>"
)

# Send email
success = await email_service.send_it_email_request(
    request_data=request_data,
    it_email="<EMAIL>"
)
```

## Email Template

The service uses the HTML template located at:
`/app/emails/templates/zoho_creation_email.html`

The template includes:
- Professional BPO branding
- Dynamic content with Jinja2 variables
- Clear action steps for IT personnel
- Responsive design for mobile devices

### Template Variables

- `{{ it_person_name }}`: Name of the IT person
- `{{ talent_first_name }}`: Talent's first name
- `{{ talent_last_name }}`: Talent's last name
- `{{ talent_email }}`: Requested email address
- `{{ request_date }}`: Date of the request

## Error Handling

The service provides comprehensive error handling:

```python
try:
    success = await send_zoho_creation_request(
        it_person_name="Sasi",
        talent_first_name="John",
        talent_last_name="Doe",
        talent_email="<EMAIL>",
        it_email="<EMAIL>"
    )
except ValueError as e:
    print(f"Configuration error: {e}")
except smtplib.SMTPException as e:
    print(f"Email sending failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Logging

The service uses structured logging with context:

```python
# Logs are automatically generated for:
# - Service initialization
# - Template loading
# - Email sending attempts
# - Errors with full context

# Example log output:
# 2025-01-15 10:30:45 | INFO | ZohoEmailService initialized with SMTP server: smtp.zoho.com
# 2025-01-15 10:30:46 | INFO | Sending IT email request for talent: John Doe (<EMAIL>)
# 2025-01-15 10:30:47 | INFO | Email sent successfully to 1 recipients
```

## Testing

Run the included test script:

```bash
python test_zoho_email.py
```

The test script will:
1. Validate the service configuration
2. Attempt to send a test email
3. Provide detailed feedback on success/failure
4. Show helpful error messages for common issues

## Configuration Options

### SMTP Settings

```python
config = ZohoEmailConfig(
    smtp_server="smtp.zoho.com",      # Zoho SMTP server
    smtp_port=465,                     # SSL port (use 587 for TLS)
    use_ssl=True,                      # Use SSL connection
    sender_email="<EMAIL>",
    sender_password="your_password",
    sender_name="Custom Sender Name"
)
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `ZOHO_SENDER_EMAIL` | Sender email address | Yes |
| `ZOHO_SENDER_PASSWORD` | App-specific password | Yes |

## Security Best Practices

1. **Use App-Specific Passwords**: Never use your main Zoho password
2. **Environment Variables**: Store credentials in environment variables
3. **Secure Storage**: Use secure credential management in production
4. **Access Control**: Limit access to email sending functionality

## Troubleshooting

### Common Issues

1. **Authentication Failed (535)**
   - Verify email and password are correct
   - Ensure you're using an app-specific password
   - Check if 2FA is enabled on your Zoho account

2. **Template Not Found**
   - Verify the template file exists at the correct path
   - Check file permissions

3. **Connection Timeout**
   - Check network connectivity
   - Verify SMTP server and port settings
   - Check firewall settings

### Debug Mode

Enable debug logging for detailed information:

```python
import logging
logging.getLogger('app.emails.zoho_creation_email').setLevel(logging.DEBUG)
```

## Integration Examples

### FastAPI Endpoint

```python
from fastapi import APIRouter, HTTPException
from app.emails.zoho_creation_email import send_zoho_creation_request

router = APIRouter()

@router.post("/send-it-email-request")
async def create_it_email_request(
    it_person_name: str,
    talent_first_name: str,
    talent_last_name: str,
    talent_email: str
):
    try:
        success = await send_zoho_creation_request(
            it_person_name=it_person_name,
            talent_first_name=talent_first_name,
            talent_last_name=talent_last_name,
            talent_email=talent_email,
            it_email="<EMAIL>"
        )
        
        if success:
            return {"message": "Email sent successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to send email")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### Background Task

```python
from fastapi import BackgroundTasks
from app.emails.zoho_creation_email import send_zoho_creation_request

async def send_email_background(
    it_person_name: str,
    talent_first_name: str,
    talent_last_name: str,
    talent_email: str
):
    """Background task for sending emails."""
    await send_zoho_creation_request(
        it_person_name=it_person_name,
        talent_first_name=talent_first_name,
        talent_last_name=talent_last_name,
        talent_email=talent_email,
        it_email="<EMAIL>"
    )

@router.post("/send-email-async")
async def send_email_async(
    background_tasks: BackgroundTasks,
    # ... parameters
):
    background_tasks.add_task(
        send_email_background,
        it_person_name,
        talent_first_name,
        talent_last_name,
        talent_email
    )
    return {"message": "Email queued for sending"}
```

## Comparison with Original Code

### Improvements Made

1. **Type Safety**: Added full type hints and Pydantic models
2. **Error Handling**: Comprehensive exception handling with context
3. **Logging**: Structured logging with detailed context information
4. **Configuration**: Flexible configuration with validation
5. **Template System**: Professional HTML templates with Jinja2
6. **Testing**: Built-in test functionality
7. **Documentation**: Comprehensive documentation and examples
8. **Architecture**: Clean separation of concerns and reusable components

### Migration from Original Code

Old way:
```python
# Original code was tightly coupled and hard to maintain
await _send_email(subject, body, attachment_path, from_address, to_addresses, cc_addresses, is_tbh)
```

New way:
```python
# Clean, type-safe, and well-documented
await send_zoho_creation_request(
    it_person_name="Sasi",
    talent_first_name="John",
    talent_last_name="Doe",
    talent_email="<EMAIL>",
    it_email="<EMAIL>"
)
```

---

**Created**: January 2025  
**Version**: 1.0  
**Target Email**: <EMAIL> (as requested)