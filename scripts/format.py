#!/usr/bin/env python3
"""Simple script to run black formatting."""

import subprocess
import sys
from pathlib import Path


def run_black(target_path: str = "app/") -> bool:
    """Run black formatter on the specified path."""
    print(f"🎨 Formatting {target_path} with black...")
    try:
        result = subprocess.run(
            ["uv", "run", "black", target_path],
            capture_output=True,
            text=True,
            check=True,
        )
        print(f"✅ Black formatting completed: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Black formatting failed: {e.stderr}")
        return False


def main():
    """Main function to run black formatting."""
    target = sys.argv[1] if len(sys.argv) > 1 else "app/"

    print(f"🚀 Running black formatter on: {target}")
    print("=" * 50)

    success = run_black(target)
    print()

    if success:
        print("🎉 Code formatting completed successfully!")
        sys.exit(0)
    else:
        print("💥 Formatting failed. Please review the output above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
