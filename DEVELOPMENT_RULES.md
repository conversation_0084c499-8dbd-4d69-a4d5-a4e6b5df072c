# Development Rules - BPO Admin Backend

## 🚨 MANDATORY READING BEFORE ANY DEVELOPMENT

**CRITICAL**: These rules MUST be followed for all development work. Violations will result in code review rejection.

## 📋 Pre-Development Checklist

### Before Starting ANY Development Task:

1. ✅ **Read Memory Bank Files** (in this order):
   - [Memory Bank README](./memory-bank/README.md)
   - [Project Brief](./memory-bank/projectbrief.md)
   - [Active Context](./memory-bank/activeContext.md)
   - [System Patterns](./memory-bank/systemPatterns.md)

2. ✅ **Understand Current State**:
   - Check [Progress](./memory-bank/progress.md) for implementation status
   - Review [Tech Context](./memory-bank/techContext.md) for technology decisions
   - Understand existing patterns in the codebase

3. ✅ **Plan Your Changes**:
   - Identify which layer(s) you're modifying (API, Service, Repository, Schema)
   - Ensure your changes align with existing architectural patterns
   - Consider impact on related components

## 🏗️ Architectural Rules

### Layer Separation (STRICTLY ENFORCED)

```
┌─────────────────┐
│   API Layer     │  ← FastAPI routers, request/response handling
├─────────────────┤
│  Service Layer  │  ← Business logic, validation, orchestration
├─────────────────┤
│Repository Layer │  ← Data access, database operations
├─────────────────┤
│  Database Layer │  ← SQLModel models, database schema
└─────────────────┘
```

#### API Layer Rules
- **Purpose**: HTTP request/response handling, input validation, authentication
- **Responsibilities**: Route definition, request parsing, response formatting
- **Forbidden**: Direct database access, business logic, data transformation
- **Pattern**: Always call service layer methods, wrap results in GeneralResponse

```python
# ✅ CORRECT
@router.post("/", response_model=GeneralResponse)
async def create_talent(
    request: Request,
    talent_data: TalentCreate,
    service: Annotated[TalentService, Depends()],
) -> GeneralResponse:
    try:
        result = service.create_talent(talent_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Talent created successfully",
            data=result,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(endpoint=request.url.path, method=request.method, status_code=500, error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")

# ❌ WRONG - Direct database access in router
@router.post("/")
async def create_talent(talent_data: TalentCreate, session: Session = Depends(get_session)):
    # This violates layer separation!
    return session.add(TalentProfile(**talent_data.model_dump()))
```

#### Service Layer Rules
- **Purpose**: Business logic, validation, orchestration between repositories
- **Responsibilities**: Data validation, business rules, transaction management
- **Forbidden**: HTTP-specific logic, direct SQL queries
- **Pattern**: Use repository methods, handle business exceptions

```python
# ✅ CORRECT
class TalentService:
    def create_talent(self, talent_data: TalentCreate) -> TalentProfile:
        try:
            # Business validation
            if self.repository.get_talent_by_email(talent_data.email):
                raise HTTPException(status_code=400, detail="Email already exists")
            
            # Convert schema to model
            talent_model = TalentProfile(**talent_data.model_dump())
            
            # Use repository for data access
            return self.repository.create_talent(talent_model)
        except Exception as e:
            log_database_error(operation="CREATE", table="talent_profile", error=e)
            raise HTTPException(status_code=500, detail="Failed to create talent")
```

#### Repository Layer Rules
- **Purpose**: Data access, database operations, query optimization
- **Responsibilities**: CRUD operations, complex queries, data persistence
- **Forbidden**: Business logic, HTTP handling, data validation
- **Pattern**: Return model objects, handle database exceptions

```python
# ✅ CORRECT
class TalentRepository:
    def create_talent(self, talent: TalentProfile) -> TalentProfile:
        try:
            self.session.add(talent)
            self.session.commit()
            self.session.refresh(talent)
            return talent
        except Exception as e:
            self.session.rollback()
            raise e
```

## 💻 Code Quality Rules

### Type Hints (MANDATORY)
```python
# ✅ CORRECT - Full type annotations
def create_talent(self, talent_data: TalentCreate) -> TalentProfile:
    pass

# ❌ WRONG - Missing type hints
def create_talent(self, talent_data):
    pass
```

### Error Handling (MANDATORY)
```python
# ✅ CORRECT - Comprehensive error handling
try:
    result = self.repository.create_talent(talent)
    return result
except HTTPException:
    raise  # Re-raise HTTP exceptions
except Exception as e:
    log_database_error(operation="CREATE", table="talent_profile", error=e)
    raise HTTPException(status_code=500, detail="Failed to create talent")

# ❌ WRONG - No error handling
result = self.repository.create_talent(talent)
return result
```

### Logging (MANDATORY)
```python
# ✅ CORRECT - Structured logging
log_database_error(
    operation="CREATE",
    table="talent_profile",
    error=e,
    additional_context={"talent_id": talent.id}
)

# ❌ WRONG - No logging or print statements
print(f"Error: {e}")
```

### Response Patterns (MANDATORY)
```python
# ✅ CORRECT - Consistent response format
return GeneralResponse(
    status_code=status.HTTP_201_CREATED,
    message="Talent created successfully",
    data=result,
)

# ❌ WRONG - Inconsistent response
return {"success": True, "talent": result}
```

## 🗄️ Database Rules

### Model Definitions
- Use SQLModel for all database models
- Include proper field validation and constraints
- Add meaningful docstrings
- Use appropriate field types and relationships

### Migration Rules
- Always create migrations for schema changes
- Test migrations on development database first
- Include rollback procedures
- Document migration purpose

### Query Optimization
- Use appropriate indexes
- Avoid N+1 query problems
- Use eager loading for related data
- Monitor query performance

## 🔐 Security Rules

### Authentication
- All endpoints require authentication unless explicitly public
- Use JWT tokens for session management
- Implement proper token validation
- Handle authentication errors gracefully

### Data Protection
- Never log sensitive data (passwords, tokens, PII)
- Validate all input data
- Use parameterized queries
- Implement proper access controls

### Error Messages
- Don't expose internal system details
- Use generic error messages for security-sensitive operations
- Log detailed errors internally only

## 📝 Documentation Rules

### Code Documentation
- All public methods must have docstrings
- Include parameter descriptions and return types
- Document complex business logic
- Keep comments up-to-date

### API Documentation
- Use FastAPI automatic documentation
- Provide meaningful endpoint descriptions
- Include example requests/responses
- Document error scenarios

## 🧪 Testing Rules

### Test Coverage
- Unit tests for all service methods
- Integration tests for API endpoints
- Repository tests for data access
- Mock external dependencies

### Test Structure
- Follow AAA pattern (Arrange, Act, Assert)
- Use descriptive test names
- Test both success and failure scenarios
- Keep tests independent

## 🚀 Performance Rules

### Database Performance
- Use database indexes appropriately
- Implement pagination for large datasets
- Avoid unnecessary database calls
- Use connection pooling

### API Performance
- Implement request/response caching where appropriate
- Use async/await for I/O operations
- Monitor endpoint response times
- Implement rate limiting

## 📦 Dependency Management

### Package Management
- Use `uv` for all dependency management
- Pin dependency versions in production
- Regularly update dependencies
- Document dependency choices

### Import Organization (MANDATORY)

#### Standard Import Order
```python
# ✅ CORRECT - Three-section import organization
# 1. Standard library imports
from typing import Annotated, List, Optional, Dict, Any

# 2. Third-party imports  
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlmodel import Session, select

# 3. Local application imports (grouped by layer)
from app.core import log_api_error, log_error_with_context, verify_password
from app.core.jwt import create_access_token, get_current_user
from app.db import User, TalentProfile, get_session
from app.repositories import UserRepository
from app.repositories.talent import BankingRepository
from app.schemas.talent import TalentCreate, TalentUpdate
from app.services.talent import TalentService
from app.response_models.general_response import GeneralResponse
```

#### Layer-Specific Import Patterns

**Repository Layer**:
```python
# Minimal imports focused on data access
from sqlmodel import Session, select
from app.db import User  # Direct model imports
from app.schemas import UserCreate  # Schema imports for type safety
```

**Service Layer**:
```python
# Business logic layer imports
from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context
from app.db import User
from app.repositories.talent.banking_repository import BankingRepository
from app.schemas.talent.banking_schema import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
    TalentBankingInformationResponse
)
```

**API Router Layer**:
```python
# API layer imports with comprehensive error handling
from typing import Annotated, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import TalentBankingInformationCreate, TalentBankingInformationUpdate
from app.services.talent import BankingService
```

#### Dependency Injection Pattern
```python
# ✅ CORRECT - Consistent dependency injection using Annotated
__service = Annotated[BankingService, Depends()]
__auth_service_init = Annotated[AuthService, Depends()]

# Usage in endpoints
async def create_banking_information(
    request: Request,
    banking_data: TalentBankingInformationCreate,
    service: __service,
) -> GeneralResponse:
```

#### Import Violations to Avoid
```python
# ❌ WRONG - Mixed import sections
from app.core import log_api_error
from typing import List
from fastapi import APIRouter
from app.db import User

# ❌ WRONG - Inconsistent dependency injection
service: BankingService = Depends()

# ❌ WRONG - Missing type annotations in imports
from app.schemas.talent import *
```

## 🔄 Git Workflow Rules

### Commit Messages
- Use conventional commit format
- Include clear, descriptive messages
- Reference issue numbers when applicable
- Keep commits atomic and focused

### Branch Strategy
- Use feature branches for new development
- Keep branches up-to-date with main
- Use descriptive branch names
- Delete merged branches

## 🚨 Code Review Rules

### Before Submitting PR
- Run type checking: `uv run basedpyright`
- Run tests: `uv run pytest`
- Check code formatting: `uv run black .`
- Verify all rules are followed

### Review Checklist
- Architectural patterns followed
- Error handling implemented
- Type hints present
- Tests included
- Documentation updated

## ⚠️ Common Violations to Avoid

1. **Layer Mixing**: Don't put business logic in routers or database queries in services
2. **Missing Error Handling**: Every external call must be wrapped in try/catch
3. **Inconsistent Responses**: Always use GeneralResponse for API responses
4. **Missing Type Hints**: All function parameters and returns must be typed
5. **Poor Logging**: Use structured logging, not print statements
6. **Security Gaps**: Never expose sensitive data or skip authentication
7. **Performance Issues**: Don't ignore database optimization or caching

---

**Remember**: These rules exist to maintain code quality, security, and maintainability. When in doubt, ask for clarification rather than guessing.

**Last Updated**: January 2025
**Enforcement**: Mandatory for all development work