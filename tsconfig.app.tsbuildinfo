{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/apis/auth/auth-apis.ts", "./src/apis/history-log/history-log.ts", "./src/apis/history-log/index.ts", "./src/apis/master/clients.ts", "./src/apis/master/equipment.ts", "./src/apis/roles/role.ts", "./src/apis/settings/settings.ts", "./src/apis/talent/banking.ts", "./src/apis/talent/bpo-related-info.ts", "./src/apis/talent/documents.ts", "./src/apis/talent/emergency-contact.ts", "./src/apis/talent/health.ts", "./src/apis/talent/it-documents.ts", "./src/apis/talent/job-related-info.ts", "./src/apis/talent/profile.ts", "./src/apis/talent/security.ts", "./src/apis/talent/skills.ts", "./src/apis/talent/third-party-integration.ts", "./src/apis/users/users.ts", "./src/composables/usebanking.ts", "./src/composables/auth/uselogin.ts", "./src/composables/clients/useclient.ts", "./src/composables/common/usepagination.ts", "./src/composables/equipment/useequipment.ts", "./src/composables/equipment/useequipmentlist.ts", "./src/composables/history-log/usehistorylog.ts", "./src/composables/permissions/usepermissions.ts", "./src/composables/roles/userole.ts", "./src/composables/settings/usesettings.ts", "./src/composables/talent/usebporelated.ts", "./src/composables/talent/usedocuments.ts", "./src/composables/talent/usehealth.ts", "./src/composables/talent/useitdocuments.ts", "./src/composables/talent/usejobrelatedinfo.ts", "./src/composables/talent/usephoneinput.ts", "./src/composables/talent/useskillsmanager.ts", "./src/composables/talent/usetalentprofile.ts", "./src/composables/talent/usetalenttable.ts", "./src/composables/talent/usethirdpartyintegrations.ts", "./src/composables/users/useuser.ts", "./src/constants/work-history-options.ts", "./src/directives/permissions.ts", "./src/plugins/permissions.ts", "./src/router/index.ts", "./src/services/work-history.service.ts", "./src/store/auth/useauthstore.ts", "./src/store/employee/usetalentstore.ts", "./src/store/permissions/usepermissionsstore.ts", "./src/store/talent/usebankingstore.ts", "./src/store/talent/usetalentprofilestore.ts", "./src/types/banking.ts", "./src/types/bpo-related-info.ts", "./src/types/documents.ts", "./src/types/equipment.ts", "./src/types/health.ts", "./src/types/history-log.ts", "./src/types/job-related-info.ts", "./src/types/permissions.ts", "./src/types/roles.ts", "./src/types/settings.ts", "./src/types/talent.ts", "./src/types/third-party-integration.ts", "./src/types/user.ts", "./src/types/master/client.ts", "./src/utils/case-converter.ts", "./src/utils/date-formatting.ts", "./src/utils/date-util.ts", "./src/utils/http-handler.ts", "./src/utils/image-background.ts", "./src/utils/validation/work-history-validation.ts", "./src/app.vue", "./src/components/calendar.vue", "./src/components/helloworld.vue", "./src/components/sidebar.vue", "./src/components/statusmodal.vue", "./src/components/talenttable.vue", "./src/components/examples/permissionexample.vue", "./src/components/modals/fileviewermodal.vue", "./src/components/tabs/bporelatedinfo.vue", "./src/components/tabs/bankinginfo.vue", "./src/components/tabs/documents.vue", "./src/components/tabs/equipmentandsoftware.vue", "./src/components/tabs/health.vue", "./src/components/tabs/historylog.vue", "./src/components/tabs/talentprofile.vue", "./src/components/tabs/thirdpartyintegrations.vue", "./src/components/tabs/workhistory.vue", "./src/views/dashboardview.vue", "./src/views/historylogview.vue", "./src/views/loginpage.vue", "./src/views/notfoundview.vue", "./src/views/placeholderview.vue", "./src/views/settingsview.vue", "./src/views/clients/addeditclientview.vue", "./src/views/clients/clientslistview.vue", "./src/views/equipment/addeditequipmentview.vue", "./src/views/equipment/equipmentlistview.vue", "./src/views/errors/unauthorizedpage.vue", "./src/views/roles/addroleview.vue", "./src/views/roles/roleslistview.vue", "./src/views/roles/toggleswitch.vue", "./src/views/talent/addtalentpage.vue", "./src/views/talent/talentlistview.vue", "./src/views/users/adduserview.vue", "./src/views/users/userslistview.vue"], "errors": true, "version": "5.8.3"}