# Active Context - BPO Admin Backend

## Current Project State

### What's Working

- **Core Application Structure**: FastAPI app with proper lifespan management
- **Database Models**: Comprehensive talent management schema with 10+ interconnected tables
- **Configuration System**: Environment-based settings with Pydantic
- **Development Environment**: uv package management with proper dependencies
- **Logging Infrastructure**: Loguru integration with FastAPI
- **Authentication Foundation**: Basic user model with admin/regular user roles

### Database Schema Status

**✅ Implemented Models:**

1. **User** - System authentication and user management
2. **TalentProfile** - Core talent information hub
3. **TalentSkillSetMapping** - Skills, experience, interview notes, English levels
4. **TalentPayrollInformationMapping** - Pay rates, frequency, tax IDs (IMMS, CURP, RFC)
5. **TalentBankingInformationMapping** - Banking details, CLABE, SWIFT codes
6. **TalentEmergencyContactMapping** - Emergency contact information
7. **TalentPositionMapping** - Position history, client assignments, contract details
8. **TalentVacationMapping** - Leave balances (vacation, bereavement, parental)
9. **TalentEquipmentMapping** - Equipment assignment tracking
10. **TalentSoftwareMapping** - Software licensing and distribution

## Current Development Status

### Recently Completed
- ✅ **Authentication System**: JWT-based authentication with role management
- ✅ **Talent Profile Management**: Complete CRUD operations for talent profiles
- ✅ **Banking Information**: Talent banking details management
- ✅ **Wage Management**: Wage information and history tracking
- ✅ **Profile Router Refactoring**: Improved error handling and response patterns
- ✅ **Banking Router Refactoring**: Enhanced service layer integration
- ✅ **Wage Router Refactoring**: Fixed type errors and parameter mismatches, eliminated `_with_response` methods
- ✅ **Project Documentation**: Created comprehensive documentation structure and development rules
- ✅ **Development Rules**: Established mandatory coding standards and architectural patterns
- ✅ **Documentation Framework**: Created memory bank system and mandatory reading requirements
- ✅ **Import Standards**: Documented three-section import organization patterns for all layers
- ✅ **Code Quality Patterns**: Established layer-specific import patterns and dependency injection standards
- ✅ **Position Router Implementation**: Complete CRUD operations with standardized error handling
- ✅ **Skill Set Router Implementation**: Complete CRUD operations aligned with client_info pattern
- ✅ **Service Layer Standardization**: Current user injection and consistent error logging patterns
- ✅ **API Pattern Establishment**: Standardized router structure with Request objects and log_api_error
- ✅ **Dependency Injection Patterns**: Annotated dependencies with proper type hints across all layers

### Currently Working On
- ✅ **Documentation Compliance**: Updated memory banks and documentation with current coding standards
- ✅ **Code Quality Improvements**: Standardized response patterns across all routers
- ✅ **Error Handling Enhancement**: Implemented comprehensive logging and error management
- ✅ **Type Safety**: Added complete type hints across the codebase
- ✅ **Structure Maintenance Guide**: Created comprehensive guide for maintaining established patterns
- 🚧 **Repository Layer Completion**: Implementing remaining repository classes
- 🚧 **Schema Standardization**: Ensuring all schemas follow established patterns

### Immediate Priorities
1. **Documentation Compliance**: Ensure all developers read mandatory documentation before development
2. **Complete Router Refactoring**: Ensure all routers follow the same patterns
3. **Implement Comprehensive Logging**: Add structured logging across all layers
4. **Add Input Validation**: Enhance data validation and sanitization
5. **Performance Optimization**: Database query optimization and caching
6. **Security Hardening**: Enhanced authentication and authorization

## Current Focus Areas

### API Layer Development

   - Create FastAPI routes for talent management operations
   - Implement CRUD operations for all talent-related entities
   - Add proper request/response schemas with Pydantic

### Repository Layer Implementation

   - Build data access layer for talent operations
   - Implement query patterns for complex talent data retrieval
   - Add proper error handling and transaction management

### Service Layer Architecture
   - Business logic for talent lifecycle management
   - Validation rules for talent data integrity
   - Workflow orchestration for onboarding/offboarding

### Key Technical Decisions Made

#### Database Design Patterns

- **Central Hub Pattern**: TalentProfile as the core entity with related mappings
- **Cascade Deletion**: Foreign key relationships with CASCADE delete for data integrity
- **Temporal Tracking**: created_at/updated_at on all entities for audit trails
- **Flexible Storage**: JSON columns for variable-length arrays (skills, English levels)
- **Mexican Compliance**: Specialized fields for IMMS, CURP, RFC tax requirements

#### Architecture Choices

- **Layered Architecture**: Clear separation between API, Service, Repository, and Model layers
- **Type Safety**: SQLModel for database operations with full type annotations
- **Async Patterns**: FastAPI with async/await for scalable operations
- **Configuration Management**: Environment-based settings for deployment flexibility

## Recent Insights & Patterns

### Data Modeling Insights

- **Normalization Strategy**: Separate tables for different aspects of talent data (skills, payroll, banking, etc.)
- **Relationship Management**: One-to-many relationships from TalentProfile to specialized mappings
- **Compliance Requirements**: Built-in support for Mexican tax and banking regulations
- **Resource Tracking**: Equipment and software assignment capabilities

### Development Workflow Patterns

- **Package Management**: uv for fast dependency resolution and virtual environment management
- **Code Quality**: Black formatting with basedpyright type checking
- **Database Migrations**: Alembic for version-controlled schema changes
- **Logging Strategy**: Structured logging with application lifecycle tracking

## Next Steps & Priorities

### Phase 1: Core API Development

1. **Talent Profile Management**

   - Create/Read/Update/Delete operations for talent profiles
   - Bulk operations for talent data import/export
   - Search and filtering capabilities

2. **Skills & Experience Management**

   - Skills assessment and tracking endpoints
   - Experience level management
   - Interview notes and evaluation tracking

3. **Position & Client Management**
   - Position assignment and history tracking
   - Client placement management
   - Contract period and renewal workflows

### Phase 2: Advanced Features

1. **Leave Management System**

   - Vacation balance tracking and calculations
   - Leave request and approval workflows
   - Year-over-year balance management

2. **Resource Management**

   - Equipment assignment and tracking
   - Software licensing and distribution
   - Asset lifecycle management

3. **Payroll Integration**
   - Payroll information management
   - Banking details and compliance
   - Tax identifier tracking

### Phase 3: Integration & Enhancement

1. **Authentication & Authorization**

   - JWT token implementation
   - Role-based access control
   - Permission management system

2. **Reporting & Analytics**

   - Talent status dashboards
   - Compliance reporting
   - Resource utilization analytics

3. **External Integrations**
   - Payroll system integration
   - Time tracking system connectivity
   - Client management system integration

## Important Considerations

### Data Security & Compliance

- **Sensitive Data Handling**: Personal, financial, and employment data requires careful access control
- **Regulatory Compliance**: GDPR, CCPA, and Mexican data protection requirements
- **Audit Trails**: Complete logging of data access and modifications

### Performance & Scalability

- **Database Optimization**: Proper indexing strategy for talent queries
- **API Performance**: Efficient query patterns for complex talent data retrieval
- **Caching Strategy**: Consider caching for frequently accessed talent information

### User Experience Goals

- **Admin Efficiency**: Streamlined workflows for talent management operations
- **Data Accuracy**: Validation rules to ensure data integrity
- **Process Automation**: Reduce manual data entry and processing overhead

## Development Environment Notes

### Current Setup

- **Python 3.13+**: Latest stable version with modern features
- **uv Package Manager**: Fast dependency resolution and virtual environment management
- **MySQL Database**: Local development instance with bpo_admin database
- **FastAPI Development Server**: `uv run python -m app.main` for local testing
- **Code Quality Tools**: Black formatting + basedpyright type checking

### Key Files & Locations

- **Main Application**: `app/main.py` - FastAPI app with lifespan management
- **Database Models**: `app/db/init_db.py` - All SQLModel definitions
- **Configuration**: `app/core/config.py` - Environment-based settings
- **Environment Variables**: `.env` file with DATABASE_URL and APP_PORT
- **Dependencies**: `pyproject.toml` with production and dev dependencies

### Development Commands

- **Install Dependencies**: `uv sync`
- **Run Server**: `uv run python -m app.main`
- **Type Checking**: `uv run basedpyright`
- **Code Formatting**: `uv run black .`
- **Database Migrations**: `uv run alembic upgrade head`

## Memory Bank Status

This Memory Bank initialization captures the current state of the BPO Admin backend project as of January 2025. The system has a solid foundation with comprehensive database models and is ready for API layer development.

**Key Strengths:**

- Well-designed normalized database schema
- Comprehensive talent management data model
- Modern Python tech stack with type safety
- Clear architectural patterns and separation of concerns

**Immediate Next Steps:**

- Implement FastAPI routes for talent management
- Build repository layer for data access
- Create service layer for business logic
- Add proper request/response schemas

The Memory Bank will be updated as development progresses and new patterns emerge.
