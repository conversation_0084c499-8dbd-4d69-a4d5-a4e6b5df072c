# System Patterns - BPO Admin Backend

## Architecture Overview

The BPO Admin backend follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────┐
│              API Layer                  │
│         (FastAPI Routes)                │
├─────────────────────────────────────────┤
│            Service Layer                │
│        (Business Logic)                 │
├─────────────────────────────────────────┤
│          Repository Layer               │
│        (Data Access)                    │
├─────────────────────────────────────────┤
│            Model Layer                  │
│      (SQLModel/Database)                │
└─────────────────────────────────────────┘
```

## Core Technical Patterns

### 1. Database Design Pattern

**Normalized Relational Schema** with clear entity relationships:

#### Primary Entities

- **User**: System users (admins, HR managers)
- **TalentProfile**: Core talent information hub
- **Related Mappings**: Specialized data linked to talent profiles

#### Entity Relationship Pattern

```
TalentProfile (1) ←→ (N) TalentSkillSetMapping
TalentProfile (1) ←→ (N) TalentPayrollInformationMapping
TalentProfile (1) ←→ (N) TalentBankingInformationMapping
TalentProfile (1) ←→ (N) TalentEmergencyContactMapping
TalentProfile (1) ←→ (N) TalentPositionMapping
TalentProfile (1) ←→ (N) TalentVacationMapping
TalentProfile (1) ←→ (N) TalentEquipmentMapping
TalentProfile (1) ←→ (N) TalentSoftwareMapping
```

### 2. Data Model Patterns

#### SQLModel Integration Pattern

```python
class BaseModel(SQLModel):
    id: int = Field(primary_key=True, sa_type=BigInteger)
    created_at: datetime = Field(sa_column=Column(DateTime, default=func.now))
    updated_at: datetime = Field(sa_column=Column(DateTime, default=func.now, onupdate=func.now))
```

#### Foreign Key Cascade Pattern

```python
talent_profile_id: int = Field(
    foreign_key="talent_profiles.id",
    ondelete="CASCADE",
    sa_type=BigInteger
)
```

#### JSON Field Pattern for Arrays

```python
skills: list[str] = Field(
    sa_column=Column(MutableList.as_mutable(JSON))
)
```

### 3. Application Structure Pattern

#### Directory Organization

```
app/
├── main.py              # Application entry point & lifespan management
├── api/v1/              # API routes (versioned)
├── core/                # Cross-cutting concerns
│   ├── config.py        # Environment configuration
│   ├── logs.py          # Logging setup
│   └── security.py      # Authentication & security
├── db/                  # Database layer
│   ├── init_db.py       # Models & database initialization
│   └── session.py       # Database session management
├── repositories/        # Data access layer
├── schemas/             # Pydantic request/response models
└── services/            # Business logic layer
```

### 4. Configuration Pattern

#### Environment-based Configuration

```python
class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
    database_url: str = ""
    app_port: str = "8000"
```

### 5. Database Initialization Pattern

#### Lifespan Management

```python
@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info("Starting application initialization")
    try:
        init_db()  # Create all tables
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise
    yield
    logger.info("Application shutdown complete")
```

## Data Modeling Decisions

### 1. Talent Profile as Central Hub

- **Pattern**: Single source of truth with related mappings
- **Rationale**: Maintains data integrity while allowing specialized information
- **Implementation**: Foreign key relationships with CASCADE delete

### 2. Temporal Data Tracking

- **Pattern**: created_at/updated_at on all entities
- **Rationale**: Audit trail and change tracking
- **Implementation**: Automatic timestamp management via SQLAlchemy

### 3. Flexible Data Storage

- **Pattern**: JSON columns for array/list data
- **Rationale**: Accommodate variable-length lists (skills, english levels)
- **Implementation**: MutableList with JSON column type

### 4. Mexican Tax Compliance

- **Pattern**: Specialized fields for local requirements
- **Rationale**: Support IMMS, CURP, RFC tax identifiers
- **Implementation**: Optional string fields with validation

## Security Patterns

### 1. Authentication Architecture

- **Current**: Basic user model with password hashing
- **Pattern**: Role-based access (is_superuser flag)
- **Future**: JWT tokens, role-based permissions

### 2. Data Protection

- **Pattern**: Sensitive data isolation
- **Implementation**: Separate tables for financial/personal data
- **Security**: Field-level access control potential

## Performance Patterns

### 1. Database Optimization

- **Indexing Strategy**: Primary keys on BigInteger for scalability
- **Query Optimization**: Foreign key relationships for efficient joins
- **Connection Management**: SQLModel engine with connection pooling

### 2. API Design

- **RESTful Pattern**: Resource-based URL structure
- **Versioning**: /api/v1/ prefix for future compatibility
- **Documentation**: Auto-generated OpenAPI/Swagger docs

## Development Patterns

### 1. Import Organization Patterns

#### Standard Import Order
```python
# 1. Standard library imports
from typing import Annotated, List, Optional, Dict, Any

# 2. Third-party imports
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlmodel import Session, select

# 3. Local application imports (grouped by layer)
from app.core import log_api_error, log_error_with_context, verify_password
from app.core.jwt import create_access_token, get_current_user
from app.db import User, get_session
from app.repositories import UserRepository
from app.repositories.talent import BankingRepository
from app.schemas.login_schema import LoginSchema
from app.schemas.talent import TalentBankingInformationCreate, TalentBankingInformationUpdate
from app.services import AuthService
from app.services.talent import BankingService
from app.response_models.general_response import GeneralResponse
```

### 2. API Router Patterns

#### Standard Router Structure
```python
"""Module docstring describing the router purpose."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import CreateSchema, UpdateSchema
from app.services.talent.service_name import ServiceClass

router = APIRouter(prefix="/endpoint")
service_dependency = Annotated[ServiceClass, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_resource(
    request: Request,
    resource_data: CreateSchema,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new resource.
    
    Args:
        request: FastAPI request object
        resource_data: The resource data to create
        service: Injected service
        
    Returns:
        GeneralResponse with created resource data
    """
    try:
        result = service.create_resource(resource_data)
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create resource"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Resource created successfully",
            data=result.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
```

### 3. Service Layer Patterns

#### Standard Service Structure
```python
"""Service module docstring describing business logic."""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.repository_name import RepositoryClass
from app.schemas.talent.schema_name import CreateSchema, UpdateSchema, ResponseSchema
from app.core.logs import log_error_with_context

class ServiceClass:
    """Service class for business logic operations."""
    
    def __init__(
        self, 
        repository: Annotated[RepositoryClass, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_resource(self, resource_data: CreateSchema) -> Optional[ResponseSchema]:
        """Create a new resource.
        
        Args:
            resource_data: Resource data to create
            
        Returns:
            Created resource response or None if creation fails
        """
        try:
            # Business validation
            if not self._validate_resource_data(resource_data):
                return None
            
            db_resource = self.repository.create(resource_data)
            if db_resource:
                return ResponseSchema.model_validate(db_resource)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_resource",
                    "service": "ServiceClass",
                    "data": resource_data.model_dump()
                }
            )
            return None
```

### 4. Error Handling Patterns

#### API Layer Error Handling
```python
try:
    result = service.method(data)
    return GeneralResponse(
        status_code=status.HTTP_200_OK,
        message="Operation successful",
        data=result
    )
except HTTPException:
    raise  # Re-raise HTTP exceptions
except Exception as e:
    log_api_error(
        endpoint=request.url.path,
        method=request.method,
        status_code=500,
        error=str(e),
        user_id=str(service.current_user.id),
    )
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Internal server error",
    )
```

#### Service Layer Error Handling
```python
try:
    # Business logic
    result = self.repository.operation(data)
    return ResponseSchema.model_validate(result)
except Exception as e:
    log_error_with_context(
        error=e,
        context={
            "operation": "operation_name",
            "service": "ServiceClass",
            "data": data.model_dump() if hasattr(data, 'model_dump') else str(data)
        }
    )
    return None
```

#### Repository Layer Import Pattern
```python
# Minimal imports focused on data access
from sqlmodel import Session, select
from app.db import User  # Direct model imports
from app.schemas import UserCreate  # Schema imports for type safety
```

#### Service Layer Import Pattern
```python
# Business logic layer imports
from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context
from app.db import User
from app.repositories.talent.banking_repository import BankingRepository
from app.schemas.talent.banking_schema import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
    TalentBankingInformationResponse
)
```

#### API Router Import Pattern
```python
# API layer imports with comprehensive error handling
from typing import Annotated, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import TalentBankingInformationCreate, TalentBankingInformationUpdate
from app.services.talent import BankingService
```

#### Dependency Injection Pattern
```python
# Consistent dependency injection using Annotated
__service = Annotated[BankingService, Depends()]
__auth_service_init = Annotated[AuthService, Depends()]

# Usage in endpoints
async def create_banking_information(
    request: Request,
    banking_data: TalentBankingInformationCreate,
    service: __service,
) -> GeneralResponse:
```

### 2. Package Management

- **Tool**: uv for fast dependency resolution
- **Pattern**: pyproject.toml for modern Python packaging
- **Environment**: Virtual environment isolation

### 3. Code Quality

- **Formatting**: Black for consistent code style
- **Type Checking**: basedpyright for static analysis
- **Linting**: Integrated development workflow

### 4. Database Migrations

- **Tool**: Alembic for schema versioning
- **Pattern**: Automatic migration generation
- **Deployment**: Version-controlled schema changes

## Integration Patterns

### 1. API-First Design

- **Pattern**: FastAPI with automatic documentation
- **Rationale**: Enable frontend and third-party integrations
- **Implementation**: Pydantic schemas for request/response validation

### 2. Logging & Monitoring

- **Pattern**: Structured logging with Loguru
- **Implementation**: Application lifecycle logging
- **Future**: Metrics and health check endpoints

## Scalability Considerations

### 1. Database Design

- **Normalization**: Reduces data redundancy
- **Relationships**: Efficient foreign key constraints
- **Indexing**: Primary key optimization for large datasets

### 2. Application Architecture

- **Separation of Concerns**: Clear layer boundaries
- **Stateless Design**: FastAPI application for horizontal scaling
- **Configuration**: Environment-based for deployment flexibility
