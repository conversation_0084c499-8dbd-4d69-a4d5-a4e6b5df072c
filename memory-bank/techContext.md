# Technical Context - BPO Admin Backend

## Technology Stack

### Core Framework & Runtime

- **Python**: 3.13+ (Latest stable version)
- **FastAPI**: 0.115.14+ (Modern async web framework)
- **Uvicorn**: ASGI server for production deployment
- **SQLModel**: Type-safe ORM combining SQLAlchemy + Pydantic

### Database & Persistence

- **MySQL**: Primary database with mysql-connector-python driver
- **Alembic**: Database migration management
- **SQLAlchemy**: Underlying ORM engine with advanced features

### Development Tools

- **uv**: Fast Python package manager and dependency resolver
- **basedpyright**: Static type checking (Pylance-based)
- **Black**: Code formatting with 88-character line length
- **Loguru**: Structured logging with better developer experience

### Configuration & Environment

- **Pydantic Settings**: Environment-based configuration management
- **python-dotenv**: Environment variable loading from .env files
- **Email Validator**: Email validation utilities

## Development Environment

### Package Management Strategy

```toml
[project]
name = "bpo-admin"
version = "0.1.0"
requires-python = ">=3.13"

[dependency-groups]
dev = [
    "basedpyright>=1.0.0",
    "black>=24.0.0",
]
```

### Key Dependencies

- **Production Dependencies**:

  - `fastapi>=0.115.14` - Web framework
  - `sqlmodel>=0.0.24` - Database ORM
  - `mysql-connector-python>=9.3.0` - MySQL driver
  - `alembic>=1.16.2` - Database migrations
  - `pydantic-settings>=2.10.1` - Configuration management
  - `loguru>=0.7.3` - Logging
  - `email-validator>=2.0.0` - Email validation
  - `uvicorn>=0.35.0` - ASGI server

- **Development Dependencies**:
  - `basedpyright>=1.0.0` - Type checking
  - `black>=24.0.0` - Code formatting

### Environment Configuration

```python
# .env file structure
DATABASE_URL=mysql+mysqlconnector://username:password@localhost:3306/bpo_admin
APP_PORT=8000
```

## Database Architecture

### Connection Management

- **Engine**: SQLModel create_engine with MySQL connector
- **Connection Pooling**: Built-in SQLAlchemy connection pooling
- **Echo Mode**: SQL query logging enabled for development

### Schema Management

- **Migration Tool**: Alembic for version-controlled schema changes
- **Auto-generation**: Automatic migration creation from model changes
- **Deployment**: Structured migration workflow

### Data Types & Patterns

- **Primary Keys**: BigInteger for scalability
- **Timestamps**: Automatic created_at/updated_at tracking
- **JSON Fields**: MutableList for array storage
- **Foreign Keys**: CASCADE delete for data integrity

## Application Architecture

### FastAPI Configuration

```python
app = FastAPI(
    lifespan=lifespan,           # Application lifecycle management
    root_path="/api/v1",         # API versioning
    title="BPO Admin",           # OpenAPI documentation
    version="1.0.0",
)
```

### Async Patterns

- **Lifespan Management**: Async context manager for startup/shutdown
- **Database Initialization**: Automatic table creation on startup
- **Error Handling**: Structured exception handling with logging

### Logging Strategy

- **Framework**: Loguru for structured logging
- **Integration**: FastAPI logging configuration
- **Levels**: Info, Error, Debug with contextual information
- **Output**: Console logging with structured format

## Development Workflow

### Local Development Setup

1. **Environment**: Python 3.13+ with uv package manager
2. **Dependencies**: `uv sync` for dependency installation
3. **Database**: Local MySQL instance with development database
4. **Server**: `uv run python -m app.main` for development server

### Code Quality Standards

- **Formatting**: Black with 88-character line length
- **Type Checking**: basedpyright for static analysis
- **Import Organization**: Three-section import structure (standard library, third-party, local)
- **Line Endings**: Consistent across development environments
- **Dependency Injection**: Annotated pattern with FastAPI Depends
- **Type Annotations**: Mandatory for all function parameters and returns

#### Import Organization Standards

**Three-Section Structure**:
```python
# 1. Standard library imports
from typing import Annotated, List, Optional, Dict, Any

# 2. Third-party imports
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlmodel import Session, select

# 3. Local application imports (grouped by layer)
from app.core import log_api_error, log_error_with_context
from app.db import User, get_session
from app.repositories import UserRepository
from app.schemas.talent import TalentCreate
from app.services.talent import TalentService
```

**Layer-Specific Patterns**:
- **Repository Layer**: Minimal imports (SQLModel, models, schemas)
- **Service Layer**: Business logic imports (repositories, schemas, core utilities)
- **API Layer**: HTTP handling imports (FastAPI, response models, services)

**Dependency Injection Pattern**:
```python
# Consistent Annotated pattern
__service = Annotated[BankingService, Depends()]

# Usage in endpoints
async def endpoint(
    service: __service,
) -> GeneralResponse:
```

### Database Development

- **Migrations**: `uv run alembic revision --autogenerate`
- **Schema Updates**: `uv run alembic upgrade head`
- **Rollbacks**: `uv run alembic downgrade -1`

## Security Considerations

### Authentication Architecture

- **Current**: Basic user authentication with password hashing
- **Session Management**: Stateless design for scalability
- **Role-based Access**: is_superuser flag for admin privileges

### Data Protection

- **Sensitive Data**: Isolated in separate database tables
- **Connection Security**: Encrypted database connections
- **Environment Variables**: Secure configuration management

## Performance Characteristics

### Database Performance

- **Indexing**: Primary key optimization with BigInteger
- **Query Efficiency**: Foreign key relationships for joins
- **Connection Pooling**: SQLAlchemy connection management
- **Query Logging**: Development-time SQL query visibility

### Application Performance

- **Async Framework**: FastAPI with async/await support
- **Stateless Design**: Horizontal scaling capability
- **Memory Management**: Efficient SQLModel object handling

## Deployment Considerations

### Production Requirements

- **Python Runtime**: 3.13+ with production ASGI server
- **Database**: MySQL 8.0+ with proper configuration
- **Environment**: Secure environment variable management
- **Logging**: Structured logging for monitoring

### Scalability Patterns

- **Horizontal Scaling**: Stateless application design
- **Database Scaling**: Normalized schema for efficient queries
- **Configuration**: Environment-based deployment flexibility

## Integration Capabilities

### API Design

- **OpenAPI**: Automatic documentation generation
- **Versioning**: /api/v1/ prefix for backward compatibility
- **Standards**: RESTful resource-based URL structure

### Future Integration Points

- **Authentication**: JWT token support
- **Monitoring**: Health check endpoints
- **Metrics**: Application performance monitoring
- **External APIs**: Third-party service integration

## Development Tools Integration

### IDE Support

- **Type Hints**: Full type annotation support
- **IntelliSense**: Enhanced code completion with basedpyright
- **Debugging**: Python debugging support with FastAPI

### Version Control

- **Git Integration**: Standard Git workflow
- **Pre-commit Hooks**: Code quality enforcement
- **Branch Strategy**: Feature branch development

## Constraints & Limitations

### Technical Constraints

- **Python Version**: Requires 3.13+ for latest features
- **Database**: MySQL-specific features and limitations
- **Memory**: JSON field storage considerations
- **Performance**: ORM overhead for complex queries

### Development Constraints

- **Dependencies**: uv package manager requirement
- **Environment**: Local MySQL setup for development
- **Migration**: Alembic learning curve for schema changes
- **Type Safety**: basedpyright configuration complexity
