/**
 * Convert camelCase string to snake_case
 */
export const camelToSnake = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * Convert snake_case string to camelCase
 */
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * Convert object keys from camelCase to snake_case
 */
export const convertKeysToSnakeCase = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map(convertKeysToSnakeCase)
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: any = {}
    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = camelToSnake(key)
      converted[snakeKey] = convertKeysToSnakeCase(value)
    }
    return converted
  }
  
  return obj
}

/**
 * Convert object keys from snake_case to camelCase
 */
export const convertKeysToCamelCase = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map(convertKeysToCamelCase)
  }
  
  if (typeof obj === 'object' && obj.constructor === Object) {
    const converted: any = {}
    for (const [key, value] of Object.entries(obj)) {
      const camelKey = snakeToCamel(key)
      converted[camelKey] = convertKeysToCamelCase(value)
    }
    return converted
  }
  
  return obj
}