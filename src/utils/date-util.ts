export const monthYearFormat = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric'
  })
}

export const datePickerFormatter = (date: Date) => {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();

  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;

  return `${formattedMonth} / ${formattedDay} / ${year}`;
}
