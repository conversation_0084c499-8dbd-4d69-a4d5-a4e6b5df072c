import axios from "axios";

const createAxiosInstance = () => {
  const accessToken = localStorage.getItem("accessToken");
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    headers: {
      Authorization: accessToken ? `Bearer ${accessToken}` : "",
    },
  });

  return instance;
};

export const postCallInstace = (url: string, data: any) =>
  createAxiosInstance()
    .post(url, data)
    .then((response) => response.data)
    .catch((error) => {
      console.error(error);
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const getCallInstace = (url: string) =>
  createAxiosInstance()
    .get(url)
    .then((response) => response.data)
    .catch((error) => {
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const deleteCallInstace = (url: string) =>
  createAxiosInstance()
    .delete(url)
    .then((response) => response.data)
    .catch((error) => {
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const putCallInstace = (url: string, data: any) =>
  createAxiosInstance()
    .put(url, data)
    .then((response) => response.data)
    .catch((error) => {
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const patchCallInstace = (url: string, data: any) =>
  createAxiosInstance()
    .patch(url, data)
    .then((response) => response.data)
    .catch((error) => {
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const postAxiosFileInstance = (url: string, data = {}) =>
  createAxiosInstance()
    .post(url, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    .then((response) => response.data)
    .catch((error) => {
      if (error.response.status === 401) {
        localStorage.removeItem("accessToken");
        window.location.href = "/";
        return;
      }
      throw error;
    });

export const http = createAxiosInstance();
