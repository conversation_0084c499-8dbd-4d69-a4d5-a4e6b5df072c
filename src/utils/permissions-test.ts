/**
 * Test utility to verify permissions caching is working correctly
 * This can be used in development to test the permissions optimization
 */

import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'

export const testPermissionsCaching = async () => {
  const permissionsStore = usePermissionsStore()
  
  console.log('🧪 Testing Permissions Caching...')
  
  // Clear any existing permissions
  permissionsStore.clearPermissions()
  console.log('✅ Cleared existing permissions')
  
  // Test 1: First call should make API request
  console.log('📡 Making first API call...')
  const start1 = performance.now()
  const result1 = await permissionsStore.fetchUserPermissions()
  const end1 = performance.now()
  console.log(`✅ First call completed in ${(end1 - start1).toFixed(2)}ms`)
  console.log('📊 Permissions loaded:', !!result1)
  console.log('🏁 Is initialized:', permissionsStore.isInitialized)
  
  // Test 2: Second call should use cache (much faster)
  console.log('📡 Making second API call (should use cache)...')
  const start2 = performance.now()
  const result2 = await permissionsStore.fetchUserPermissions()
  const end2 = performance.now()
  console.log(`✅ Second call completed in ${(end2 - start2).toFixed(2)}ms`)
  console.log('📊 Same result:', result1 === result2)
  
  // Test 3: Multiple simultaneous calls should not cause multiple API requests
  console.log('📡 Making 3 simultaneous calls...')
  permissionsStore.clearPermissions()
  const start3 = performance.now()
  const promises = [
    permissionsStore.fetchUserPermissions(),
    permissionsStore.fetchUserPermissions(),
    permissionsStore.fetchUserPermissions()
  ]
  const results = await Promise.all(promises)
  const end3 = performance.now()
  console.log(`✅ Three simultaneous calls completed in ${(end3 - start3).toFixed(2)}ms`)
  console.log('📊 All results identical:', results[0] === results[1] && results[1] === results[2])
  
  // Test 4: Force refresh should make new API call
  console.log('📡 Testing force refresh...')
  const start4 = performance.now()
  const result4 = await permissionsStore.refreshUserPermissions()
  const end4 = performance.now()
  console.log(`✅ Force refresh completed in ${(end4 - start4).toFixed(2)}ms`)
  console.log('📊 Refresh result loaded:', !!result4)
  
  console.log('🎉 Permissions caching test completed!')
  
  return {
    firstCallTime: end1 - start1,
    secondCallTime: end2 - start2,
    simultaneousCallsTime: end3 - start3,
    forceRefreshTime: end4 - start4,
    cachingWorking: (end2 - start2) < (end1 - start1) / 2 // Second call should be much faster
  }
}

// Add to window for easy testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testPermissionsCaching = testPermissionsCaching
}
