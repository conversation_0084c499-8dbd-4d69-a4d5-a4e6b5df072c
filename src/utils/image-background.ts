// Generate background color based on name
export const getBackgroundColor = (name: string) => {
  const colors = [
    'bg-blue-100 text-blue-600',
    'bg-green-100 text-green-600', 
    'bg-purple-100 text-purple-600',
    'bg-pink-100 text-pink-600',
    'bg-indigo-100 text-indigo-600',
    'bg-yellow-100 text-yellow-600',
    'bg-red-100 text-red-600',
    'bg-teal-100 text-teal-600'
  ]
  const hash = name ? name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) : 0
  return colors[hash % colors.length]
}

export const getInitials = (name: string) => {
  return !name ? '' : name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2)
}

