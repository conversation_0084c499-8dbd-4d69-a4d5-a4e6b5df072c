import { ModuleId } from '@/types/permissions'

/**
 * Maps API module IDs to their corresponding names and ModuleId enum values
 * Based on the modules you provided:
 * 1. "Talent Profile"
 * 2. "Talent Banking Info" 
 * 3. "Talent Documents"
 * 4. "Talent Work History"
 * 5. "Talent BPO Related Info"
 * 6. "Talent Equipment & Software"
 * 7. "Talent Health"
 * 8. "Talent 3rd Party Integrations"
 * 9. "Talent History Logs"
 * 10. "Users"
 * 11. "Roles"
 * 12. "Clients"
 * 13. "Equipment"
 * 14. "System History Logs"
 */

export const MODULE_ID_TO_NAME: Record<number, string> = {
  1: "Talent Profile",
  2: "Talent Banking Info",
  3: "Talent Documents", 
  4: "Talent Work History",
  5: "Talent BPO Related Info",
  6: "Talent Equipment & Software",
  7: "Talent Health",
  8: "Talent 3rd Party Integrations",
  9: "Talent History Logs",
  10: "Users",
  11: "Roles",
  12: "Clients",
  13: "Equipment",
  14: "System History Logs"
}

export const MODULE_ID_TO_ENUM: Record<number, ModuleId> = {
  1: ModuleId.TALENT_PROFILE,
  2: ModuleId.TALENT_BANKING_INFO,
  3: ModuleId.TALENT_DOCUMENTS,
  4: ModuleId.TALENT_WORK_HISTORY,
  5: ModuleId.TALENT_BPO_RELATED_INFO,
  6: ModuleId.TALENT_EQUIPMENT_SOFTWARE,
  7: ModuleId.TALENT_HEALTH,
  8: ModuleId.TALENT_3RD_PARTY_INTEGRATIONS,
  9: ModuleId.TALENT_HISTORY_LOGS,
  10: ModuleId.USERS,
  11: ModuleId.ROLES,
  12: ModuleId.CLIENTS,
  13: ModuleId.EQUIPMENT,
  14: ModuleId.SYSTEM_HISTORY_LOGS
}

export const MODULE_NAME_TO_ID: Record<string, number> = {
  "Talent Profile": 1,
  "Talent Banking Info": 2,
  "Talent Documents": 3,
  "Talent Work History": 4,
  "Talent BPO Related Info": 5,
  "Talent Equipment & Software": 6,
  "Talent Health": 7,
  "Talent 3rd Party Integrations": 8,
  "Talent History Logs": 9,
  "Users": 10,
  "Roles": 11,
  "Clients": 12,
  "Equipment": 13,
  "System History Logs": 14
}

/**
 * Helper function to get module name from ID
 */
export const getModuleName = (moduleId: number): string => {
  return MODULE_ID_TO_NAME[moduleId] || `Unknown Module (${moduleId})`
}

/**
 * Helper function to get ModuleId enum from API module ID
 */
export const getModuleEnum = (moduleId: number): ModuleId | null => {
  return MODULE_ID_TO_ENUM[moduleId] || null
}

/**
 * Helper function to check if a module is talent-related
 */
export const isTalentModule = (moduleId: number): boolean => {
  return moduleId >= 1 && moduleId <= 9
}

/**
 * Helper function to check if a module is history log related
 */
export const isHistoryLogModule = (moduleId: number): boolean => {
  return moduleId === 9 || moduleId === 14 // Talent History Logs or System History Logs
}
