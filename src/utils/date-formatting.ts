/**
 * Formats a date string to MM/DD/YYYY format
 */
export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    
    // Check if date is valid
    if (isNaN(date.getTime())) return dateString
    
    // Format as MM/DD/YYYY
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  } catch (error) {
    console.error('Error formatting date:', error)
    return dateString || ''
  }
}

/**
 * Converts ISO date string to YYYY-MM-DD format for HTML date inputs
 */
export const formatDateForInput = (dateString: string | null | undefined): string => {
  if (!dateString) return ''
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''
    
    return date.toISOString().split('T')[0]
  } catch (error) {
    console.error('Error formatting date for input:', error)
    return ''
  }
}

/**
 * Creates a date range string for display
 */
export const createDateRangeString = (startDate: string, endDate?: string | null): string => {
  const formattedStart = formatDate(startDate)
  
  if (!endDate) {
    return formattedStart ? `${formattedStart} - Present` : 'Date not specified'
  }
  
  const formattedEnd = formatDate(endDate)
  return `${formattedStart} - ${formattedEnd}`
}
