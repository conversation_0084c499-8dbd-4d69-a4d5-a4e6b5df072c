import type { JobRelatedInfoCreate } from '@/types/job-related-info'

export interface ValidationErrors {
  positionType: string
  campaign: string
  clientManager: string
  reportingDepartment: string
  role: string
  title: string
  clientId: string
  location: string
  startDate: string
  workDays: string
  shiftStartTime: string
  shiftEndTime: string
  timeZone: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationErrors
}

/**
 * Validates work history form data
 */
export const validateWorkHistoryForm = (form: JobRelatedInfoCreate): ValidationResult => {
  const errors: ValidationErrors = {
    positionType: '',
    campaign: '',
    clientManager: '',
    reportingDepartment: '',
    role: '',
    title: '',
    clientId: '',
    location: '',
    startDate: '',
    workDays: '',
    shiftStartTime: '',
    shiftEndTime: '',
    timeZone: '',
  }

  let isValid = true

  // Position Type validation (required)
  if (!form.positionType || form.positionType.trim() === '') {
    errors.positionType = 'Please select a position type (FTE, Contract, or Other)'
    isValid = false
  }

  // Campaign validation (required)
  if (!form.campaign || form.campaign.trim() === '') {
    errors.campaign = 'Campaign is required'
    isValid = false
  }

  // Client Manager validation (required)
  if (!form.clientManager || form.clientManager.trim() === '') {
    errors.clientManager = 'Client Manager is required'
    isValid = false
  }

  // Reporting Department validation (required)
  if (!form.reportingDepartment || form.reportingDepartment.trim() === '') {
    errors.reportingDepartment = 'Reporting Department is required'
    isValid = false
  }

  // Role validation (required)
  if (!form.role || form.role.trim() === '') {
    errors.role = 'Role is required'
    isValid = false
  }

  // Title validation (required)
  if (!form.title || form.title.trim() === '') {
    errors.title = 'Title is required'
    isValid = false
  }

  // Client validation (required)
  if (!form.clientId) {
    errors.clientId = 'Client is required'
    isValid = false
  }

  // Location validation (required)
  if (!form.location || form.location.trim() === '') {
    errors.location = 'Location is required'
    isValid = false
  }

  // Start Date validation (required)
  if (!form.startDate || form.startDate.trim() === '') {
    errors.startDate = 'Start Date is required'
    isValid = false
  }

  // Work Days validation (required)
  if (!form.workDays || form.workDays.length === 0) {
    errors.workDays = 'At least one work day is required'
    isValid = false
  }

  // Shift Start Time validation (required)
  if (!form.shiftStartTime || form.shiftStartTime.trim() === '') {
    errors.shiftStartTime = 'Start Time is required'
    isValid = false
  }

  // Shift End Time validation (required)
  if (!form.shiftEndTime || form.shiftEndTime.trim() === '') {
    errors.shiftEndTime = 'End Time is required'
    isValid = false
  }

  // Time Zone validation (required)
  if (!form.timeZone || form.timeZone.trim() === '') {
    errors.timeZone = 'Time Zone is required'
    isValid = false
  }

  return { isValid, errors }
}

/**
 * Creates an empty validation errors object
 */
export const createEmptyValidationErrors = (): ValidationErrors => ({
  positionType: '',
  campaign: '',
  clientManager: '',
  reportingDepartment: '',
  role: '',
  title: '',
  clientId: '',
  location: '',
  startDate: '',
  workDays: '',
  shiftStartTime: '',
  shiftEndTime: '',
  timeZone: '',
})
