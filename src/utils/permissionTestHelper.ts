import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { UserPermissions } from '@/types/permissions'

/**
 * Test helper to simulate different permission scenarios
 */

// Sample API response for HR role (your example)
export const hrRolePermissions: UserPermissions = {
  is_superuser: false,
  permissions: {
    id: 1,
    role: "HR",
    description: "HR",
    module: [
      { module_id: 1, view: true, edit: true, create: true, list: true },   // Talent Profile
      { module_id: 2, view: true, edit: true, create: true, list: true },   // Talent Banking Info
      { module_id: 3, view: true, edit: true, create: true, list: true },   // Talent Documents
      { module_id: 4, view: true, edit: true, create: true, list: true },   // Talent Work History
      { module_id: 5, view: true, edit: true, create: true, list: true },   // Talent BPO Related Info
      { module_id: 6, view: true, edit: false, create: false, list: true }, // Talent Equipment & Software (limited)
      { module_id: 7, view: true, edit: true, create: true, list: true },   // Talent Health
      { module_id: 8, view: false, edit: false, create: false, list: false }, // Talent 3rd Party Integrations (no access)
      { module_id: 9, view: false, edit: false, create: false, list: false }, // Talent History Logs (no access)
      { module_id: 10, view: false, edit: false, create: false, list: false }, // Users (no access)
      { module_id: 11, view: false, edit: false, create: false, list: false }, // Roles (no access)
      { module_id: 12, view: false, edit: false, create: false, list: false }, // Clients (no access)
      { module_id: 13, view: false, edit: false, create: false, list: false }, // Equipment (no access)
      { module_id: 14, view: false, edit: false, create: false, list: false }  // System History Logs (no access)
    ]
  }
}

// Sample superuser permissions
export const superuserPermissions: UserPermissions = {
  is_superuser: true,
  permissions: {
    id: 1,
    role: "Super Admin",
    description: "Super Administrator",
    module: [] // Superusers don't need module permissions
  }
}

// Sample limited user (only view access to some talent modules)
export const limitedUserPermissions: UserPermissions = {
  is_superuser: false,
  permissions: {
    id: 2,
    role: "Viewer",
    description: "Limited Viewer",
    module: [
      { module_id: 1, view: true, edit: false, create: false, list: true },   // Talent Profile (view only)
      { module_id: 2, view: true, edit: false, create: false, list: false },  // Talent Banking Info (view only)
      { module_id: 3, view: false, edit: false, create: false, list: false }, // No other access
      { module_id: 4, view: false, edit: false, create: false, list: false },
      { module_id: 5, view: false, edit: false, create: false, list: false },
      { module_id: 6, view: false, edit: false, create: false, list: false },
      { module_id: 7, view: false, edit: false, create: false, list: false },
      { module_id: 8, view: false, edit: false, create: false, list: false },
      { module_id: 9, view: false, edit: false, create: false, list: false },
      { module_id: 10, view: false, edit: false, create: false, list: false },
      { module_id: 11, view: false, edit: false, create: false, list: false },
      { module_id: 12, view: false, edit: false, create: false, list: false },
      { module_id: 13, view: false, edit: false, create: false, list: false },
      { module_id: 14, view: false, edit: false, create: false, list: false }
    ]
  }
}

// Sample user with no talent permissions
export const noTalentPermissions: UserPermissions = {
  is_superuser: false,
  permissions: {
    id: 3,
    role: "Admin",
    description: "System Administrator",
    module: [
      { module_id: 1, view: false, edit: false, create: false, list: false }, // No talent access
      { module_id: 2, view: false, edit: false, create: false, list: false },
      { module_id: 3, view: false, edit: false, create: false, list: false },
      { module_id: 4, view: false, edit: false, create: false, list: false },
      { module_id: 5, view: false, edit: false, create: false, list: false },
      { module_id: 6, view: false, edit: false, create: false, list: false },
      { module_id: 7, view: false, edit: false, create: false, list: false },
      { module_id: 8, view: false, edit: false, create: false, list: false },
      { module_id: 9, view: false, edit: false, create: false, list: false },
      { module_id: 10, view: true, edit: true, create: true, list: true },   // Users (full access)
      { module_id: 11, view: true, edit: true, create: true, list: true },   // Roles (full access)
      { module_id: 12, view: true, edit: false, create: false, list: true }, // Clients (view/list only)
      { module_id: 13, view: true, edit: true, create: true, list: true },   // Equipment (full access)
      { module_id: 14, view: false, edit: false, create: false, list: false } // System History Logs (no access)
    ]
  }
}

/**
 * Test function to simulate different permission scenarios
 */
export const testPermissionScenario = (permissions: UserPermissions, scenarioName: string) => {
  console.log(`\n=== Testing ${scenarioName} ===`)
  
  const store = usePermissionsStore()
  store.setUserPermissions(permissions)
  
  console.log('Is Superuser:', store.isSuperuser)
  console.log('Role:', store.userRole?.role)
  console.log('Has Any Talent Permission:', store.hasAnyTalentPermission())
  console.log('Can Access History Logs:', store.canAccessHistoryLogs())
  
  // Test specific modules
  console.log('\nModule Access:')
  console.log('- Talent Profile (view):', store.canView(1))
  console.log('- Talent Banking (edit):', store.canEdit(2))
  console.log('- Users (list):', store.canList(10))
  console.log('- Equipment (create):', store.canCreate(13))
  
  console.log('Accessible Modules:', store.getAccessibleModules())
}

/**
 * Run all test scenarios
 */
export const runAllPermissionTests = () => {
  testPermissionScenario(hrRolePermissions, 'HR Role')
  testPermissionScenario(superuserPermissions, 'Superuser')
  testPermissionScenario(limitedUserPermissions, 'Limited User')
  testPermissionScenario(noTalentPermissions, 'No Talent Access')
}
