import { useClientsApi } from '@/apis/master/clients'
import { ClientForm } from '@/types/master/client'
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export interface AssignedEmployee {
  id: number
  firstName: string
  lastName: string
  avatar: string
  startDate: string
  position: string
}

export const useClient = () => {
  const route = useRoute()
  const router = useRouter()

  // Form state
  const clientForm = ref<ClientForm>({
    name: '',
    clientId: '',
    startDate: '',
    endDate: '',
    status: 'Active',
    streetAddress: '',
    city: '',
    zipCode: '',
    country: '',
    contactManager: '',
    contactManagerEmail: '',
    contactManagerPhone: '',
    notes: '',
    attachments: []
  })

  // UI state
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  const showDatePicker = ref(false)
  const showEndDatePicker = ref(false)
  const labelStates = ref({
    name: false,
    clientId: false,
    startDate: false,
    endDate: false,
    streetAddress: false,
    city: false,
    zipCode: false,
    country: false,
    contactManager: false,
    contactManagerEmail: false,
    contactManagerPhone: false,
    notes: false
  })

  // Validation errors
  const fieldErrors = ref<Record<string, string>>({
    name: '',
    clientId: '',
    startDate: '',
    streetAddress: '',
    city: '',
    zipCode: '',
    country: '',
    contactManager: '',
    contactManagerEmail: '',
    contactManagerPhone: ''
  })

  // Assigned employees (for edit mode)
  const assignedEmployees = ref<AssignedEmployee[]>([])

  // Computed properties
  const isEditMode = computed(() => {
    return route.params.id !== undefined && route.params.id !== 'add'
  })

  const pageTitle = computed(() => {
    return isEditMode.value ? clientForm.value.name || 'Edit Client' : 'New Client'
  })

  const submitButtonText = computed(() => {
    return isEditMode.value ? 'UPDATE' : 'SUBMIT'
  })

  // Country options
  const countryOptions = [
    'United States',
    'Canada',
    'Mexico',
    'United Kingdom',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Australia',
    'Japan',
    'China',
    'India',
    'Brazil'
  ]

  // Methods
  const getListOfClients = async (): Promise<ClientForm[]> => {
    const response = await useClientsApi.getAllClients()
    return response.data
  }

  const setLabelActive = (field: keyof typeof labelStates.value, active: boolean) => {
    labelStates.value[field] = active
  }

  const toggleDatePicker = () => {
    showDatePicker.value = !showDatePicker.value
  }

  const toggleEndDatePicker = () => {
    showEndDatePicker.value = !showEndDatePicker.value
    showDatePicker.value = false
  }

  const handleDateSelect = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    clientForm.value.startDate = `${year}-${month}-${day}`
    showDatePicker.value = false
    // Clear validation error when date is selected
    fieldErrors.value.startDate = ''
  }

  const handleEndDateSelect = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    clientForm.value.endDate = `${year}-${month}-${day}`
    showEndDatePicker.value = false
  }

  const clearStartDate = () => {
    clientForm.value.startDate = ''
    setLabelActive('startDate', false)
  }

  const clearEndDate = () => {
    clientForm.value.endDate = ''
    setLabelActive('endDate', false)
  }

  // Validation methods
  const validateField = (field: string, value: any) => {
    switch (field) {
      case 'name':
        fieldErrors.value.name = !value ? 'Client name is required' : ''
        break
      case 'clientId':
        fieldErrors.value.clientId = !value ? 'Client ID is required' : ''
        break
      case 'startDate':
        fieldErrors.value.startDate = !value ? 'Start date is required' : ''
        break
      case 'streetAddress':
        fieldErrors.value.streetAddress = !value ? 'Street address is required' : ''
        break
      case 'city':
        fieldErrors.value.city = !value ? 'City is required' : ''
        break
      case 'zipCode':
        fieldErrors.value.zipCode = !value ? 'ZIP/Postcode is required' : ''
        break
      case 'country':
        fieldErrors.value.country = !value ? 'Country is required' : ''
        break
      case 'contactManager':
        fieldErrors.value.contactManager = !value ? 'Contact name is required' : ''
        break
      case 'contactManagerEmail':
        if (!value) {
          fieldErrors.value.contactManagerEmail = 'Email is required'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          fieldErrors.value.contactManagerEmail = 'Please enter a valid email address'
        } else {
          fieldErrors.value.contactManagerEmail = ''
        }
        break
      case 'contactManagerPhone':
        fieldErrors.value.contactManagerPhone = !value ? 'Phone number is required' : ''
        break
      default:
        break
    }
  }

  const validateForm = () => {
    const requiredFields = [
      'name',
      'clientId',
      'startDate',
      'streetAddress',
      'city',
      'zipCode',
      'country',
      'contactManager',
      'contactManagerEmail',
      'contactManagerPhone'
    ]

    requiredFields.forEach(field => {
      validateField(field, clientForm.value[field as keyof ClientForm])
    })

    return Object.values(fieldErrors.value).filter(error => error).length === 0
  }

  const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement
    if (target.files) {
      const newFiles = Array.from(target.files)
      clientForm.value.attachments.push(...newFiles)
    }
  }

  const removeAttachment = (index: number) => {
    clientForm.value.attachments.splice(index, 1)
  }

  const resetForm = () => {
    clientForm.value = {
      name: '',
      clientId: '',
      startDate: '',
      endDate: '',
      status: 'Active',
      streetAddress: '',
      city: '',
      zipCode: '',
      country: '',
      contactManager: '',
      contactManagerEmail: '',
      contactManagerPhone: '',
      notes: '',
      attachments: []
    }
    
    // Reset label states
    Object.keys(labelStates.value).forEach(key => {
      labelStates.value[key as keyof typeof labelStates.value] = false
    })
  }

  const loadClient = async (clientId: string) => {
    isLoading.value = true
    try {
      // Mock data for now - replace with actual API call
      const response = await useClientsApi.getClientById(parseInt(clientId))
      clientForm.value = {
        ...response.data,
        endDate: response.data.endDate || '',
        attachments: []
      }
      const empResponse = await useClientsApi.getAssignedEmployees(parseInt(clientId))
      assignedEmployees.value = empResponse.data
    } catch (error) {
      console.error('Error loading client:', error)
    } finally {
      isLoading.value = false
    }
  }

  const submitClient = async () => {
    // Validate form before submission
    if (!validateForm()) {
      console.log('Form validation failed')
      return
    }

    isSubmitting.value = true
    try {
      if (isEditMode.value) {
        // Update existing client
        useClientsApi.updateClient(parseInt(route.params.id as string), clientForm.value)
      } else {
        // Create new client
        useClientsApi.createClient(clientForm.value)
        // API call would go here
      }

      // Navigate back to clients list
      router.push('/clients')
    } catch (error) {
      console.error('Error submitting client:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  const cancelEdit = () => {
    router.push('/clients')
  }

  return {
    // State
    clientForm,
    isLoading,
    isSubmitting,
    showDatePicker,
    showEndDatePicker,
    labelStates,
    assignedEmployees,
    fieldErrors,

    // Computed
    isEditMode,
    pageTitle,
    submitButtonText,
    countryOptions,

    // Methods
    getListOfClients,
    setLabelActive,
    toggleDatePicker,
    toggleEndDatePicker,
    handleDateSelect,
    handleEndDateSelect,
    clearStartDate,
    clearEndDate,
    validateField,
    validateForm,
    handleFileUpload,
    removeAttachment,
    resetForm,
    loadClient,
    submitClient,
    cancelEdit
  }
}
