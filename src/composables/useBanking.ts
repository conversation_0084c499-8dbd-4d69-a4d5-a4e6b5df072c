import { ref, computed } from "vue";
import type {
  BankingInformation,
  BankingInformationCreate,
  BankingInformationUpdate,
} from "@/types/banking";
import { bankingApi } from "@/apis/talent/banking";
import { toast } from "vue3-toastify";
import { useBankingStore } from "@/store/talent/useBankingStore";

export function useBanking() {
  const store = useBankingStore()
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Field validation errors
  const fieldErrors = ref<Record<string, string>>({
    bankName: "",
    accountNumber: "",
    clabe: "",
    swiftCode: "",
  });

  const validateField = (fieldName: string, value: any) => {
    const validationRules: Record<string, () => string> = {
      bankName: () => (!value?.trim() ? "Bank Name is required" : ""),
      accountNumber: () => {
        if (!value?.trim()) return "Account Number is required";
        if (!value.isdigit?.() && !/^\d+$/.test(value))
          return "Account number must contain only digits";
        return "";
      },
      clabe: () => {
        if (!value?.trim()) return "CLABE is required";
        if (value.length !== 18) return "CLABE must be exactly 18 digits";
        if (!/^\d+$/.test(value)) return "CLABE must contain only digits";
        return "";
      },
      swiftCode: () => {
        if (value?.trim()) {
          const swiftLen = value.length;
          if (swiftLen !== 8 && swiftLen !== 11)
            return "SWIFT code must be 8 or 11 characters long";
          if (!/^[A-Za-z0-9]+$/.test(value))
            return "SWIFT code must contain only alphanumeric characters";
        }
        return "";
      },
    };

    fieldErrors.value[fieldName] = validationRules[fieldName]?.() || "";
  };

  const validateForm = () => {
    const requiredFields = ["bankName", "accountNumber", "clabe"];
    const optionalFields = ["swiftCode"];

    requiredFields.forEach((field) => {
      validateField(
        field,
        store.bankingInfo?.[field as keyof BankingInformation]
      );
    });

    optionalFields.forEach((field) => {
      const value = store.bankingInfo?.[field as keyof BankingInformation];
      if (value) {
        validateField(field, value);
      }
    });

    return Object.values(fieldErrors.value).filter((error) => error);
  };

  const createBankingInfo = async (data: BankingInformationCreate) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await bankingApi.create(data);
      store.bankingInfo = response.data;
      store.isCreateMode = false;
      toast("Banking information created successfully", {
        theme: "auto",
        type: "success",
      });
      return { success: true, data: response.data };
    } catch (err: any) {
      error.value = err.message || "An error occurred";
      return { success: false, error: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  const getBankingByTalentId = async (talentProfileId: number) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await bankingApi.getByTalentId(talentProfileId);
      if (!response.data) {
        return { success: false, error: "No banking information found" };
      }
      store.bankingInfo = response.data;
      store.isCreateMode = false;
      return { success: true, data: response.data };
    } catch (err: any) {
      error.value = err.message || "An error occurred";
      return { success: false, error: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  const updateBankingInfo = async (
    bankingId: number,
    data: BankingInformationUpdate
  ) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await bankingApi.update(bankingId, data);
      store.bankingInfo = response.data;
      store.isCreateMode = false;
      return { success: true, data: response.data };
    } catch (err: any) {
      error.value = err.message || "An error occurred";
      return { success: false, error: error.value };
    } finally {
      isLoading.value = false;
    }
  };

  const clearError = () => {
    error.value = null;
  };

  return {
    bankingInfo: computed(() => store.bankingInfo),
    isCreateMode: computed(() => store.isCreateMode),
    isLoading,
    error,
    fieldErrors,
    createBankingInfo,
    getBankingByTalentId,
    updateBankingInfo,
    validateField,
    validateForm,
    clearError,
  };
}
