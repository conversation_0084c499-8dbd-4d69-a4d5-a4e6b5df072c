import { ref, computed, type Ref } from 'vue'

export const usePagination = <T>(items: Ref<T[]>, itemsPerPageCount = 10) => {
  const currentPage = ref(1)
  const itemsPerPage = ref(itemsPerPageCount)

  const totalPages = computed(() => 
    Math.ceil(items.value.length / itemsPerPage.value)
  )

  const paginatedItems = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return items.value.slice(start, end)
  })

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++
    }
  }

  const previousPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }

  return {
    currentPage,
    totalPages,
    itemsPerPage,
    paginatedItems,
    goToPage,
    nextPage,
    previousPage
  }
}