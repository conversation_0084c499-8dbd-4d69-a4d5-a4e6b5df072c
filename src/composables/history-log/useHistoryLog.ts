import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { historyLogApi } from '@/apis/history-log/history-log'
import type { HistoryLog, HistoryLogFilters } from '@/types/history-log'
import { usePagination } from '@/composables/common/usePagination'

export const useHistoryLog = () => {
  // State
  const historyLogs = ref<HistoryLog[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Filter states
  const searchQuery = ref('')
  const startDate = ref(new Date(2020, 0, 1)) // Jan 2020
  const endDate = ref(new Date(2020, 1, 1)) // Feb 2020
  const selectedEventType = ref('all')
  const selectedStatus = ref('all')
  const selectedUser = ref('all')

  // UI states
  const showStartDatePicker = ref(false)
  const showEndDatePicker = ref(false)

  // Mock data for development - remove when API is ready
  const mockData: HistoryLog[] = [
    {
      id: 1,
      date: '05/03/2025',
      time: '21:37',
      event: 'Update',
      user: '<PERSON>',
      userRole: 'Admin',
      descriptions: [
        { field: 'Users / Jane Smith', value: 'Active', label: 'Before' },
        { field: 'Users / Jane Smith', value: 'Deactive', label: 'After' }
      ],
      status: 'Succeeded'
    },
    {
      id: 2,
      date: '05/03/2025',
      time: '20:00',
      event: 'Delete',
      user: 'Gerardo Urias',
      userRole: 'Admin',
      descriptions: [
        { field: 'Talent / Eric Canseco / Documents / Documents Collected (5)', value: 'Birth Certificate', label: 'Before' },
        { field: 'Talent / Eric Canseco / Documents / Documents Collected (4)', value: '', label: 'After' }
      ],
      status: 'Succeeded'
    },
    {
      id: 3,
      date: '05/05/2025',
      time: '13:10',
      event: 'Update',
      user: 'William Brown',
      userRole: 'Editor',
      descriptions: [
        { field: 'Talent / Eric Canseco / Profile / Personal Security Info / CURP', value: 'X234FSDF234234', label: 'Before' },
        { field: 'Talent / Eric Canseco / Profile / Personal Security Info / CURP', value: 'X234FSDF234234', label: 'After' }
      ],
      status: 'Error'
    },
    {
      id: 4,
      date: '05/03/2020',
      time: '20:00',
      event: 'Add',
      user: 'Jessica Taylor',
      userRole: 'Editor',
      descriptions: [
        { field: 'Talent / Eric Canseco / Profile / Emergency Contact (1)', value: '', label: 'Before' },
        { field: 'Talent / Eric Canseco / Profile / Emergency Contact (2)', value: 'Luke Bishop, Brother, 664 419 5614, <EMAIL>', label: 'After' }
      ],
      status: 'Succeeded'
    }
  ]

  // Computed filtered logs
  const filteredLogs = computed(() => {
    let filtered = historyLogs.value

    // Apply search filter
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase().trim()
      filtered = filtered.filter(log => {
        return log.user.toLowerCase().includes(query) ||
               log.event.toLowerCase().includes(query) ||
               log.descriptions.some(desc => 
                 desc.field.toLowerCase().includes(query) ||
                 desc.value.toLowerCase().includes(query)
               )
      })
    }

    // Apply event type filter
    if (selectedEventType.value !== 'all') {
      filtered = filtered.filter(log => log.event.toLowerCase() === selectedEventType.value.toLowerCase())
    }

    // Apply status filter
    if (selectedStatus.value !== 'all') {
      filtered = filtered.filter(log => log.status === selectedStatus.value)
    }

    // Apply user filter
    if (selectedUser.value !== 'all') {
      filtered = filtered.filter(log => log.user === selectedUser.value)
    }

    return filtered
  })

  // Get unique values for filters
  const eventTypes = computed(() => {
    const types = new Set<string>()
    historyLogs.value.forEach(log => types.add(log.event))
    return Array.from(types).sort()
  })

  const users = computed(() => {
    const userSet = new Set<string>()
    historyLogs.value.forEach(log => userSet.add(log.user))
    return Array.from(userSet).sort()
  })

  // Pagination
  const { currentPage, totalPages, paginatedItems, nextPage, previousPage, goToPage } =
    usePagination(filteredLogs, 10)

  // Reset to page 1 when filters change
  watch([searchQuery, selectedEventType, selectedStatus, selectedUser], () => {
    goToPage(1)
  })

  // Methods
  const fetchHistoryLogs = async (filters?: Partial<HistoryLogFilters>) => {
    try {
      isLoading.value = true
      error.value = null
      
      // For now, use mock data. Replace with API call when ready:
      // const response = await historyLogApi.getHistoryLogs(filters)
      // historyLogs.value = response.data
      
      historyLogs.value = mockData
    } catch (err) {
      error.value = 'Failed to fetch history logs'
      console.error('Error fetching history logs:', err)
    } finally {
      isLoading.value = false
    }
  }

  const fetchHistoryLogsByTalentId = async (talentId: number) => {
    try {
      isLoading.value = true
      error.value = null
      const response = await historyLogApi.getHistoryLogsByTalentId(talentId)
      historyLogs.value = response.data
    } catch (err) {
      error.value = 'Failed to fetch talent history logs'
      console.error('Error fetching talent history logs:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Date picker methods
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const toggleStartDatePicker = () => {
    showStartDatePicker.value = !showStartDatePicker.value
    showEndDatePicker.value = false
  }

  const toggleEndDatePicker = () => {
    showEndDatePicker.value = !showEndDatePicker.value
    showStartDatePicker.value = false
  }

  const updateStartDate = (date: Date) => {
    startDate.value = date
    showStartDatePicker.value = false
  }

  const updateEndDate = (date: Date) => {
    endDate.value = date
    showEndDatePicker.value = false
  }

  // Filter methods
  const clearFilters = () => {
    searchQuery.value = ''
    selectedEventType.value = 'all'
    selectedStatus.value = 'all'
    selectedUser.value = 'all'
    startDate.value = new Date(2020, 0, 1)
    endDate.value = new Date(2020, 1, 1)
  }

  // Click outside handler
  const handleClickOutside = (event: Event) => {
    const target = event.target as Element
    if (!target.closest('.calendar-container')) {
      showStartDatePicker.value = false
      showEndDatePicker.value = false
    }
  }

  // Lifecycle
  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    fetchHistoryLogs()
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  return {
    // State
    historyLogs,
    isLoading,
    error,
    filteredLogs,
    
    // Filter state
    searchQuery,
    startDate,
    endDate,
    selectedEventType,
    selectedStatus,
    selectedUser,
    
    // UI state
    showStartDatePicker,
    showEndDatePicker,
    
    // Computed
    eventTypes,
    users,
    
    // Pagination
    currentPage,
    totalPages,
    paginatedItems,
    nextPage,
    previousPage,
    goToPage,
    
    // Methods
    fetchHistoryLogs,
    fetchHistoryLogsByTalentId,
    formatDate,
    toggleStartDatePicker,
    toggleEndDatePicker,
    updateStartDate,
    updateEndDate,
    clearFilters
  }
}
