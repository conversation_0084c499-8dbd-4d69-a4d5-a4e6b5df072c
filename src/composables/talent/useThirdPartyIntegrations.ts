import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue3-toastify'
import { thirdPartyIntegrationApi } from '@/apis/talent/third-party-integration'
import { 
  ThirdPartyIntegrationDisplay, 
  ThirdPartyIntegrationResponse,
  INTEGRATION_TYPES,
  INTEGRATION_DESCRIPTIONS,
  type IntegrationType
} from '@/types/third-party-integration'

export const useThirdPartyIntegrations = () => {
  const route = useRoute()
  
  // State
  const integrations = ref<ThirdPartyIntegrationDisplay[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const talentId = computed(() => {
    const id = route.params.id as string
    return id ? Number(id) : 0
  })

  // Helper function to convert API response to display format
  const convertToDisplayFormat = (apiIntegrations: ThirdPartyIntegrationResponse[]): ThirdPartyIntegrationDisplay[] => {
    const predefinedIntegrations: ThirdPartyIntegrationDisplay[] = Object.values(INTEGRATION_TYPES).map(name => ({
      id: 0, // Will be updated if integration exists
      name,
      isActive: false, // Default to false (not created)
      status: undefined, // Backend managed
      description: INTEGRATION_DESCRIPTIONS[name],
      data: undefined,
      createdAt: undefined,
      updatedAt: undefined
    }))

    // Update with actual data from API
    apiIntegrations.forEach(apiIntegration => {
      const index = predefinedIntegrations.findIndex(p => p.name === apiIntegration.thirdParty)
      if (index !== -1) {
        predefinedIntegrations[index] = {
          id: apiIntegration.id,
          name: apiIntegration.thirdParty as IntegrationType,
          isActive: apiIntegration.isActive,
          status: apiIntegration.status,
          description: INTEGRATION_DESCRIPTIONS[apiIntegration.thirdParty as IntegrationType] || 'Third party integration',
          data: apiIntegration.data,
          createdAt: apiIntegration.createdAt,
          updatedAt: apiIntegration.updatedAt
        }
      }
    })

    return predefinedIntegrations
  }

  // Actions
  const fetchIntegrations = async () => {
    if (!talentId.value) {
      error.value = 'No talent ID provided'
      return
    }

    try {
      isLoading.value = true
      error.value = null
      
      const response = await thirdPartyIntegrationApi.getByTalentId(talentId.value)
      integrations.value = convertToDisplayFormat(response.data || [])
    } catch (err) {
      error.value = 'Failed to fetch third party integrations'
      console.error('Failed to fetch integrations:', err)
      toast.error('Failed to load integrations')
    } finally {
      isLoading.value = false
    }
  }

  const createIntegration = async (integrationName: string) => {
    if (!talentId.value) {
      toast.error('No talent ID provided')
      return
    }

    try {
      isLoading.value = true

      // Generate default data for the integration
      const data = `${integrationName.toLowerCase().replace(/\s+/g, '_')}_${talentId.value}_${Date.now()}`

      await thirdPartyIntegrationApi.create({
        talentProfileId: talentId.value,
        thirdParty: integrationName,
        data,
        isActive: true,
        jsonData: JSON.stringify({ createdBy: 'ui', timestamp: new Date().toISOString() })
      })

      toast.success(`${integrationName} integration created successfully`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to create integration:', err)
      toast.error(`Failed to create ${integrationName} integration`)
    } finally {
      isLoading.value = false
    }
  }

  const toggleIntegration = async (integration: ThirdPartyIntegrationDisplay) => {
    if (!integration.isActive && integration.id === 0) {
      toast.warning('Please create the integration first')
      return
    }

    try {
      isLoading.value = true

      await thirdPartyIntegrationApi.toggle(integration.id, integration.isActive)

      const action = integration.isActive ? 'disabled' : 'enabled'

      toast.success(`${integration.name} integration ${action}`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to toggle integration:', err)
      toast.error(`Failed to toggle ${integration.name} integration`)
    } finally {
      isLoading.value = false
    }
  }

  const deleteIntegration = async (integrationName: string) => {
    const integration = integrations.value.find(i => i.name === integrationName)
    if (!integration || (!integration.isActive && integration.id === 0)) {
      toast.warning('Integration not found or not created')
      return
    }

    try {
      isLoading.value = true

      await thirdPartyIntegrationApi.delete(integration.id)

      toast.success(`${integrationName} integration deleted successfully`)
      await fetchIntegrations() // Refresh the list
    } catch (err) {
      console.error('Failed to delete integration:', err)
      toast.error(`Failed to delete ${integrationName} integration`)
    } finally {
      isLoading.value = false
    }
  }

  // Status helpers
  const getStatusColor = (integration: ThirdPartyIntegrationDisplay) => {
    if (integration.id === 0 || !integration.isActive) {
      return 'text-gray-600 bg-gray-100' // Not created or inactive
    }

    // Use backend status if available, otherwise default to active
    const backendStatus = integration.status || 'active'
    switch (backendStatus.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'success':
        return 'text-green-600 bg-green-100'
      case 'pending':
      case 'initiated':
      case 'processing':
        return 'text-yellow-600 bg-yellow-100'
      case 'failed':
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-blue-600 bg-blue-100'
    }
  }

  const getStatusText = (integration: ThirdPartyIntegrationDisplay) => {
    if (integration.id === 0) {
      return 'Not Created'
    }

    if (!integration.isActive) {
      return 'Disabled'
    }

    // Use backend status if available
    const backendStatus = integration.status || 'Active'
    return backendStatus.charAt(0).toUpperCase() + backendStatus.slice(1).toLowerCase()
  }

  return {
    // State
    integrations,
    isLoading,
    error,
    
    // Computed
    talentId,
    
    // Actions
    fetchIntegrations,
    createIntegration,
    toggleIntegration,
    deleteIntegration,
    
    // Helpers
    getStatusColor,
    getStatusText
  }
}
