import { useTalentProfileStore } from '@/store/talent/useTalentProfileStore';
import { ref } from 'vue'

export const useSkillsManager = () => {
  const newSkill = ref('')

  const store = useTalentProfileStore();
  const { form } = store

  const addSkill = () => {
    console.log('Adding skill:', newSkill.value)
    console.log('Current skills:', form.skills)
    if (newSkill.value.trim()) {
      // Initialize skills array if it doesn't exist
      if (!form.skills) {
        form.skills = []
      }
      
      // Check if skill already exists
      if (!form.skills.includes(newSkill.value.trim())) {
        form.skills.push(newSkill.value.trim())
        newSkill.value = ''
      }
    }
  }

  const removeSkill = (index: number) => {
    if (form.skills && form.skills.length > index) {
      form.skills.splice(index, 1)
    }
  }

  return {
    newSkill,
    addSkill,
    removeSkill,
  }
}
