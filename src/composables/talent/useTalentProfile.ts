import { ref, computed } from "vue";
import { useTalentProfileStore } from "@/store/talent/useTalentProfileStore";
import {
  createTalentProfile,
  getTalentProfileById,
  updateTalentProfileApi,
  uploadProfilePicture,
  updateProfilePicture,
} from "@/apis/talent/profile";
import {
  createTalentSecurity,
  getTalentSecurityById,
  updateTalentSecurity,
} from "@/apis/talent/security";
import {
  createTalentSkillSet,
  getTalentSkillSetById,
  updateTalentSkillSet,
} from "@/apis/talent/skills";
import {
  createTalentEmergencyContact,
  getTalentEmergencyContactById,
  updateTalentEmergencyContacts,
} from "@/apis/talent/emergency-contact";
import { useRouter } from "vue-router";
import { EmergencyContactSnakeCase } from "@/types/talent";
import { toast } from "vue3-toastify";

export const useTalentProfile = () => {
  const store = useTalentProfileStore();
  const profilePicture = ref('')

  const router = useRouter();

  // Error states for each field
  const fieldErrors = ref<Record<string, string>>({
    firstName: "",
    lastName: "",
    primaryContact: "",
    personalEmail: "",
    dateOfBirth: "",
    gender: "",
    address1: "",
    city: "",
    country: "",
    state: "",
    postalCode: "",
    skills: "",
    writtenEnglish: "",
    spokenEnglish: "",
    englishType: "",
    yearsOfExperience: "",
  });

  const validateField = (fieldName: string, value: any) => {
    const validationRules: Record<string, () => string> = {
      firstName: () => (!value?.trim() ? "First Name is required" : ""),
      lastName: () => (!value?.trim() ? "Last Name is required" : ""),
      primaryContact: () =>
        !value?.trim() ? "Primary Contact is required" : "",
      personalEmail: () => (!value?.trim() ? "Personal Email is required" : ""),
      dateOfBirth: () => (!value ? "Date of Birth is required" : ""),
      gender: () => (!value ? "Gender is required" : ""),
      address1: () => (!value?.trim() ? "Address 1 is required" : ""),
      city: () => (!value?.trim() ? "City is required" : ""),
      country: () => (!value ? "Country is required" : ""),
      state: () => (!value?.trim() ? "State/Province is required" : ""),
      postalCode: () => (!value?.trim() ? "Postal Code is required" : ""),
      skills: () =>
        !value || value.length === 0 ? "At least one skill is required" : "",
      writtenEnglish: () => (!value ? "Written English level is required" : ""),
      spokenEnglish: () => (!value ? "Spoken English level is required" : ""),
      englishType: () => (!value ? "English Type is required" : ""),
      yearsOfExperience: () =>
        value === undefined || value === null
          ? "Years of Experience is required"
          : "",
    };

    fieldErrors.value[fieldName] = validationRules[fieldName]?.() || "";
  };

  const validateForm = () => {
    const requiredFields = [
      "firstName",
      "lastName",
      "primaryContact",
      "personalEmail",
      "dateOfBirth",
      "gender",
      "address1",
      "city",
      "country",
      "state",
      "postalCode",
      "skills",
      "writtenEnglish",
      "spokenEnglish",
      "englishType",
      "yearsOfExperience",
    ];

    requiredFields.forEach((field) => {
      validateField(field, store.form[field as keyof typeof store.form]);
    });

    return Object.values(fieldErrors.value).filter((error) => error);
  };

  const submitForm = async (isEdit: boolean, id = 0) => {
    if (isEdit) {
      await updateTalentProfileCall(id);
    } else {
      await createTalentProfileCall();
    }
  };

  const createTalentProfileCall = async () => {
    try {
      store.setLoading(true);

      const validationErrors = validateForm();

      if (validationErrors.length > 0) {
        return;
      }

      const response = await createTalentProfile({
        firstName: store.form.firstName,
        middleName: store.form.middleName || undefined,
        lastName: store.form.lastName,
        personalEmail: store.form.personalEmail,
        countryCode: store.form.primaryCountryCode || undefined,
        phone: store.form.primaryContact || undefined,
        countryCode2: store.form.secondaryCountryCode || undefined,
        phone2: store.form.secondaryContact || undefined,
        dateOfBirth: store.form.dateOfBirth || undefined,
        gender: store.form.gender || undefined,
        address_1: store.form.address1,
        apt_unit: store.form.aptUnit || undefined,
        address_2: store.form.aptUnit || undefined,
        city: store.form.city || undefined,
        country: store.form.country || undefined,
        zipCode: store.form.postalCode || undefined,
        state: store.form.state || undefined,
        pic: store.form.pic || undefined,
        is_active: true,
      });

      if (
        store.form.skills.length > 0 ||
        store.form.writtenEnglish.length > 0 ||
        store.form.spokenEnglish.length > 0 ||
        store.form.englishType.length > 0 ||
        store.form.yearsOfExperience !== null
      ) {
        await createTalentSkillSet({
          talentProfileId: response.data.id,
          skills: store.form.skills || undefined,
          yearsOfExperience: store.form.yearsOfExperience || undefined,
          notesFromInterview: store.form.notes || undefined,
          englishLevel: {
            write: store.form.writtenEnglish,
            speak: store.form.spokenEnglish,
            type: store.form.englishType,
          },
        });
      }

      if (store.form.emergencyContacts.length > 0) {
        store.form.emergencyContacts.forEach(async (contact) => {
          await createTalentEmergencyContact({
            ...contact,
            talent_profile_id: response.data.id,
          });
        });
      }

      if (
        store.form.imms.length > 0 ||
        store.form.curp.length > 0 ||
        store.form.rfc.length > 0
      ) {
        await createTalentSecurity({
          talent_profile_id: response.data.id,
          imms: store.form.imms || undefined,
          curp: store.form.curp || undefined,
          rfc: store.form.rfc || undefined,
        });
      }

      router.push({ path: `/talent/${response.data.id}/banking` });
    } catch (err) {
      console.error("Failed to create talent profile:", err);
      store.setError("Failed to create talent profile");
    }
  };

  const getTalentProfile = async (id: number) => {
    try {
      store.setLoading(true);
      const response = await getTalentProfileById(id);
      if (!response.data) {
        return;
      }
      store.form.address1 = response.data.address_1;
      store.form.address2 = response.data.address_2 || "";
      store.form.city = response.data.city || "";
      store.form.country = response.data.country || "";
      store.form.postalCode = response.data.zipCode || "";
      store.form.state = response.data.state || "";

      store.form.firstName = response.data.firstName;
      store.form.lastName = response.data.lastName;
      store.form.middleName = response.data.middleName || "";
      store.form.personalEmail = response.data.personalEmail;
      store.form.primaryContact = response.data.phone || "";
      store.form.primaryCountryCode = response.data.countryCode || "+52";
      store.form.secondaryContact = response.data.phone2 || "";
      store.form.secondaryCountryCode = response.data.countryCode2 || "+52";
      store.form.dateOfBirth = response.data.dateOfBirth || "";
      store.form.gender = response.data.gender || "";
      store.form.pic = response.data.pic ? "data:image/jpeg;base64," + response.data.pic : "";
      profilePicture.value = "data:image/jpeg;base64," + response.data.pic;
    } catch (err) {
      store.setError("Failed to fetch talent profile");
      alert("Failed to fetch talent profile");
      router.push({ path: "/talent" });
    }
  };

  const getTalentSecurity = async (id: number) => {
    try {
      store.setLoading(true);
      const response = await getTalentSecurityById(id);
      if (!response.data) {
        return;
      }
      store.form.imms = response.data.imms || "";
      store.form.curp = response.data.curp || "";
      store.form.rfc = response.data.rfc || "";
    } catch (err) {}
  };

  const getTalentEmergencyContacts = async (id: number) => {
    try {
      store.setLoading(true);
      const response = await getTalentEmergencyContactById(id);
      if (!response.data) {
        return;
      }
      store.form.emergencyContacts = response.data.map(
        (contact: EmergencyContactSnakeCase) => {
          return {
            id: contact.id,
            talent_profile_id: contact.talent_profile_id,
            firstName: contact.first_name,
            middleName: contact.middle_name || "",
            lastName: contact.last_name || "",
            countryCode: contact.country_code || "+52",
            phone: contact.phone,
            email: contact.email || "",
            relationship: contact.relationship,
          };
        }
      );
    } catch (err) {
    } finally {
      store.setLoading(false);
    }
  };

  const getTalentSkillSet = async (id: number) => {
    try {
      store.setLoading(true);
      const response = await getTalentSkillSetById(id);
      if (!response.data) {
        return;
      }
      store.form.skills = response.data.skills || [];
      store.form.writtenEnglish = response.data.englishLevel?.write || "";
      store.form.spokenEnglish = response.data.englishLevel?.speak || "";
      store.form.englishType = response.data.englishLevel?.type || "";
      store.form.yearsOfExperience = response.data.yearsOfExperience || null;
      store.form.notes = response.data.notesFromInterview || "";
    } catch (err) {
      alert("Failed to fetch talent information");
    } finally {
      store.setLoading(false);
    }
  };

  const updateTalentProfileCall = async (id: number) => {
    try {
      store.setLoading(true);

      const validationErrors = validateForm();

      if (validationErrors.length > 0) {
        return;
      }

      await updateTalentProfileApi(id, {
        firstName: store.form.firstName,
        middleName: store.form.middleName || undefined,
        lastName: store.form.lastName,
        personalEmail: store.form.personalEmail,
        countryCode: store.form.primaryCountryCode || undefined,
        phone: store.form.primaryContact || undefined,
        countryCode2: store.form.secondaryCountryCode || undefined,
        phone2: store.form.secondaryContact || undefined,
        dateOfBirth: store.form.dateOfBirth || undefined,
        gender: store.form.gender || undefined,
        address_1: store.form.address1,
        apt_unit: store.form.aptUnit || undefined,
        address_2: store.form.aptUnit || undefined,
        city: store.form.city || undefined,
        country: store.form.country || undefined,
        zipCode: store.form.postalCode || undefined,
        state: store.form.state || undefined,
        pic: store.form.pic || undefined,
        is_active: true,
      });

      await updateTalentSkillSet(id, {
        skills: store.form.skills || undefined,
        yearsOfExperience: store.form.yearsOfExperience || undefined,
        notesFromInterview: store.form.notes || undefined,
        englishLevel: {
          write: store.form.writtenEnglish,
          speak: store.form.spokenEnglish,
          type: store.form.englishType,
        },
      });

      for (const contact of store.form.emergencyContacts) {
        if (contact.id) {
          await updateTalentEmergencyContacts(id, contact);
        } else {
          await createTalentEmergencyContact({
            ...contact,
            talent_profile_id: id,
          });
        }
      }

      await updateTalentSecurity(id, {
        imms: store.form.imms || undefined,
        curp: store.form.curp || undefined,
        rfc: store.form.rfc || undefined,
      });

      toast("Talent Profile Updated Successfully!", {
        theme: "auto",
        type: "default",
        dangerouslyHTMLString: true,
      });

    } catch (err) {
    } finally {
      store.setLoading(false);
    }
  };

  const uploadTalentProfilePicture = async (talentId: number, file: File) => {
    try {
      store.setLoading(true);
      const response = await uploadProfilePicture(talentId, file);

      // Update the form with the new profile picture URL
      store.form.pic = response.data.pic || '';

      toast("Profile picture uploaded successfully!", {
        theme: "auto",
        type: "success",
        dangerouslyHTMLString: true,
      });

      return response.data.pic;
    } catch (err) {
      console.error("Failed to upload profile picture:", err);
      toast("Failed to upload profile picture", {
        theme: "auto",
        type: "error",
        dangerouslyHTMLString: true,
      });
      throw err;
    } finally {
      store.setLoading(false);
    }
  };

  const updateTalentProfilePicture = async (talentId: number, file: File) => {
    try {
      store.setLoading(true);
      const response = await updateProfilePicture(talentId, file);

      // Update the form with the new profile picture (base64 or URL)
      if (response.data && response.data.pic) {
        store.form.pic = response.data.pic;
      }

      toast("Profile picture updated successfully!", {
        theme: "auto",
        type: "success",
        dangerouslyHTMLString: true,
      });

      return response.data.pic;
    } catch (err) {
      console.error("Failed to update profile picture:", err);
      toast("Failed to update profile picture", {
        theme: "auto",
        type: "error",
        dangerouslyHTMLString: true,
      });
      throw err;
    } finally {
      store.setLoading(false);
    }
  };

  return {
    // State
    fieldErrors,

    // Computed
    isLoading: computed(() => store.isLoading),
    error: computed(() => store.error),
    profilePicture,

    // Methods
    validateField,
    validateForm,
    submitForm,
    getTalentProfile,
    getTalentSecurity,
    getTalentEmergencyContacts,
    getTalentSkillSet,
    uploadTalentProfilePicture,
    updateTalentProfilePicture,
  };
};
