import { ref, computed, watch } from "vue";
import { DocumentItem } from "@/types/documents";
import {
  createTalentDocuments,
  getTalentDocumentsApi,
} from "@/apis/talent/documents";

export const useDocuments = () => {
  const fileInput = ref<HTMLInputElement>();
  const selectedDocumentType = ref("");
  const otherDocumentType = ref("");

  const labelStates = ref({
    documentType: false,
    otherDocumentType: false,
  });

  // Upload preview state
  const previewFile = ref<File | null>(null);
  const previewUrl = ref<string | null>(null);
  const previewType = ref<string>("");

  // View modal state
  const isViewModalOpen = ref(false);
  const isLoadingDocument = ref(false);
  const viewUrl = ref<string | null>(null);
  const viewType = ref<string>("");
  const viewDocName = ref<string>("");
  const viewError = ref<string>("");

  const collectedDocuments = ref<DocumentItem[]>([]);

  // Computed property to determine the final document type
  const finalDocumentType = computed(() => {
    if (selectedDocumentType.value === "Others") {
      return otherDocumentType.value.trim();
    }
    return selectedDocumentType.value;
  });

  // Watch for changes in selectedDocumentType to clear otherDocumentType when switching away from "Others"
  watch(selectedDocumentType, (newVal) => {
    if (newVal !== "Others") {
      otherDocumentType.value = "";
    }
  });

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  const triggerFileUpload = () => {
    if (finalDocumentType.value && !previewUrl.value) {
      fileInput.value?.click();
    }
  };

  const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0] && finalDocumentType.value) {
      const file = target.files[0];

      // Clean up previous preview
      if (previewUrl.value) {
        URL.revokeObjectURL(previewUrl.value);
      }

      // Create preview
      previewFile.value = file;
      previewType.value = file.type;
      previewUrl.value = URL.createObjectURL(file);

      // Reset input
      target.value = "";
    }
  };

  const confirmAddDocument = async (id: number) => {
    if (!previewFile.value || !previewUrl.value || !finalDocumentType.value)
      return;

    const docType = finalDocumentType.value;
    const newDocument: DocumentItem = {
      name: docType,
      description: previewFile.value.name,
      size: formatFileSize(previewFile.value.size),
      file: previewFile.value,
      url: previewUrl.value,
    };

    collectedDocuments.value.push(newDocument);

    await createTalentDocuments({
      talent_profile_id: id,
      doc_type: docType,
      file: previewFile.value,
    });

    // Reset upload state
    previewFile.value = null;
    previewType.value = "";
    previewUrl.value = null;
    selectedDocumentType.value = "";
    otherDocumentType.value = "";
  };

  const getTalentDocuments = async (id: number) => {
    const documents = await getTalentDocumentsApi(id);
    for (const doc of documents.data) {
      console.log(doc);
      collectedDocuments.value.push({
        name: doc.docType,
        size: formatFileSize(doc.size),
        base64Content: doc.base64Content,
      });
    }
  };

  const cancelPreview = () => {
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value);
    }
    previewFile.value = null;
    previewType.value = "";
    previewUrl.value = null;
  };

  const removeDocument = (index: number) => {
    const doc = collectedDocuments.value[index];
    if (doc.url) {
      URL.revokeObjectURL(doc.url);
    }
    collectedDocuments.value.splice(index, 1);
  };

  const inferMimeType = (filename: string): string => {
    const ext = filename.split(".").pop()?.toLowerCase();
    switch (ext) {
      case "pdf":
        return "application/pdf";
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "gif":
        return "image/gif";
      case "doc":
        return "application/msword";
      case "docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      default:
        return "application/octet-stream";
    }
  };

  const viewFile = async (doc: DocumentItem) => {
    // If it's a newly uploaded file with blob URL, show it directly
    if (doc.url && doc.file) {
      viewDocName.value = doc.name;
      viewUrl.value = doc.url;
      viewType.value = doc.file.type;
      isViewModalOpen.value = true;
      return;
    }

    try {
      // For existing documents, fetch from API
      isViewModalOpen.value = true;
      isLoadingDocument.value = true;
      viewError.value = "";
      viewDocName.value = doc.name;
      
      const mockBase64 = doc.base64Content; // 1x1 transparent PNG
      const mimeType = inferMimeType(doc.description || "jpg"); // Default to JPEG if not provided

      viewUrl.value = `data:${mimeType};base64,${mockBase64}`;
      viewType.value = mimeType;
    } catch (error) {
      console.error("Error fetching document:", error);
      viewError.value = "Failed to load document. Please try again.";
    } finally {
      isLoadingDocument.value = false;
    }
  };

  const closeViewModal = () => {
    isViewModalOpen.value = false;
    isLoadingDocument.value = false;
    viewUrl.value = null;
    viewType.value = "";
    viewDocName.value = "";
    viewError.value = "";
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0KB";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(0)) + sizes[i];
  };

  return {
    fileInput,
    selectedDocumentType,
    otherDocumentType,
    finalDocumentType,
    labelStates,
    previewFile,
    previewUrl,
    previewType,
    isViewModalOpen,
    isLoadingDocument,
    viewUrl,
    viewType,
    viewDocName,
    viewError,
    collectedDocuments,

    setLabelActive,
    triggerFileUpload,
    handleFileUpload,
    confirmAddDocument,
    cancelPreview,
    removeDocument,
    viewFile,
    closeViewModal,
    formatFileSize,
    getTalentDocuments,
  };
};
