import { ref } from "vue";
import { useRoute } from "vue-router";
import type { JobRelatedInfoCreate } from "@/types/job-related-info";
import { jobRelatedInfoApi } from "@/apis/talent/job-related-info";
import { WorkHistoryService } from "@/services/work-history.service";
import { WORK_HISTORY_OPTIONS, WORK_DAYS_OPTIONS } from "@/constants/work-history-options";

export const useJobRelatedInfo = () => {
  const route = useRoute();

  const jobRelatedInfoForm = ref<JobRelatedInfoCreate>({
    talentProfileId: 0,
    isFte: false,
    isContract: false,
    isOther: false,
    campaign: "",
    clientManager: "",
    reportingDepartment: "",
    positionType: "",
    role: "",
    title: "",
    clientId: "",
    location: "",
    startDate: "",
    endDate: "",
    workDays: [],
    shiftEndTime: "",
    shiftStartTime: "",
    timeZone: "",
    baseWage: "",
    currency: "",
    frequency: "",
    paidDaysOff: "",
    vacationsUsed: "",
    remainingVacations: "",
    notes: "",
  });

  const labelStates = ref({
    campaign: false,
    clientManager: false,
    reportingDepartment: false,
    positionType: false,
    role: false,
    title: false,
    clientPlaced: false,
    location: false,
    startDate: false,
    endDate: false,
    workDays: false,
    startTime: false,
    endTime: false,
    timeZone: false,
    baseWage: false,
    currency: false,
    frequency: false,
    paidDaysOff: false,
    vacationsUsed: false,
    remainingVacations: false,
    notes: false,
  });

  const pastWorkHistory = ref<JobRelatedInfoCreate[]>([]);

  // Date picker states
  const showStartDatePicker = ref(false);
  const showEndDatePicker = ref(false);

  // Dropdown states for searchable selects
  const dropdownStates = ref({
    campaign: false,
    clientManager: false,
    reportingDepartment: false,
    role: false,
    clientPlaced: false,
    location: false,
    workDays: false,
    timeZone: false,
    currency: false,
    frequency: false,
  });

  // Work days options
  const workDaysOptions = WORK_DAYS_OPTIONS;

  // Dropdown options
  const dropdownOptions = ref(WORK_HISTORY_OPTIONS);

  // Filtered options based on search input
  const getFilteredOptions = (field: string) => {
    const searchValue = jobRelatedInfoForm.value[
      field as keyof typeof jobRelatedInfoForm.value
    ] as string;
    if (!searchValue)
      return dropdownOptions.value[field as keyof typeof dropdownOptions.value];

    return dropdownOptions.value[
      field as keyof typeof dropdownOptions.value
    ].filter((option) =>
      option.toLowerCase().includes(searchValue.toLowerCase())
    );
  };

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  // Date picker methods
  const toggleStartDatePicker = () => {
    showStartDatePicker.value = !showStartDatePicker.value;
  };

  const toggleEndDatePicker = () => {
    showEndDatePicker.value = !showEndDatePicker.value;
  };

  const updateStartDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    jobRelatedInfoForm.value.startDate = `${year}-${month}-${day}`;
    showStartDatePicker.value = false;
  };

  const updateEndDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    jobRelatedInfoForm.value.endDate = `${year}-${month}-${day}`;
    showEndDatePicker.value = false;
  };

  const clearStartDate = () => {
    jobRelatedInfoForm.value.startDate = "";
    setLabelActive("startDate", false);
  };

  const clearEndDate = () => {
    jobRelatedInfoForm.value.endDate = "";
    setLabelActive("endDate", false);
  };

  const selectJobOption = (field: string, option: string) => {
    (jobRelatedInfoForm.value as any)[field] = option;
    dropdownStates.value[field as keyof typeof dropdownStates.value] = false;
    setLabelActive(field, false);
  };

  const handleDropdownInput = (field: string) => {
    dropdownStates.value[field as keyof typeof dropdownStates.value] = true;
  };

  const handleDropdownBlur = (field: string) => {
    setTimeout(() => {
      setLabelActive(field, false);
      dropdownStates.value[field as keyof typeof dropdownStates.value] = false;
    }, 150);
  };

  // Work days functions
  const removeWorkDay = (index: number) => {
    if (jobRelatedInfoForm.value.workDays.length > index) {
      jobRelatedInfoForm.value.workDays.splice(index, 1);
    }
  };

  const addPredefinedWorkDay = (day: string) => {
    if (!jobRelatedInfoForm.value.workDays.includes(day)) {
      jobRelatedInfoForm.value.workDays.push(day);
    }
  };

  const getTalentJobRelatedInfo = async () => {
    try {
      const id = Number.parseInt(route.params.id as string);
      const response = await jobRelatedInfoApi.getByTalentId(id);
      if (!response.data) {
        return;
      }

      // Normalize work history data using service
      pastWorkHistory.value = WorkHistoryService.normalizeWorkHistoryData(response.data);
    } catch (err) {
      console.error("Error fetching job related info:", err);
      pastWorkHistory.value = [];
    }
  };

  const prepareFormData = () => {
    const formData = { ...jobRelatedInfoForm.value };
    formData.talentProfileId = Number.parseInt(route.params.id as string);
    if (formData.positionType === "FTE") {
      formData.isFte = true;
      formData.isContract = false;
      formData.isOther = false;
    } else if (formData.positionType === "Contract") {
      formData.isFte = false;
      formData.isContract = true;
      formData.isOther = false;
    } else if (formData.positionType === "Other") {
      formData.isOther = true;
      formData.isFte = false;
      formData.isContract = false;
    }
    return formData;
  };

  const submitJobForm = async () => {
    const formData = prepareFormData();
    await jobRelatedInfoApi.create(formData);
  };

  const updatePastWorkHistory = async () => {
    const formData = prepareFormData();
    await jobRelatedInfoApi.update(
      jobRelatedInfoForm.value.id as number,
      formData
    );
  };

  return {
    jobRelatedInfoForm,
    labelStates,
    showStartDatePicker,
    showEndDatePicker,
    dropdownStates,
    dropdownOptions,
    workDaysOptions,
    pastWorkHistory,
    getFilteredOptions,
    setLabelActive,
    toggleStartDatePicker,
    toggleEndDatePicker,
    updateStartDate,
    updateEndDate,
    clearStartDate,
    clearEndDate,
    selectJobOption,
    handleDropdownInput,
    handleDropdownBlur,
    removeWorkDay,
    addPredefinedWorkDay,
    getTalentJobRelatedInfo,
    submitJobForm,
    updatePastWorkHistory,
  };
};
