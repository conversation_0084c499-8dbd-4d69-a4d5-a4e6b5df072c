import { healthApi } from "@/apis/talent/health";
import { ref } from "vue";
import { useRoute } from "vue-router";

export const useHealth = () => {
  const route = useRoute();
  const labelStates = ref({
    ongoingHealthIssueDetails: false,
    chronicConditionDetails: false,
    pastHealthIssuesDetails: false,
    allergiesDetails: false,
    regularMedicationDetails: false,
  });
  const form = ref({
    ongoingHealthIssue: "No",
    ongoingHealthIssueDetails: "",
    chronicCondition: "No",
    chronicConditionDetails: "",
    pastHealthIssues: "No",
    pastHealthIssuesDetails: "",
    allergies: "No",
    allergiesDetails: "",
    regularMedication: "No",
    regularMedicationDetails: "",
  });

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  const submitHealthInfo = async () => {
    if (form.value.ongoingHealthIssue === "No") {
      form.value.ongoingHealthIssueDetails = "";
    }
    if (form.value.chronicCondition === "No") {
      form.value.chronicConditionDetails = " ";
    }
    if (form.value.pastHealthIssues === "No") {
      form.value.pastHealthIssuesDetails = " ";
    }
    if (form.value.allergies === "No") {
      form.value.allergiesDetails = " ";
    }
    if (form.value.regularMedication === "No") {
      form.value.regularMedicationDetails = " ";
    }

    await healthApi.createAllergies({
      talentProfileId: Number.parseInt(route.params.id as string),
      allergy: form.value.allergiesDetails,
      medication: form.value.regularMedicationDetails,
    });

    await healthApi.createChronicConditions({
      talentProfileId: Number.parseInt(route.params.id as string),
      cronicCondition: form.value.chronicConditionDetails
    });

    await healthApi.createPastHealthIssues({
      talentProfileId: Number.parseInt(route.params.id as string),
      pastHealthIssues: form.value.pastHealthIssuesDetails,
    });

    await healthApi.createOngoingHealthIssues({
      talentProfileId: Number.parseInt(route.params.id as string),
      ongoingHealthIssues: form.value.ongoingHealthIssueDetails,
      medication: form.value.regularMedicationDetails,
    });

  };

  const getAllergyInfo = async () => {
    const response = await healthApi.getAllergies(Number.parseInt(route.params.id as string));
    const data = response.data;
    form.value.allergies = "No";
    form.value.allergiesDetails = "";
    if (data.allergy.trim().length > 0) {
      form.value.allergies = "Yes";
      form.value.allergiesDetails = data.allergy;
    }
  };


  const getOngoingHealthIssues = async () => {
    const response = await healthApi.getOnGoingHealthIssues(Number.parseInt(route.params.id as string));
    const data = response.data;
    form.value.ongoingHealthIssue = "No";
    form.value.ongoingHealthIssueDetails = "";
    if (data.ongoingHealthIssues.trim().length > 0) {
      form.value.ongoingHealthIssue = "Yes";
      form.value.ongoingHealthIssueDetails = data.ongoingHealthIssues;
    }

    if (data.medication.trim().length > 0) {
      form.value.regularMedication = "Yes";
      form.value.regularMedicationDetails = data.medication;
    }
  };


  const getPastHealthIssues = async () => {
    const response = await healthApi.getPastHealthIssues(Number.parseInt(route.params.id as string));
    const data = response.data;
    form.value.pastHealthIssues = "No";
    form.value.pastHealthIssuesDetails = "";
    if (data.pastHealthIssues.trim().length > 0) {
      form.value.pastHealthIssues = "Yes";
      form.value.pastHealthIssuesDetails = data.pastHealthIssues;
    }
  };

  const getChronicConditions = async () => {
    const response = await healthApi.getChronicConditions(Number.parseInt(route.params.id as string));
    const data = response.data;
    console.log(data);
    form.value.chronicCondition = "No";
    form.value.chronicConditionDetails = "";
    if (data.cronicCondition.trim().length > 0) {
      form.value.chronicCondition = "Yes";
      form.value.chronicConditionDetails = data.cronicCondition;
    }
  };

  return {
    labelStates,
    form,
    setLabelActive,
    submitHealthInfo,
    getAllergyInfo,
    getOngoingHealthIssues,
    getPastHealthIssues,
    getChronicConditions,
  };
};
