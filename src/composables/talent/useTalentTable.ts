import { computed } from 'vue'
import { useTalentStore } from '@/store/employee/useTalentStore'
import { 
  getAllTalents,
  deactivateTalent
} from '@/apis/talent/profile'
import { toast } from 'vue3-toastify'

export const useTalentTable = () => {
  const store = useTalentStore()

  const fetchAllTalents = async () => {
    try {
      store.setLoading(true)
      const response = await getAllTalents()
      console.log('Talents:', response)
      store.setEmployees(response)
    } catch (err) {
      store.setError('Failed to fetch employees')
      console.error('Failed to fetch employees:', err)
    } finally {
      store.setLoading(false)
    }
  }

  const updateTalentStatus = async (id: number, action: string, reason: string) => {
    try {
      store.setLoading(true)
      
      const isActive = action === 'activate'
      await deactivateTalent(id, { 
        is_active: isActive, 
        reason: reason 
      })
      
      toast(`Talent ${action}d successfully!`, {
        theme: "auto",
        type: "success",
      })
    } catch (err) {
      store.setError(`Failed to ${action} talent`)
      throw err
    } finally {
      store.setLoading(false)
    }
  }
  

  return {
    // State
    isLoading: computed(() => store.isLoading),
    error: computed(() => store.error),
    talents: computed(() => store.talents),

    // Methods
    fetchAllTalents,
    updateTalentStatus
  }
}
