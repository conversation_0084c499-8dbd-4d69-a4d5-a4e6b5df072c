
export const usePhoneInput = () => {
  // Phone formatting patterns for different countries
  const phoneFormats = {
    '+1': { // US/Canada
      maxLength: 10,
      format: (cleaned: string) => {
        if (cleaned.length === 10) {
          return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
        }
        return cleaned
      }
    },
    '+52': { // Mexico
      maxLength: 10,
      format: (cleaned: string) => {
        if (cleaned.length === 10) {
          return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6)}`
        }
        return cleaned
      }
    },
    '+91': { // India
      maxLength: 10,
      format: (cleaned: string) => {
        if (cleaned.length === 10) {
          return `${cleaned.slice(0, 5)} ${cleaned.slice(5)}`
        }
        return cleaned
      }
    }
  }

  const formatPhoneDisplay = (phone: string, countryCode?: string) => {
    if (!phone) return ''
    const cleaned = phone.replace(/\D/g, '')

    // If no country code provided, use default US formatting for backward compatibility
    if (!countryCode || !phoneFormats[countryCode as keyof typeof phoneFormats]) {
      if (cleaned.length === 10) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
      }
      return cleaned
    }

    const format = phoneFormats[countryCode as keyof typeof phoneFormats]
    return format.format(cleaned)
  }

  const handlePhoneInput = (event: Event, callback: (value: string) => void, countryCode?: string) => {
    const input = event.target as HTMLInputElement
    const cleaned = input.value.replace(/\D/g, '')

    // Get max length based on country code, default to 10
    const maxLength = countryCode && phoneFormats[countryCode as keyof typeof phoneFormats]
      ? phoneFormats[countryCode as keyof typeof phoneFormats].maxLength
      : 10

    const limited = cleaned.substring(0, maxLength)
    callback(limited)
    input.value = formatPhoneDisplay(limited, countryCode)
  }

  return {
    formatPhoneDisplay,
    handlePhoneInput,
  }
}
