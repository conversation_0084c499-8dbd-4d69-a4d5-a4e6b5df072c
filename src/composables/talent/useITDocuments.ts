import { ref, computed } from 'vue'
import { createITDocument, getITDocumentsByTalentId, type ITDocumentCreate, type ITDocumentResponse } from '@/apis/talent/it-documents'
import { toast } from 'vue3-toastify'

export const useITDocuments = () => {
  // State
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const itDocuments = ref<ITDocumentResponse[]>([])
  
  // Form state
  const selectedDocumentType = ref('')
  const otherDocumentType = ref('')
  const selectedFile = ref<File | null>(null)
  const documentNotes = ref('')
  const showAddDocumentForm = ref(false)

  // Label states for floating labels
  const labelStates = ref<Record<string, boolean>>({
    documentType: false,
    otherDocumentType: false,
    documentNotes: false,
  })

  // Computed
  const finalDocumentType = computed(() => {
    return selectedDocumentType.value === 'Others' ? otherDocumentType.value : selectedDocumentType.value
  })

  // Methods
  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field] = active
  }

  const resetForm = () => {
    selectedDocumentType.value = ''
    otherDocumentType.value = ''
    selectedFile.value = null
    documentNotes.value = ''
    showAddDocumentForm.value = false

    // Reset label states
    Object.keys(labelStates.value).forEach(key => {
      labelStates.value[key] = false
    })
  }

  const loadITDocuments = async (talentId: number) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await getITDocumentsByTalentId(talentId)
      
      if (response.status_code === 200) {
        itDocuments.value = response.data || []
      } else if (response.status_code === 404) {
        itDocuments.value = []
      } else {
        throw new Error(response.message || 'Failed to load IT documents')
      }
    } catch (err) {
      console.error('Error loading IT documents:', err)
      error.value = err instanceof Error ? err.message : 'Failed to load IT documents'
      itDocuments.value = []
    } finally {
      isLoading.value = false
    }
  }

  const createDocument = async (talentId: number) => {
    if (!finalDocumentType.value || !selectedFile.value) {
      toast('Please select a document type and file', {
        theme: 'auto',
        type: 'error',
      })
      return
    }

    try {
      isLoading.value = true
      error.value = null

      const documentData: ITDocumentCreate = {
        talentProfileId: talentId,
        documentType: finalDocumentType.value,
        file: selectedFile.value,
        notes: documentNotes.value || undefined,
      }

      const response = await createITDocument(documentData)

      if (response.status_code === 201) {
        toast('IT document uploaded successfully!', {
          theme: 'auto',
          type: 'success',
        })

        // Add the new document to the list
        if (response.data) {
          itDocuments.value.push(response.data)
        }

        // Reset form
        resetForm()
      } else {
        throw new Error(response.message || 'Failed to upload IT document')
      }
    } catch (err) {
      console.error('Error uploading IT document:', err)
      error.value = err instanceof Error ? err.message : 'Failed to upload IT document'
      toast('Failed to upload IT document', {
        theme: 'auto',
        type: 'error',
      })
    } finally {
      isLoading.value = false
    }
  }

  const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement
    if (target.files && target.files[0]) {
      selectedFile.value = target.files[0]
    }
  }

  const showAddForm = () => {
    showAddDocumentForm.value = true
  }

  const cancelAdd = () => {
    resetForm()
  }

  return {
    // State
    isLoading,
    error,
    itDocuments,

    // Form state
    selectedDocumentType,
    otherDocumentType,
    selectedFile,
    documentNotes,
    showAddDocumentForm,
    labelStates,

    // Computed
    finalDocumentType,

    // Methods
    setLabelActive,
    resetForm,
    loadITDocuments,
    createDocument,
    handleFileSelect,
    showAddForm,
    cancelAdd,
  }
}
