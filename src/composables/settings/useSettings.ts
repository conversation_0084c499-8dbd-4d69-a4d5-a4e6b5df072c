import { ref, computed } from 'vue'
import { settingsApi } from '@/apis/settings/settings'
import { PersonalInfo, PasswordForm, SettingsErrors, SettingsLabelStates } from '@/types/settings'
import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { toast } from 'vue3-toastify'

export const useSettings = () => {
  // Use permissions store
  const permissionsStore = usePermissionsStore()

  // Loading states
  const isLoadingPersonalInfo = ref(false)
  const isLoadingPassword = ref(false)
  const isLoadingSettings = ref(false)

  // Personal Information
  const personalInfo = ref<PersonalInfo>({
    name: '',
    email: '',
    phone: ''
  })

  // Password Form
  const passwordForm = ref<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // Form validation errors
  const errors = ref<SettingsErrors>({
    name: '',
    email: '',
    phone: ''
  })

  // Label states for floating labels
  const labelStates = ref<SettingsLabelStates>({
    name: false,
    email: false,
    phone: false
  })

  // Password visibility toggles
  const showCurrentPassword = ref(false)
  const showNewPassword = ref(false)
  const showConfirmPassword = ref(false)

  // Set label active state
  const setLabelActive = (field: keyof SettingsLabelStates, active: boolean) => {
    labelStates.value[field] = active
  }

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    return phoneRegex.test(phone.replace(/\s/g, ''))
  }

  // Clear errors
  const clearErrors = () => {
    errors.value = {
      name: '',
      email: '',
      phone: ''
    }
  }

  // Validate personal info form
  const validatePersonalInfo = (): boolean => {
    clearErrors()
    let isValid = true

    if (!personalInfo.value.name.trim()) {
      errors.value.name = 'Name is required'
      isValid = false
    }

    if (!personalInfo.value.email.trim()) {
      errors.value.email = 'Email is required'
      isValid = false
    } else if (!validateEmail(personalInfo.value.email)) {
      errors.value.email = 'Please enter a valid email address'
      isValid = false
    }

    if (!personalInfo.value.phone.trim()) {
      errors.value.phone = 'Phone number is required'
      isValid = false
    } else if (!validatePhone(personalInfo.value.phone)) {
      errors.value.phone = 'Please enter a valid phone number'
      isValid = false
    }

    return isValid
  }

  // Password validation computed property
  const isPasswordFormValid = computed(() => {
    return passwordForm.value.currentPassword &&
           passwordForm.value.newPassword &&
           passwordForm.value.confirmPassword &&
           passwordForm.value.newPassword === passwordForm.value.confirmPassword &&
           passwordForm.value.newPassword.length >= 8 &&
           /[A-Z]/.test(passwordForm.value.newPassword) &&
           /[a-z]/.test(passwordForm.value.newPassword) &&
           /[0-9]/.test(passwordForm.value.newPassword)
  })

  // Load user settings
  const loadUserSettings = async () => {
    try {
      isLoadingSettings.value = true
      
      // Get data from API
      const response = await settingsApi.getUserSettings()
      
      personalInfo.value = {
        name: response.data.name || localStorage.getItem('name') || '',
        email: response.data.email || localStorage.getItem('email') || '',
        phone: response.data.phone || ''
      }
    } catch (error) {
      console.error('Error loading user settings:', error)
      toast.error('Failed to load user settings')
    } finally {
      isLoadingSettings.value = false
    }
  }

  // Update personal information
  const updatePersonalInfo = async () => {
    if (!validatePersonalInfo()) {
      return false
    }

    try {
      isLoadingPersonalInfo.value = true
      
      const response = await settingsApi.updatePersonalInfo({
        name: personalInfo.value.name,
        phone: personalInfo.value.phone
      })

      if (response.status_code === 200) {
        // Update localStorage
        localStorage.setItem('name', personalInfo.value.name)
        
        toast.success('Personal information updated successfully!')
        return true
      } else {
        toast.error(response.message || 'Failed to update personal information')
        return false
      }
    } catch (error) {
      console.error('Error updating personal info:', error)
      toast.error('Failed to update personal information')
      return false
    } finally {
      isLoadingPersonalInfo.value = false
    }
  }

  // Update password
  const updatePassword = async () => {
    if (!isPasswordFormValid.value) {
      toast.error('Please check all password requirements')
      return false
    }

    try {
      isLoadingPassword.value = true

      if (passwordForm.value.currentPassword === passwordForm.value.newPassword) {
        toast.error('New password cannot be the same as the current password')
        return false
      }

      if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
        toast.error('Passwords do not match')
        return false
      }
      
      const response = await settingsApi.updatePassword({
        oldPassword: passwordForm.value.currentPassword,
        newPassword: passwordForm.value.newPassword
      })

      if (response.status_code === 200) {
        // Reset password form
        passwordForm.value = {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
        
        toast.success('Password updated successfully!')
        return true
      } else {
        toast.error(response.message || 'Failed to update password')
        return false
      }
    } catch (error) {
      console.error('Error updating password:', error)
      toast.error('Failed to update password')
      return false
    } finally {
      isLoadingPassword.value = false
    }
  }


  // Get user permissions using the store
  const getUserPermissions = async () => {
    try {
      await permissionsStore.fetchUserPermissions()
    } catch (error) {
      console.error('Error getting user permissions:', error)
    }
  }

  return {
    // State
    personalInfo,
    passwordForm,
    errors,
    labelStates,
    showCurrentPassword,
    showNewPassword,
    showConfirmPassword,
    isLoadingPersonalInfo,
    isLoadingPassword,
    isLoadingSettings,

    // Computed
    isPasswordFormValid,

    // Methods
    setLabelActive,
    loadUserSettings,
    updatePersonalInfo,
    updatePassword,
    validatePersonalInfo,
    clearErrors,
    getUserPermissions,

    // Permissions store
    permissionsStore
  }
}
