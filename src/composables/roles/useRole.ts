import { rolesAndModulesApi } from "@/apis/roles/role";
import { Module, ModulePermission } from "@/types/roles";
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";

export const useRole = () => {
  const module = ref<Module[]>([]);
  const router = useRouter();
  const route = useRoute();

  const form = ref({
    roleName: "",
    roleDescription: "",
  });

  const labelStates = ref({
    roleName: false,
    roleDescription: false,
  });

  const permissions = ref<ModulePermission[]>([]);
  const isEditMode = ref(false);
  const roleId = ref<number | null>(null);

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  const goBack = () => {
    router.back();
  };

  const getModules = async () => {
    try {
      const response = await rolesAndModulesApi.getModules();
      module.value = response.data;
    } catch (error) {
      console.error("Error fetching modules:", error);
      return [];
    }
  };

  const getRoles = async () => {
    try {
      const response = await rolesAndModulesApi.getRoles();
      return response;
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  };

  const getRoleById = async (id: number) => {
    try {
      const response = await rolesAndModulesApi.getRoleById(id);
      return response;
    } catch (error) {
      console.error("Error fetching role by ID:", error);
      return null;
    }
  };

  const initializeEditMode = () => {
    const id = route.params.id;
    if (id && !isNaN(Number(id))) {
      isEditMode.value = true;
      roleId.value = Number(id);
    } else {
      isEditMode.value = false;
      roleId.value = null;
    }
  };

  const populateFormForEdit = async () => {
    if (!isEditMode.value || !roleId.value) return;

    const roleData = await getRoleById(roleId.value);
    if (roleData) {
      // Populate form fields
      form.value.roleName = roleData.role;
      form.value.roleDescription = roleData.description;

      // Populate permissions based on role data
      permissions.value = module.value.map((m) => {
        const roleModule = roleData.module.find((rm: any) => rm.module_id === m.id);
        return {
          name: m.name,
          permissions: {
            id: m.id,
            list: roleModule ? roleModule.list : false,
            create: roleModule ? roleModule.create : false,
            view: roleModule ? roleModule.view : false,
            edit: roleModule ? roleModule.edit : false,
          },
        };
      });
    }
  };

  const handleSubmit = async () => {
    const roleData = {
      name: form.value.roleName,
      roleId: roleId.value,
      description: form.value.roleDescription,
      module: permissions.value.map((m) => ({
        module_id: m.permissions.id!,
        view: m.permissions.view,
        edit: m.permissions.edit,
        create: m.permissions.create,
        list: m.permissions.list,
      })),
    };

    try {
      if (isEditMode.value && roleId.value) {

        await rolesAndModulesApi.updateRole(roleId.value, roleData);
      } else {
        // Create new role
        await rolesAndModulesApi.createRole(roleData);
      }

      // Navigate back to roles list
      router.push("/roles");
    } catch (error) {
      console.error("Error submitting role:", error);
    }
  };

  return {
    module,
    permissions,
    form,
    labelStates,
    isEditMode,
    roleId,
    getModules,
    getRoles,
    getRoleById,
    initializeEditMode,
    populateFormForEdit,
    setLabelActive,
    goBack,
    handleSubmit,
  };
};
