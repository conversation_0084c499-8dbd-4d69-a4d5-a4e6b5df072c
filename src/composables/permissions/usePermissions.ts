import { computed } from 'vue'
import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { ModuleId, PermissionType } from '@/types/permissions'

export const usePermissions = () => {
  const permissionsStore = usePermissionsStore()

  // Computed properties for easy access
  const isSuperuser = computed(() => permissionsStore.isSuperuser)
  const userRole = computed(() => permissionsStore.userRole)
  const isLoading = computed(() => permissionsStore.isLoading)
  const error = computed(() => permissionsStore.error)

  // Permission check methods
  const hasPermission = (moduleId: ModuleId, permissionType: PermissionType): boolean => {
    return permissionsStore.hasPermission(moduleId, permissionType)
  }

  const canView = (moduleId: ModuleId): boolean => {
    return permissionsStore.canView(moduleId)
  }

  const canEdit = (moduleId: ModuleId): boolean => {
    return permissionsStore.canEdit(moduleId)
  }

  const canCreate = (moduleId: ModuleId): boolean => {
    return permissionsStore.canCreate(moduleId)
  }

  const canList = (moduleId: ModuleId): boolean => {
    return permissionsStore.canList(moduleId)
  }

  const canAccess = (moduleId: ModuleId): boolean => {
    return permissionsStore.canAccess(moduleId)
  }

  // Navigation helpers
  const canAccessTalent = computed(() => permissionsStore.hasAnyTalentPermission())
  const canAccessUsers = computed(() => canAccess(ModuleId.USERS))
  const canAccessRoles = computed(() => canAccess(ModuleId.ROLES))
  const canAccessClients = computed(() => canAccess(ModuleId.CLIENTS))
  const canAccessEquipment = computed(() => canAccess(ModuleId.EQUIPMENT))
  const canAccessHistoryLog = computed(() => permissionsStore.canAccessHistoryLogs())

  // Individual talent module access helpers
  const canAccessTalentProfile = computed(() => canAccess(ModuleId.TALENT_PROFILE))
  const canAccessTalentBanking = computed(() => canAccess(ModuleId.TALENT_BANKING_INFO))
  const canAccessTalentDocuments = computed(() => canAccess(ModuleId.TALENT_DOCUMENTS))
  const canAccessTalentWorkHistory = computed(() => canAccess(ModuleId.TALENT_WORK_HISTORY))
  const canAccessTalentBPOInfo = computed(() => canAccess(ModuleId.TALENT_BPO_RELATED_INFO))
  const canAccessTalentEquipment = computed(() => canAccess(ModuleId.TALENT_EQUIPMENT_SOFTWARE))
  const canAccessTalentHealth = computed(() => canAccess(ModuleId.TALENT_HEALTH))
  const canAccessTalent3rdParty = computed(() => canAccess(ModuleId.TALENT_3RD_PARTY_INTEGRATIONS))
  const canAccessTalentHistoryLogs = computed(() => canAccess(ModuleId.TALENT_HISTORY_LOGS))

  // Action helpers for common operations
  const canCreateTalent = computed(() => canCreate(ModuleId.TALENT_PROFILE))
  const canEditTalent = computed(() => canEdit(ModuleId.TALENT_PROFILE))
  const canViewTalent = computed(() => canView(ModuleId.TALENT_PROFILE))

  // Talent module specific permissions
  const canCreateTalentProfile = computed(() => canCreate(ModuleId.TALENT_PROFILE))
  const canEditTalentProfile = computed(() => canEdit(ModuleId.TALENT_PROFILE))
  const canViewTalentProfile = computed(() => canView(ModuleId.TALENT_PROFILE))

  const canCreateTalentBanking = computed(() => canCreate(ModuleId.TALENT_BANKING_INFO))
  const canEditTalentBanking = computed(() => canEdit(ModuleId.TALENT_BANKING_INFO))
  const canViewTalentBanking = computed(() => canView(ModuleId.TALENT_BANKING_INFO))

  const canCreateTalentDocuments = computed(() => canCreate(ModuleId.TALENT_DOCUMENTS))
  const canEditTalentDocuments = computed(() => canEdit(ModuleId.TALENT_DOCUMENTS))
  const canViewTalentDocuments = computed(() => canView(ModuleId.TALENT_DOCUMENTS))

  const canCreateUsers = computed(() => canCreate(ModuleId.USERS))
  const canEditUsers = computed(() => canEdit(ModuleId.USERS))
  const canViewUsers = computed(() => canView(ModuleId.USERS))

  const canCreateRoles = computed(() => canCreate(ModuleId.ROLES))
  const canEditRoles = computed(() => canEdit(ModuleId.ROLES))
  const canViewRoles = computed(() => canView(ModuleId.ROLES))

  // Get accessible modules for navigation
  const getAccessibleModules = () => {
    return permissionsStore.getAccessibleModules()
  }

  // Check if user has any talent permission
  const hasAnyTalentPermission = () => {
    return permissionsStore.hasAnyTalentPermission()
  }

  // Initialize permissions
  const initializePermissions = async () => {
    try {
      await permissionsStore.fetchUserPermissions()
    } catch (error) {
      console.error('Failed to initialize permissions:', error)
    }
  }

  // Refresh permissions (force reload from API)
  const refreshPermissions = async () => {
    try {
      await permissionsStore.refreshUserPermissions()
    } catch (error) {
      console.error('Failed to refresh permissions:', error)
    }
  }

  // Clear permissions (for logout)
  const clearPermissions = () => {
    permissionsStore.clearPermissions()
  }

  return {
    // Store access
    permissionsStore,

    // Computed properties
    isSuperuser,
    userRole,
    isLoading,
    error,

    // Permission check methods
    hasPermission,
    canView,
    canEdit,
    canCreate,
    canList,
    canAccess,

    // Navigation helpers
    canAccessTalent,
    canAccessUsers,
    canAccessRoles,
    canAccessClients,
    canAccessEquipment,
    canAccessHistoryLog,

    // Individual talent module access
    canAccessTalentProfile,
    canAccessTalentBanking,
    canAccessTalentDocuments,
    canAccessTalentWorkHistory,
    canAccessTalentBPOInfo,
    canAccessTalentEquipment,
    canAccessTalentHealth,
    canAccessTalent3rdParty,
    canAccessTalentHistoryLogs,

    // Action helpers
    canCreateTalent,
    canEditTalent,
    canViewTalent,
    canCreateUsers,
    canEditUsers,
    canViewUsers,
    canCreateRoles,
    canEditRoles,
    canViewRoles,

    // Talent module specific permissions
    canCreateTalentProfile,
    canEditTalentProfile,
    canViewTalentProfile,
    canCreateTalentBanking,
    canEditTalentBanking,
    canViewTalentBanking,
    canCreateTalentDocuments,
    canEditTalentDocuments,
    canViewTalentDocuments,

    // Utility methods
    getAccessibleModules,
    hasAnyTalentPermission,
    initializePermissions,
    refreshPermissions,
    clearPermissions
  }
}
