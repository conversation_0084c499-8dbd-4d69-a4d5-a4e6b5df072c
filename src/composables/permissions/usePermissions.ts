import { computed } from 'vue'
import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { ModuleId, PermissionType } from '@/types/permissions'

export const usePermissions = () => {
  const permissionsStore = usePermissionsStore()

  // Computed properties for easy access
  const isSuperuser = computed(() => permissionsStore.isSuperuser)
  const userRole = computed(() => permissionsStore.userRole)
  const isLoading = computed(() => permissionsStore.isLoading)
  const error = computed(() => permissionsStore.error)

  // Permission check methods
  const hasPermission = (moduleId: ModuleId, permissionType: PermissionType): boolean => {
    return permissionsStore.hasPermission(moduleId, permissionType)
  }

  const canView = (moduleId: ModuleId): boolean => {
    return permissionsStore.canView(moduleId)
  }

  const canEdit = (moduleId: ModuleId): boolean => {
    return permissionsStore.canEdit(moduleId)
  }

  const canCreate = (moduleId: ModuleId): boolean => {
    return permissionsStore.canCreate(moduleId)
  }

  const canList = (moduleId: ModuleId): boolean => {
    return permissionsStore.canList(moduleId)
  }

  const canAccess = (moduleId: ModuleId): boolean => {
    return permissionsStore.canAccess(moduleId)
  }

  // Navigation helpers
  const canAccessTalent = computed(() => canAccess(ModuleId.TALENT))
  const canAccessUsers = computed(() => canAccess(ModuleId.USERS))
  const canAccessRoles = computed(() => canAccess(ModuleId.ROLES))
  const canAccessClients = computed(() => canAccess(ModuleId.CLIENTS))
  const canAccessEquipment = computed(() => canAccess(ModuleId.EQUIPMENT))
  const canAccessHistoryLog = computed(() => canAccess(ModuleId.HISTORY_LOG))
  const canAccessSettings = computed(() => canAccess(ModuleId.SETTINGS))
  const canAccessDashboard = computed(() => canAccess(ModuleId.DASHBOARD))
  const canAccessReports = computed(() => canAccess(ModuleId.REPORTS))
  const canAccessProfile = computed(() => canAccess(ModuleId.PROFILE))
  const canAccessNotifications = computed(() => canAccess(ModuleId.NOTIFICATIONS))
  const canAccessAudit = computed(() => canAccess(ModuleId.AUDIT))
  const canAccessBackup = computed(() => canAccess(ModuleId.BACKUP))
  const canAccessSystem = computed(() => canAccess(ModuleId.SYSTEM))

  // Action helpers for common operations
  const canCreateTalent = computed(() => canCreate(ModuleId.TALENT))
  const canEditTalent = computed(() => canEdit(ModuleId.TALENT))
  const canViewTalent = computed(() => canView(ModuleId.TALENT))

  const canCreateUsers = computed(() => canCreate(ModuleId.USERS))
  const canEditUsers = computed(() => canEdit(ModuleId.USERS))
  const canViewUsers = computed(() => canView(ModuleId.USERS))

  const canCreateRoles = computed(() => canCreate(ModuleId.ROLES))
  const canEditRoles = computed(() => canEdit(ModuleId.ROLES))
  const canViewRoles = computed(() => canView(ModuleId.ROLES))

  // Get accessible modules for navigation
  const getAccessibleModules = () => {
    return permissionsStore.getAccessibleModules()
  }

  // Check if user has any talent permission
  const hasAnyTalentPermission = () => {
    return permissionsStore.hasAnyTalentPermission()
  }

  // Initialize permissions
  const initializePermissions = async () => {
    try {
      await permissionsStore.fetchUserPermissions()
    } catch (error) {
      console.error('Failed to initialize permissions:', error)
    }
  }

  // Refresh permissions (force reload from API)
  const refreshPermissions = async () => {
    try {
      await permissionsStore.refreshUserPermissions()
    } catch (error) {
      console.error('Failed to refresh permissions:', error)
    }
  }

  // Clear permissions (for logout)
  const clearPermissions = () => {
    permissionsStore.clearPermissions()
  }

  return {
    // Store access
    permissionsStore,

    // Computed properties
    isSuperuser,
    userRole,
    isLoading,
    error,

    // Permission check methods
    hasPermission,
    canView,
    canEdit,
    canCreate,
    canList,
    canAccess,

    // Navigation helpers
    canAccessTalent,
    canAccessUsers,
    canAccessRoles,
    canAccessClients,
    canAccessEquipment,
    canAccessHistoryLog,
    canAccessSettings,
    canAccessDashboard,
    canAccessReports,
    canAccessProfile,
    canAccessNotifications,
    canAccessAudit,
    canAccessBackup,
    canAccessSystem,

    // Action helpers
    canCreateTalent,
    canEditTalent,
    canViewTalent,
    canCreateUsers,
    canEditUsers,
    canViewUsers,
    canCreateRoles,
    canEditRoles,
    canViewRoles,

    // Utility methods
    getAccessibleModules,
    hasAnyTalentPermission,
    initializePermissions,
    refreshPermissions,
    clearPermissions
  }
}
