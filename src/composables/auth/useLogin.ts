import { useAuthApis } from "@/apis/auth/auth-apis";
import { useAuthStore } from "@/store/auth/useAuthStore";
import { usePermissionsStore } from "@/store/permissions/usePermissionsStore";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";

export const useLogin = () => {
  const store = useAuthStore();
  const router = useRouter();
  const permissionsStore = usePermissionsStore();

  // Use storeToRefs to make store properties reactive and writable
  const {
    email,
    password,
    rememberMe,
    showPassword,
    isLoading,
    error
  } = storeToRefs(store);

  const handleLogin = async () => {
    store.updateLoading(true);
    store.updateError(false);

    try {
      const response = await useAuthApis().login(email.value, password.value);

      // Store tokens in localStorage
      if (response.access_token) {
        localStorage.setItem("accessToken", response.access_token);
        localStorage.setItem("name", response.name || "");
        localStorage.setItem("email", response.email || "");
        localStorage.setItem("isSuperuser", response.is_superuser?.toString() || "false");
        localStorage.setItem("pic", response.pic || "");

        try {
          // Load permissions before redirecting
          await permissionsStore.fetchUserPermissions();

          // Redirect to dashboard after permissions are loaded
          router.push("/dashboard");
        } catch (permissionError) {
          console.error("Failed to load permissions:", permissionError);
          // Still redirect even if permissions fail to load
          router.push("/dashboard");
        }
      }
    } catch (error) {
      console.error("Login failed:", error);
      store.updateError(true);
    } finally {
      store.updateLoading(false);
    }
  };

  return {
    // Reactive store state (writable)
    email,
    password,
    rememberMe,
    showPassword,
    isLoading,
    error,
    
    // Store actions
    togglePassword: store.togglePassword,
    toggleRememberMe: store.toggleRememberMe,
    
    // Login handler
    handleLogin,
  };
};
