import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { EquipmentForm } from '@/types/equipment'
import { useEquipmentsApi } from '@/apis/master/equipment'

export const useEquipment = () => {
  const route = useRoute()
  const router = useRouter()

  // Form state
  const equipmentForm = ref<EquipmentForm>({
    name: '',
    equipmentId: '',
    category: '',
    serialNumber: '',
    model: '',
    assignedTo: '',
    purchaseDate: '',
  })

  // UI state
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  const showDatePicker = ref(false)
  const labelStates = ref({
    name: false,
    serialNumber: false,
    category: false,
    equipmentId: false,
    model: false,
    purchaseDate: false,
    status: false,
    assignedTo: false
  })

  // Validation errors
  const fieldErrors = ref<Record<string, string>>({
    name: '',
    serialNumber: '',
    category: '',
    equipmentId: '',
    model: '',
    purchaseDate: '',
    status: '',
    assignedTo: ''
  })

  // Computed properties
  const isEditMode = computed(() => {
    return route.params.id !== undefined && route.params.id !== 'add'
  })

  const pageTitle = computed(() => {
    return isEditMode.value ? equipmentForm.value.name || 'Edit Equipment' : 'New Equipment'
  })

  const submitButtonText = computed(() => {
    return isEditMode.value ? 'UPDATE' : 'SUBMIT'
  })

  // Category options
  const categoryOptions = [
    'Monitor',
    'Laptop',
    'Desktop',
    'Keyboard',
    'Mouse',
    'Headset',
    'Webcam',
    'Printer',
    'Other'
  ]

  // Methods
  const setLabelActive = (field: keyof typeof labelStates.value, active: boolean) => {
    labelStates.value[field] = active
  }

  const toggleDatePicker = () => {
    showDatePicker.value = !showDatePicker.value
  }

  const handleDateSelect = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    equipmentForm.value.purchaseDate = `${year}-${month}-${day}`
    showDatePicker.value = false
    fieldErrors.value.purchaseDate = ''
  }

  const clearPurchaseDate = () => {
    equipmentForm.value.purchaseDate = ''
    setLabelActive('purchaseDate', false)
  }

  // Validation methods
  const validateField = (field: string, value: any) => {
    switch (field) {
      case 'name':
        fieldErrors.value.name = !value ? 'Equipment name is required' : ''
        break
      case 'serialNumber':
        fieldErrors.value.serialNumber = !value ? 'Serial number is required' : ''
        break
      case 'category':
        fieldErrors.value.category = !value ? 'Category is required' : ''
        break
      case 'equipmentId':
        fieldErrors.value.equipmentId = !value ? 'Equipment ID is required' : ''
        break
      case 'model':
        // Model is optional, so no validation needed
        fieldErrors.value.model = ''
        break
      case 'purchaseDate':
        fieldErrors.value.purchaseDate = !value ? 'Purchase date is required' : ''
        break
      case 'assignedTo':
        // Assigned To is optional, so no validation needed
        fieldErrors.value.assignedTo = ''
        break
      default:
        break
    }
  }

  const validateForm = () => {
    const requiredFields = [
      'name',
      'serialNumber',
      'category',
      'equipmentId',
      'purchaseDate',
      'status'
    ]

    requiredFields.forEach(field => {
      validateField(field, equipmentForm.value[field as keyof EquipmentForm])
    })

    return Object.values(fieldErrors.value).filter(error => error).length === 0
  }

  const resetForm = () => {
    equipmentForm.value = {
      name: '',
      equipmentId: '',
      category: '',
      serialNumber: '',
      model: '',
      assignedTo: '',
      purchaseDate: ''
    }
    
    // Reset label states
    Object.keys(labelStates.value).forEach(key => {
      labelStates.value[key as keyof typeof labelStates.value] = false
    })

    // Reset validation errors
    Object.keys(fieldErrors.value).forEach(key => {
      fieldErrors.value[key] = ''
    })
  }

  const loadEquipment = async (equipmentId: string) => {
    isLoading.value = true
    try {
      const response = await useEquipmentsApi.getEquipmentById(parseInt(equipmentId))
      if (response.data) {
        const equipment = response.data
        equipmentForm.value = {
          id: equipment.id,
          name: equipment.name,
          equipmentId: equipment.equipmentId,
          category: equipment.category,
          serialNumber: equipment.serialNumber || '',
          model: equipment.model || '',
          assignedTo: equipment.assignedTo,
          purchaseDate: equipment.purchaseDate ? equipment.purchaseDate.split('T')[0] : ''
        }
      }
    } catch (error) {
      console.error('Error loading equipment:', error)
    } finally {
      isLoading.value = false
    }
  }

  const submitEquipment = async () => {
    // Validate form before submission
    if (!validateForm()) {
      console.log('Form validation failed')
      return
    }

    isSubmitting.value = true
    try {
      if (isEditMode.value) {
        // Update existing equipment
        console.log('Updating equipment:', equipmentForm.value)
        await useEquipmentsApi.updateEquipment(parseInt(route.params.id as string), equipmentForm.value)
      } else {
        // Create new equipment
        console.log('Creating equipment:', equipmentForm.value)
        await useEquipmentsApi.createEquipment(equipmentForm.value)
      }
      
      // Navigate back to equipment list
      router.push('/equipment')
    } catch (error) {
      console.error('Error submitting equipment:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  const cancelEdit = () => {
    router.push('/equipment')
  }

  const equipmentList = () => {
    return useEquipmentsApi.getAllEquipments()
  }

  return {
    // State
    equipmentForm,
    isLoading,
    isSubmitting,
    showDatePicker,
    labelStates,
    fieldErrors,
    
    // Computed
    isEditMode,
    pageTitle,
    submitButtonText,
    categoryOptions,
    
    // Methods
    setLabelActive,
    toggleDatePicker,
    handleDateSelect,
    clearPurchaseDate,
    validateField,
    validateForm,
    resetForm,
    loadEquipment,
    submitEquipment,
    cancelEdit,
    equipmentList
  }
}
