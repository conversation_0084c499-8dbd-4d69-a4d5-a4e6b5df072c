import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import type { Equipment } from '@/types/equipment'
import { useEquipmentsApi } from '@/apis/master/equipment'

export const useEquipmentList = () => {
  const router = useRouter()

  // State
  const equipmentList = ref<Equipment[]>([])
  const isLoading = ref(false)
  const searchQuery = ref('')
  const categoryFilter = ref('')

  // Computed properties
  const filteredEquipment = computed(() => {
    let filtered = equipmentList.value

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(equipment => 
        equipment.name.toLowerCase().includes(query) ||
        equipment.equipmentId.toLowerCase().includes(query) ||
        equipment.category.toLowerCase().includes(query) ||
        (equipment.serialNumber && equipment.serialNumber.toLowerCase().includes(query)) ||
        (equipment.model && equipment.model.toLowerCase().includes(query)) ||
        equipment.assignedTo.toLowerCase().includes(query)
      )
    }

    // Filter by category
    if (categoryFilter.value) {
      filtered = filtered.filter(equipment => equipment.category === categoryFilter.value)
    }

    return filtered
  })

  const categories = computed(() => {
    const uniqueCategories = [...new Set(equipmentList.value.map(eq => eq.category))]
    return uniqueCategories.sort()
  })

  // Methods
  const loadEquipment = async () => {
    isLoading.value = true
    try {
      const response = await useEquipmentsApi.getAllEquipments()
      equipmentList.value = response.data
    } catch (error) {
      console.error('Error loading equipment:', error)
    } finally {
      isLoading.value = false
    }
  }

  const handleAddEquipment = () => {
    router.push('/equipment/add')
  }

  const handleEditEquipment = (equipmentId: number) => {
    router.push(`/equipment/${equipmentId}`)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (equipment: Equipment) => {
    if (!equipment.isActive) {
      return { text: 'Inactive', class: 'bg-gray-100 text-gray-800' }
    }
    
    if (equipment.assignedTo && equipment.assignedTo !== 'N/A') {
      return { text: 'Assigned', class: 'bg-green-100 text-green-800' }
    }
    
    return { text: 'Available', class: 'bg-blue-100 text-blue-800' }
  }

  // Initialize
  onMounted(() => {
    loadEquipment()
  })

  return {
    // State
    equipmentList,
    isLoading,
    searchQuery,
    categoryFilter,
    
    // Computed
    filteredEquipment,
    categories,
    
    // Methods
    loadEquipment,
    handleAddEquipment,
    handleEditEquipment,
    formatDate,
    getStatusBadge
  }
}
