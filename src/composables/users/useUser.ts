import { usersApi } from "@/apis/users/users";
import { rolesAndModulesApi } from "@/apis/roles/role";
import { ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";

export const useUser = () => {
  const router = useRouter();
  const route = useRoute();

  // Check if we're in edit mode
  const isEditMode = computed(() => route.name === "EditUser");
  const userId = computed(() => route.params.id as string);

  const form = ref({
    name: "",
    email: "",
    phone: "",
    password: "",
    isSuperuser: false,
    department: "",
    roleId: "",
    pic: "",
  });

  const labelStates = ref({
    name: false,
    email: false,
    phone: false,
    password: false,
    department: false,
    roleId: false,
  });

  const errors = ref({
    name: "",
    email: "",
    phone: "",
    password: "",
    department: "",
    roleId: "",
  });

  // Roles data
  const roles = ref<any[]>([]);

  // Users data
  const users = ref<any[]>([]);

  const profilePicture = ref("");
  const profilePictureInput = ref<HTMLInputElement>();

  // Watch for isSuperuser changes to clear department and role
  watch(
    () => form.value.isSuperuser,
    (newValue) => {
      if (newValue) {
        form.value.department = "";
        form.value.roleId = "";
        errors.value.department = "";
        errors.value.roleId = "";
      }
    }
  );

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""));
  };

  const validatePassword = (password: string): boolean => {
    return password.length >= 8;
  };

  const validateForm = (): boolean => {
    let isValid = true;

    // Reset errors
    Object.keys(errors.value).forEach((key) => {
      errors.value[key as keyof typeof errors.value] = "";
    });

    // Validate name
    if (!form.value.name.trim()) {
      errors.value.name = "Full name is required";
      isValid = false;
    }

    // Validate email
    if (!form.value.email.trim()) {
      errors.value.email = "Email is required";
      isValid = false;
    } else if (!validateEmail(form.value.email)) {
      errors.value.email = "Please enter a valid email address";
      isValid = false;
    }

    // Validate phone number
    if (!form.value.phone.trim()) {
      errors.value.phone = "Phone number is required";
      isValid = false;
    } else if (!validatePhoneNumber(form.value.phone)) {
      errors.value.phone = "Please enter a valid phone number";
      isValid = false;
    }

    // Validate password
    if (form.value.password && !validatePassword(form.value.password)) {
      errors.value.password = "Password must be at least 8 characters long";
      isValid = false;
    }

    // Validate department and role only if not superuser
    if (!form.value.isSuperuser) {
      if (!form.value.department.trim()) {
        errors.value.department = "Department is required";
        isValid = false;
      }

      if (!form.value.roleId) {
        errors.value.roleId = "Role is required";
        isValid = false;
      }
    }

    return isValid;
  };

  const goBack = () => {
    router.back();
  };

  const triggerProfilePictureUpload = () => {
    profilePictureInput.value?.click();
  };

  const handleProfilePictureUpload = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        profilePicture.value = result;
        form.value.pic = result;
      };
      reader.readAsDataURL(file);
    }
  };

  // Get roles from API
  const getRoles = async () => {
    try {
      const response = await rolesAndModulesApi.getRoles();
      roles.value = response; // Response is already the roles array
      return response;
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    }
  };

  // Get users from API
  const getUsers = async () => {
    try {
      const response = await usersApi.getUsers();
      users.value = response.data;
      return response;
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    }
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    if (isEditMode.value) {
      usersApi.updateUser(parseInt(userId.value), {
        ...form.value,
        roleId: form.value.isSuperuser ? null : form.value.roleId, // Convert roleId to null if superuser
        department: form.value.isSuperuser ? null : form.value.department, // Convert department to null if superuser
      });
    } else {
      usersApi.createUser({
        ...form.value,
        roleId: form.value.isSuperuser ? null : form.value.roleId, // Convert roleId to null if superuser
        department: form.value.isSuperuser ? null : form.value.department, // Convert department to null if superuser
      });
    }

    // Navigate back to users list
    router.push("/users");
  };

  const loadUser = async () => {
    try {
      const response = await usersApi.getUserById(parseInt(userId.value));
      // Map the User response to the form structure
      form.value = {
        name: response.data.name || "",
        email: response.data.email || "",
        phone: response.data.phone || "",
        password: "", // Don't populate password for security reasons
        isSuperuser: response.data.isSuperuser || false,
        department: response.data.department || "",
        roleId: response.data.roleId ? String(response.data.roleId) : "", // Convert to string and handle camelCase
        pic: response.data.pic || "",
      };
      return response;
    } catch (error) {
      console.error("Error fetching user:", error);
    }
  };

  return {
    form,
    labelStates,
    errors,
    isEditMode,
    userId,
    profilePicture,
    profilePictureInput,
    roles,
    users,
    setLabelActive,
    goBack,
    triggerProfilePictureUpload,
    handleProfilePictureUpload,
    handleSubmit,
    getRoles,
    getUsers,
    loadUser,
  };
};
