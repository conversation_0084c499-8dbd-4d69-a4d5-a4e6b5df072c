import type { JobRelatedInfoCreate } from '@/types/job-related-info'
import { formatDateForInput, createDateRangeString } from '@/utils/date-formatting'

/**
 * Business logic for work history operations
 */
export class WorkHistoryService {
  /**
   * Populates form with work history data for editing
   */
  static populateFormForEdit(form: JobRelatedInfoCreate, workData: JobRelatedInfoCreate): void {
    form.id = workData.id
    form.campaign = workData.campaign || ''
    form.clientManager = workData.clientManager || ''
    form.reportingDepartment = workData.reportingDepartment || ''
    form.role = workData.role || ''
    form.title = workData.title || ''
    form.clientId = workData.clientId || 0
    form.location = workData.location || ''
    form.startDate = formatDateForInput(workData.startDate)
    form.endDate = formatDateForInput(workData.endDate)
    form.workDays = workData.workDays || []
    form.shiftStartTime = workData.shiftStartTime || ''
    form.shiftEndTime = workData.shiftEndTime || ''
    form.timeZone = workData.timeZone || ''
    form.notes = workData.notes || ''
    form.isFte = workData.isFte || false
    form.isContract = workData.isContract || false
    form.isOther = workData.isOther || false
    form.positionType = workData.isFte ? 'FTE' : workData.isContract ? 'Contract' : 'Other'
  }

  /**
   * Resets form to initial state
   */
  static resetForm(form: JobRelatedInfoCreate): void {
    Object.keys(form).forEach((key) => {
      if (key === 'workDays') {
        (form as any)[key] = []
      } else if (
        key === 'talentProfileId' ||
        key === 'isFte' ||
        key === 'isContract' ||
        key === 'isOther' ||
        key === 'positionType'
      ) {
        // Keep these as they are
      } else {
        (form as any)[key] = ''
      }
    })
  }

  /**
   * Creates a work history entry for display
   */
  static createWorkHistoryEntry(formData: JobRelatedInfoCreate) {
    return {
      company: formData.clientId,
      position: formData.title,
      dateRange: createDateRangeString(formData.startDate, formData.endDate),
      description: formData.notes || undefined,
      role: formData.role,
      isFte: formData.isFte,
      isContract: formData.isContract,
    }
  }

  /**
   * Determines position type display text
   */
  static getPositionTypeDisplay(work: JobRelatedInfoCreate): string {
    if (work.isFte) return 'FTE'
    if (work.isContract) return 'Contract'
    return 'Other'
  }

  /**
   * Converts API response object to array if needed
   */
  static normalizeWorkHistoryData(data: any): JobRelatedInfoCreate[] {
    if (!data) return []
    
    if (Array.isArray(data)) {
      return data
    }
    
    // Convert object with numeric keys to array
    if (typeof data === 'object') {
      return Object.values(data)
    }
    
    return []
  }
}
