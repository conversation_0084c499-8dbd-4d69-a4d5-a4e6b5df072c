export interface EmergencyContact {
  id?: number
  talent_profile_id?: number
  firstName: string
  middleName: string
  lastName: string
  countryCode: string
  phone: string
  email: string
  relationship: string
}

export interface EmergencyContactSnakeCase {
  id?: number
  talent_profile_id?: number
  first_name: string
  middle_name: string
  last_name: string
  country_code: string
  phone: string
  email: string
  relationship: string
}

export interface FormData {
  firstName: string
  middleName: string
  lastName: string
  primaryContact: string
  secondaryContact: string
  personalEmail: string
  dateOfBirth: string
  gender: string
  address1: string
  aptUnit: string
  address2: string
  postalCode: string
  city: string
  state: string
  country: string
  imms: string
  curp: string
  rfc: string
  pic?: string
  primaryCountryCode: string
  secondaryCountryCode: string
  emergencyContacts: EmergencyContact[]
  skills: string[]
  writtenEnglish: string
  spokenEnglish: string
  englishType: string
  yearsOfExperience: number | null
  notes: string
  uploadedFiles: File[]
}

export interface TalentProfileCreate {
  firstName: string
  middleName?: string
  lastName: string
  personalEmail: string
  countryCode?: string
  phone?: string
  countryCode2?: string
  phone2?: string
  dateOfBirth?: string
  gender?: string
  pic?: string
  apt_unit?: string
  address_1: string
  address_2?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  is_active?: boolean
}

export interface TalentProfileResponse extends TalentProfileCreate {
  id: number
}

export interface GeneralResponse<T = any> {
  status_code: number
  message: string
  data: T
}

export interface TalentSecurityCreate {
  talent_profile_id?: number
  imms?: string
  curp?: string
  rfc?: string
}

export interface TalentSkillSetCreate {
  talentProfileId?: number
  skills?: string[]
  yearsOfExperience?: number
  notesFromInterview?: string
  englishLevel?: {
    write: string
    speak: string
    type: string
  }
}


export interface Talent {
  id: number
  firstName: string
  lastName: string
  middleName: string
  pic: string
  phone: string
  date: string
  position: string
  createdAt: string
  supervisor: {
    name: string
    pic: string
  }
  isActive: boolean
}

export interface TalentProfileResponse {
  id: number
  talentName: string
  phone: string
  startDate: string
  jobPosition: string
  supervisor: string
  status: boolean
}

export interface TalentStatusUpdate {
  action: string
  reason: string
}
