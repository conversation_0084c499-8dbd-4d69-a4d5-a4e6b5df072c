export interface JobRelatedInfoCreate {
  id?: number
  talentProfileId: number
  isFte?: boolean
  isContract?: boolean
  isOther?: boolean
  campaign: string
  clientManager: string
  reportingDepartment: string
  positionType: string
  role: string
  title: string
  clientId: number
  location: string
  startDate: string
  endDate: string
  workDays: string[]
  shiftStartTime: string
  shiftEndTime: string
  timeZone: string
  baseWage: string
  currency: string
  frequency: string
  paidDaysOff: string
  vacationsUsed: string
  remainingVacations: string
  notes?: string
}
