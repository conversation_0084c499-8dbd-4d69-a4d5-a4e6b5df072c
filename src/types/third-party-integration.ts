/**
 * Third Party Integration types for talent management
 */

export interface ThirdPartyIntegrationBase {
  talentProfileId: number
  thirdParty: string
  data: string
  status?: string // Managed by backend cron
  isActive: boolean
  jsonData?: string
}

export interface ThirdPartyIntegrationCreate extends Omit<ThirdPartyIntegrationBase, 'status'> {}

export interface ThirdPartyIntegrationUpdate {
  thirdParty?: string
  data?: string
  isActive?: boolean
  jsonData?: string
}

export interface ThirdPartyIntegrationResponse extends ThirdPartyIntegrationBase {
  id: number
  createdAt: string
  updatedAt?: string
}

export interface ThirdPartyIntegrationDisplay {
  id: number
  name: IntegrationType
  isActive: boolean
  status?: string // Backend managed status
  description: string
  data?: string
  createdAt?: string
  updatedAt?: string
}

// Predefined integration types
export const INTEGRATION_TYPES = {
  ZOHO_MAIL: 'Zoho Mail',
  JIBBLE: 'Jibble',
  ACTIVTRAK: 'ActivTrak'
} as const

export type IntegrationType = typeof INTEGRATION_TYPES[keyof typeof INTEGRATION_TYPES]

// Integration descriptions
export const INTEGRATION_DESCRIPTIONS = {
  [INTEGRATION_TYPES.ZOHO_MAIL]: 'Email integration for communication',
  [INTEGRATION_TYPES.JIBBLE]: 'Time tracking and attendance management',
  [INTEGRATION_TYPES.ACTIVTRAK]: 'Employee monitoring and productivity tracking'
} as const
