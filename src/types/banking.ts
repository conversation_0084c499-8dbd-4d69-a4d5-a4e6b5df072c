export interface BankingInformation {
  id?: number
  talentProfileId: number
  bankName: string
  accountNumber: string
  accountType: string
  clabe: string
  swiftCode?: string
  createdAt?: string
  updatedAt?: string
}

export interface BankingInformationCreate {
  talentProfileId: number
  bankName: string
  accountNumber: string
  clabe: string
  swiftCode?: string
}

export interface BankingInformationUpdate {
  bankName?: string
  accountNumber?: string
  accountType?: string
  clabe?: string
  swiftCode?: string
}
