export interface PersonalInfo {
  name: string
  email: string
  phone: string
}

export interface PasswordForm {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface SettingsErrors {
  name: string
  email: string
  phone: string
}

export interface SettingsLabelStates {
  name: boolean
  email: boolean
  phone: boolean
}

export interface UpdatePersonalInfoRequest {
  name: string
  phone: string
}

export interface UpdatePasswordRequest {
  oldPassword: string
  newPassword: string
}

export interface SettingsResponse {
  name: string
  email: string
  phone: string
}
