export interface ClientForm {
  id?: number
  name: string
  clientId: string
  startDate: string
  endDate: string
  status: 'Active' | 'Inactive'
  empCount?: number
  
  // Address
  streetAddress: string
  city: string
  zipCode: string
  country: string
  
  // Contact Manager
  contactManager: string
  contactManagerEmail: string
  contactManagerPhone: string
  
  // Notes & Attachments
  notes: string
  attachments: File[]
  isActive?: boolean
  
}
