export interface Equipment {
  id?: number
  createdAt?: string
  updatedAt?: string
  name: string
  equipmentId: string
  category: string
  serialNumber: string | null
  model: string | null
  assignedTo: string
  purchaseDate: string
  isActive?: boolean
}

export interface EquipmentForm {
  id?: number
  name: string
  equipmentId: string
  category: string
  serialNumber: string
  model: string
  assignedTo: string
  purchaseDate: string
}
