export interface HistoryLogDescription {
  field: string
  value: string
  type?: string
  label?: string
}

export interface HistoryLog {
  id: number
  date: string
  time: string
  event: string
  user: string
  userRole?: string
  descriptions: HistoryLogDescription[]
  status: 'Succeeded' | 'Error'
}

export interface HistoryLogFilters {
  searchQuery: string
  startDate: Date
  endDate: Date
  eventType?: string
  status?: 'Succeeded' | 'Error' | 'all'
  user?: string
}

export interface HistoryLogResponse {
  id: number
  createdAt: string
  updatedAt: string
  event: string
  user: string
  userRole?: string
  descriptions: HistoryLogDescription[]
  status: 'Succeeded' | 'Error'
  talentProfileId?: number
  userId?: number
}

export interface HistoryLogCreate {
  event: string
  user: string
  userRole?: string
  descriptions: HistoryLogDescription[]
  status: 'Succeeded' | 'Error'
  talentProfileId?: number
  userId?: number
}

export interface GeneralResponse<T = any> {
  status_code: number
  message: string
  data: T
}
