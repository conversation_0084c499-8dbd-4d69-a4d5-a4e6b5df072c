export interface ModulePermission {
  module_id: number
  view: boolean
  edit: boolean
  create: boolean
  list: boolean
}

export interface UserRole {
  id: number
  role: string
  description: string
  module: ModulePermission[]
}

export interface UserPermissions {
  is_superuser: boolean
  permissions: UserRole
}

export interface PermissionState {
  userPermissions: UserPermissions | null
  isLoading: boolean
  error: string | null
}

// Module IDs mapping based on your actual modules
export enum ModuleId {
  TALENT_PROFILE = 1,
  TALENT_BANKING_INFO = 2,
  TALENT_DOCUMENTS = 3,
  TALENT_WORK_HISTORY = 4,
  TALENT_BPO_RELATED_INFO = 5,
  TALENT_EQUIPMENT_SOFTWARE = 6,
  TALENT_HEALTH = 7,
  TALENT_3RD_PARTY_INTEGRATIONS = 8,
  TALENT_HISTORY_LOGS = 9,
  USERS = 10,
  ROLES = 11,
  CLIENTS = 12,
  EQUIPMENT = 13,
  SYSTEM_HISTORY_LOGS = 14
}

// Helper to check if a module is talent-related
export const isTalentModule = (moduleId: ModuleId): boolean => {
  return moduleId >= ModuleId.TALENT_PROFILE && moduleId <= ModuleId.TALENT_HISTORY_LOGS
}

// Helper to check if a module is history log related
export const isHistoryLogModule = (moduleId: ModuleId): boolean => {
  return moduleId === ModuleId.TALENT_HISTORY_LOGS || moduleId === ModuleId.SYSTEM_HISTORY_LOGS
}

// Permission types
export type PermissionType = 'view' | 'edit' | 'create' | 'list'

// Helper type for checking permissions
export interface PermissionCheck {
  moduleId: ModuleId
  permission: PermissionType
}
