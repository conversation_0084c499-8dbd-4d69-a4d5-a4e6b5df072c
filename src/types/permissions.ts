export interface ModulePermission {
  module_id: number
  view: boolean
  edit: boolean
  create: boolean
  list: boolean
}

export interface UserRole {
  id: number
  role: string
  description: string
  module: ModulePermission[]
}

export interface UserPermissions {
  is_superuser: boolean
  permissions: UserRole
}

export interface PermissionState {
  userPermissions: UserPermissions | null
  isLoading: boolean
  error: string | null
}

// Module IDs mapping (you can adjust these based on your actual module IDs)
export enum ModuleId {
  TALENT = 1,
  USERS = 2,
  ROLES = 3,
  CLIENTS = 4,
  EQUIPMENT = 5,
  HISTORY_LOG = 6,
  SETTINGS = 7,
  DASHBOARD = 8,
  REPORTS = 9,
  PROFILE = 10,
  NOTIFICATIONS = 11,
  AUDIT = 12,
  BACKUP = 13,
  SYSTEM = 14
}

// Permission types
export type PermissionType = 'view' | 'edit' | 'create' | 'list'

// Helper type for checking permissions
export interface PermissionCheck {
  moduleId: ModuleId
  permission: PermissionType
}
