export interface Role {
  id: number;
  name: string;
  description: string;
}

export interface RolePermission {
  id: number;
  name: string;
  description: string;
  module: ModulePermission[];
}

export interface Module {
  id: number;
  name: string;
  isActive: boolean;
}

export interface ModulePermission {
  name: string;
  permissions: {
    id?: number;
    view: boolean;
    edit: boolean;
    create: boolean;
    list: boolean;
  };
}

export interface RolePermissionRequest {
  name: string;
  description: string;
  module: ModulePermissionRequest[];
}

export interface ModulePermissionRequest {
  module_id: number;
  view: boolean;
  edit: boolean;
  create: boolean;
  list: boolean;
}
