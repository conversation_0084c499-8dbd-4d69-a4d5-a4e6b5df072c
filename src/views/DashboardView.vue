<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p class="text-gray-600">Welcome to the BPO Management System</p>
    </div>

    <!-- User Info Card -->
    <div class="mb-6 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">User Information</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Name</label>
          <p class="text-gray-900">{{ userName || 'Not available' }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Email</label>
          <p class="text-gray-900">{{ userEmail || 'Not available' }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Role</label>
          <p class="text-gray-900">
            <span v-if="isSuperuser" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Superuser
            </span>
            <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ userRole?.role || 'No role assigned' }}
            </span>
          </p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Status</label>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Active
          </span>
        </div>
      </div>
    </div>

    <!-- Available Modules -->
    <div class="mb-6 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Available Modules</h2>
      <div v-if="accessibleModules.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <router-link
          v-for="module in moduleLinks"
          :key="module.id"
          :to="module.path"
          class="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:shadow-md transition-all duration-200 group"
        >
          <div class="flex items-center space-x-3">
            <component :is="module.icon" :size="24" class="text-gray-600 group-hover:text-orange-600" />
            <div>
              <h3 class="font-medium text-gray-900 group-hover:text-orange-900">{{ module.name }}</h3>
              <p class="text-sm text-gray-500">{{ module.description }}</p>
            </div>
          </div>
        </router-link>
      </div>
      <div v-else class="text-center py-8">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <ShieldX :size="32" class="text-gray-400" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Modules Available</h3>
        <p class="text-gray-500">You don't have access to any modules. Please contact your administrator.</p>
      </div>
    </div>

    <!-- Quick Actions -->
    <div v-if="isSuperuser" class="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions (Superuser)</h2>
      <div class="flex flex-wrap gap-3">
        <router-link
          to="/settings"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <Settings :size="16" class="mr-2" />
          Settings
        </router-link>
        <router-link
          to="/talent"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <Users :size="16" class="mr-2" />
          Manage Talent
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'
import { Users, UserCheck, Shield, Building2, Wrench, History, Settings, ShieldX } from 'lucide-vue-next'

const {
  isSuperuser,
  userRole,
  getAccessibleModules,
  initializePermissions
} = usePermissions()

// Get user info from localStorage
const userName = localStorage.getItem('name')
const userEmail = localStorage.getItem('email')

const accessibleModules = computed(() => getAccessibleModules())

const moduleLinks = computed(() => {
  const moduleMap = [
    {
      id: ModuleId.TALENT,
      name: 'Talent Management',
      description: 'Manage talent profiles and information',
      path: '/talent',
      icon: Users
    },
    {
      id: ModuleId.USERS,
      name: 'User Management',
      description: 'Manage system users',
      path: '/users',
      icon: UserCheck
    },
    {
      id: ModuleId.ROLES,
      name: 'Role Management',
      description: 'Manage user roles and permissions',
      path: '/roles',
      icon: Shield
    },
    {
      id: ModuleId.CLIENTS,
      name: 'Client Management',
      description: 'Manage client information',
      path: '/clients',
      icon: Building2
    },
    {
      id: ModuleId.EQUIPMENT,
      name: 'Equipment Management',
      description: 'Manage equipment and assets',
      path: '/equipment',
      icon: Wrench
    },
    {
      id: ModuleId.HISTORY_LOG,
      name: 'History Logs',
      description: 'View system history and logs',
      path: '/history-log',
      icon: History
    }
  ]

  return moduleMap.filter(module => accessibleModules.value.includes(module.id))
})

onMounted(async () => {
  // Ensure permissions are loaded
  await initializePermissions()
})
</script>

<style scoped>
/* Add any additional styling if needed */
</style>
