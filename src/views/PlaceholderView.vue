<template>
  <div class="text-center py-12">
    <div class="max-w-md mx-auto">
      <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <component :is="getIcon()" :size="32" class="text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">{{ getTitle() }}</h3>
      <p class="text-gray-600 mb-6">This section is under development and will be available soon.</p>
      <button class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
        Coming Soon
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { Users, Shield, Building2, Wrench, History } from 'lucide-vue-next'

const route = useRoute()

const getTitle = () => {
  switch (route.name) {
    case 'Roles': return 'Roles Management'
    case 'Clients': return 'Clients Management'
    case 'Equipment': return 'Equipment Management'
    case 'HistoryLog': return 'History Log'
    default: return 'Coming Soon'
  }
}

const getIcon = () => {
  switch (route.name) {
    case 'Roles': return Shield
    case 'Clients': return Building2
    case 'Equipment': return Wrench
    case 'HistoryLog': return History
    default: return Users
  }
}
</script>