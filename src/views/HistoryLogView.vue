<template>
  <div class="px-4 py-6 bg-white md:px-6 lg:px-8">
    <!-- Search and Filters -->
    <div class="mb-6">
      <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <!-- Search -->
        <div class="flex flex-col flex-1 gap-4 sm:flex-row sm:items-center">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 w-4 h-4 text-gray-400 transform -translate-y-1/2" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search Here"
              class="py-2 pr-4 pl-10 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:w-auto"
            />
          </div>
        </div>

        <!-- Date Pickers -->
        <div class="flex items-center space-x-4">
          <div class="relative calendar-container">
            <button 
              @click="toggleStartDatePicker"
              class="flex items-center px-4 py-2 bg-white rounded-lg border border-gray-300 shadow-sm transition-colors hover:bg-gray-50"
            >
              <Calendar :size="16" class="mr-2 text-orange-500" />
              <span class="text-sm">{{ formatDate(startDate) }}</span>
            </button>
            <CalendarComponent 
              :is-open="showStartDatePicker"
              :selected-date="startDate"
              @select-date="updateStartDate"
              @close="showStartDatePicker = false"
            />
          </div>
          <div class="relative calendar-container">
            <button 
              @click="toggleEndDatePicker"
              class="flex items-center px-4 py-2 bg-white rounded-lg border border-gray-300 shadow-sm transition-colors hover:bg-gray-50"
            >
              <Calendar :size="16" class="mr-2 text-orange-500" />
              <span class="text-sm">{{ formatDate(endDate) }}</span>
            </button>
            <CalendarComponent 
              :is-open="showEndDatePicker"
              :selected-date="endDate"
              :position="'right'"
              @select-date="updateEndDate"
              @close="showEndDatePicker = false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- History Log Table -->
    <div class="overflow-hidden mb-6 bg-white rounded-lg border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full min-w-[1200px]">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th class="px-4 py-3 w-32 text-xs font-medium tracking-wider text-left text-gray-500 uppercase md:px-6">DATE & TIME</th>
            <th class="px-4 py-3 w-24 text-xs font-medium tracking-wider text-left text-gray-500 uppercase md:px-6">EVENT</th>
            <th class="px-4 py-3 w-40 text-xs font-medium tracking-wider text-left text-gray-500 uppercase md:px-6">USER</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase md:px-6">DESCRIPTION</th>
            <th class="px-4 py-3 w-32 text-xs font-medium tracking-wider text-left text-gray-500 uppercase md:px-6">STATUS</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="log in filteredLogs" :key="log.id" class="hover:bg-gray-50">
            <!-- Date & Time -->
            <td class="px-4 py-4 w-32 text-sm text-gray-900 md:px-6">
              <div>{{ log.date }}</div>
              <div>{{ log.time }}</div>
            </td>

            <!-- Event -->
            <td class="px-4 py-4 w-24 text-sm font-medium text-gray-900 md:px-6">
              {{ log.event }}
            </td>

            <!-- User -->
            <td class="px-4 py-4 w-40 text-sm md:px-6">
              <div class="font-medium text-gray-900">{{ log.user }}</div>
              <div v-if="log.userRole" class="text-xs text-gray-500">{{ log.userRole }}</div>
            </td>

            <!-- Description -->
            <td class="px-4 py-4 pr-8 text-sm md:px-6">
              <div class="space-y-1">
                <div 
                  v-for="(desc, index) in log.descriptions" 
                  :key="index"
                  class="space-y-0.5"
                >
                  <div class="flex items-start">
                    <span class="mr-1 font-medium text-gray-900">{{ desc.field }}:</span>
                    <span :class="desc.type === 'email' ? 'text-blue-600' : 'text-gray-600'">{{ desc.value }}</span>
                  </div>
                  <div v-if="desc.label" class="text-xs font-medium text-orange-500">{{ desc.label }}</div>
                </div>
              </div>
            </td>

            <!-- Status -->
            <td class="px-4 py-4 w-32 md:px-6">
              <span 
                :class="[
                  'inline-flex px-3 py-1 text-xs font-medium rounded-full',
                  log.status === 'Succeeded' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                ]"
              >
                {{ log.status }}
              </span>
            </td>
          </tr>
        </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft class="w-4 h-4" />
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Search, Calendar, ChevronLeft, ChevronRight } from 'lucide-vue-next'
import CalendarComponent from '@/components/Calendar.vue'

const searchQuery = ref('')
const currentPage = ref(1)
const itemsPerPage = 10
const startDate = ref(new Date(2020, 0, 1)) // Jan 2020
const endDate = ref(new Date(2020, 1, 1)) // Feb 2020
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)

interface HistoryLog {
  id: number
  date: string
  time: string
  event: string
  user: string
  userRole?: string
  descriptions: Array<{
    field: string
    value: string
    type?: string
    label?: string
  }>
  status: 'Succeeded' | 'Error'
}

const historyLogs = ref<HistoryLog[]>([
  {
    id: 1,
    date: '05/03/2025',
    time: '21:37',
    event: 'Update',
    user: 'Brian Matlock',
    userRole: 'Admin',
    descriptions: [
      { field: 'Users / Jane Smith', value: 'Active', label: 'Before' },
      { field: 'Users / Jane Smith', value: 'Deactive', label: 'After' }
    ],
    status: 'Succeeded'
  },
  {
    id: 2,
    date: '05/03/2025',
    time: '20:00',
    event: 'Delete',
    user: 'Gerardo Urias',
    userRole: 'Admin',
    descriptions: [
      { field: 'Talent / Eric Canseco / Documents / Documents Collected (5)', value: 'Birth Certificate', label: 'Before' },
      { field: 'Talent / Eric Canseco / Documents / Documents Collected (4)', value: '', label: 'After' }
    ],
    status: 'Succeeded'
  },
  {
    id: 3,
    date: '05/05/2025',
    time: '13:10',
    event: 'Update',
    user: 'William Brown',
    userRole: 'Editor',
    descriptions: [
      { field: 'Talent / Eric Canseco / Profile / Personal Security Info / CURP', value: 'X234FSDF234234', label: 'Before' },
      { field: 'Talent / Eric Canseco / Profile / Personal Security Info / CURP', value: 'X234FSDF234234', label: 'After' }
    ],
    status: 'Error'
  },
  {
    id: 4,
    date: '05/03/2020',
    time: '20:00',
    event: 'Add',
    user: 'Jessica Taylor',
    userRole: 'Editor',
    descriptions: [
      { field: 'Talent / Eric Canseco / Profile / Emergency Contact (1)', value: '', label: 'Before' },
      { field: 'Talent / Eric Canseco / Profile / Emergency Contact (2)', value: 'Luke Bishop, Brother, 664 419 5614, <EMAIL>', label: 'After' }
    ],
    status: 'Succeeded'
  }
])

const filteredLogs = computed(() => {
  return historyLogs.value.filter(log => {
    const matchesSearch = log.user.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         log.event.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         log.descriptions.some(desc => 
                           desc.field.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                           desc.value.toLowerCase().includes(searchQuery.value.toLowerCase())
                         )
    
    return matchesSearch
  })
})

const totalPages = computed(() => {
  return Math.ceil(filteredLogs.value.length / itemsPerPage)
})

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric'
  })
}

const toggleStartDatePicker = () => {
  showStartDatePicker.value = !showStartDatePicker.value
  showEndDatePicker.value = false
}

const toggleEndDatePicker = () => {
  showEndDatePicker.value = !showEndDatePicker.value
  showStartDatePicker.value = false
}

const updateStartDate = (date: Date) => {
  startDate.value = date
  showStartDatePicker.value = false
}

const updateEndDate = (date: Date) => {
  endDate.value = date
  showEndDatePicker.value = false
}

const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.calendar-container')) {
    showStartDatePicker.value = false
    showEndDatePicker.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
