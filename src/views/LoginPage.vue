<script setup lang="ts">
import { ref } from "vue";
import { <PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON><PERSON>, Loader } from "lucide-vue-next";
import { useLogin } from "@/composables/auth/useLogin";

const {
  email,
  password,
  rememberMe,
  showPassword,
  isLoading,
  error,
  togglePassword,
  toggleRememberMe,
  handleLogin,
} = useLogin();

// Focus states for floating labels
const emailFocused = ref(false);
const passwordFocused = ref(false);
</script>

<template>
  <div class="flex min-h-screen">
    <!-- Left Side - Login Form -->
    <div class="flex flex-1 justify-center items-center px-8 bg-gray-50">
      <div class="w-full max-w-md">
        <!-- Login Header -->
        <div class="mb-8">
          <h1 class="mb-2 text-2xl font-semibold text-gray-900">Login</h1>
          <p class="text-sm text-gray-600">Please enter your email</p>
          <p v-if="error" class="mt-2 text-sm text-red-600">
            Incorrect email or password
          </p>
        </div>

        <!-- Login Form -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- Email Field -->
          <div class="floating-label-container">
            <input
              id="email"
              v-model="email"
              type="email"
              placeholder=" "
              class="floating-input"
              :class="{ active: email || emailFocused }"
              @focus="emailFocused = true"
              @blur="emailFocused = false"
              required
            />
            <label
              for="email"
              class="floating-label"
              :class="{ active: email || emailFocused }"
            >
              Email
            </label>
          </div>

          <!-- Password Field -->
          <div class="floating-label-container">
            <div class="relative">
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                placeholder=" "
                class="pr-10 floating-input"
                :class="{ active: password || passwordFocused }"
                @focus="passwordFocused = true"
                @blur="passwordFocused = false"
                required
              />
              <label
                for="password"
                class="floating-label"
                :class="{ active: password || passwordFocused }"
              >
                Password
              </label>
              <button
                type="button"
                @click="togglePassword"
                class="flex absolute inset-y-0 right-0 items-center pr-3"
              >
                <Eye
                  v-if="!showPassword"
                  :size="16"
                  class="text-gray-400 hover:text-gray-600"
                />
                <EyeOff
                  v-else
                  :size="16"
                  class="text-gray-400 hover:text-gray-600"
                />
              </button>
            </div>
          </div>

          <!-- Remember Me & Forgot Password -->
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                type="checkbox"
                class="w-4 h-4 text-orange-500 rounded border-gray-300 focus:ring-orange-500"
                @click="toggleRememberMe"
              />
              <label for="remember-me" class="block ml-2 text-sm text-gray-600">
                Remember Me
              </label>
            </div>
            <button
              type="button"
              class="text-sm text-gray-600 hover:text-gray-800"
            >
              Forgot Password?
            </button>
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            :disabled="isLoading"
            class="flex justify-center items-center px-8 py-3 w-full text-sm font-medium text-white bg-gray-800 rounded-md transition-colors hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Loader v-if="isLoading" :size="16" class="mr-2 animate-spin" />
            <span>LOGIN</span>
          </button>
        </form>
      </div>
    </div>

    <!-- Right Side - BPO Branding -->
    <div class="flex flex-1 justify-center items-center bg-gray-600">
      <div class="text-center text-white">
        <!-- BPO Title -->
        <h1 class="mb-1 text-7xl font-bold tracking-tight">BPO</h1>
        <!-- Solutions Group with Gear Icon -->
        <div class="text-white">
          <div
            class="flex justify-center items-center text-base font-normal tracking-widest"
          >
            <span>S</span>
            <Settings :size="16" class="mx-0.5" />
            <span>LUTIONS GROUP</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
