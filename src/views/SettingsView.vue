<script setup lang="ts">
import { onMounted } from 'vue'
import { Eye, EyeOff } from 'lucide-vue-next'
import { useSettings } from '@/composables/settings/useSettings'

const {
  // State
  personalInfo,
  passwordForm,
  errors,
  labelStates,
  showCurrentPassword,
  showNewPassword,
  showConfirmPassword,
  isLoadingPersonalInfo,
  isLoadingPassword,
  isLoadingSettings,

  // Computed
  isPasswordFormValid,

  // Methods
  setLabelActive,
  loadUserSettings,
  updatePersonalInfo,
  updatePassword,
} = useSettings()

// Load user settings on component mount
onMounted(async () => {
  await loadUserSettings()
})

// Profile Picture Functions - COMMENTED OUT
/*
const triggerProfilePictureUpload = () => {
  profilePictureInput.value?.click()
}

const handleProfilePictureUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Create preview URL
    const reader = new FileReader()
    reader.onload = (e) => {
      currentProfilePicture.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    // Reset input
    target.value = ''
  }
}

const removeProfilePicture = () => {
  // Reset to default empty profile picture
  currentProfilePicture.value = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjQzRDNEM0Ci8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzciIHI9IjE1IiBmaWxsPSIjOUM5QzlDIi8+CjxwYXRoIGQ9Ik0yMCA4MEMyMCA2OS41MDY2IDI4LjUwNjYgNjEgMzkgNjFINjFDNzEuNDkzNCA2MSA4MCA2OS41MDY2IDgwIDgwVjEwMEgyMFY4MFoiIGZpbGw9IiM5QzlDOUMiLz4KPC9zdmc+'
}
*/
</script>

<template>
  <div class="px-4 py-6 bg-white md:px-6 lg:px-8">
    <!-- Page Title -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-900">Settings</h1>
      <p class="mt-1 text-sm text-gray-500">Manage your account settings and preferences</p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingSettings" class="flex justify-center items-center py-12">
      <div class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"></div>
    </div>

    <div v-else class="space-y-8 max-w-4xl">
      <!-- Section 1: Personal Information -->
      <div class="material-section">
        <h3 class="mb-6 text-lg font-medium text-gray-900">Personal Information</h3>
        
        <!-- Profile Picture Section - COMMENTED OUT -->
        <!--
        <div class="flex items-center mb-8 space-x-6">
          <div class="relative">
            <img
              :src="currentProfilePicture"
              alt="Profile Picture"
              class="object-cover w-24 h-24 rounded-full border-4 border-gray-200"
            >
            <button
              @click="removeProfilePicture"
              class="flex absolute -top-2 -right-2 justify-center items-center w-8 h-8 text-white bg-red-500 rounded-full transition-colors hover:bg-red-600"
            >
              <X :size="16" />
            </button>
          </div>

          <div class="flex-1">
            <input
              ref="profilePictureInput"
              type="file"
              @change="handleProfilePictureUpload"
              class="hidden"
              accept="image/*"
            />
            <div class="space-y-3">
              <button
                @click="triggerProfilePictureUpload"
                class="flex items-center px-4 py-2 text-white bg-gray-600 rounded-lg transition-colors hover:bg-gray-700"
              >
                <Upload :size="16" class="mr-2" />
                Upload New Picture
              </button>
              <p class="text-sm text-gray-500">
                Recommended: Square image, at least 200x200px. Max file size: 5MB.
              </p>
            </div>
          </div>
        </div>
        -->

        <form @submit.prevent="updatePersonalInfo" class="space-y-6">
          <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
            <!-- Full Name -->
            <div class="floating-label-container">
              <input
                id="name"
                v-model="personalInfo.name"
                type="text"
                required
                class="floating-input"
                :class="{ 'border-red-500': errors.name }"
                @focus="setLabelActive('name', true)"
                @blur="setLabelActive('name', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.name || personalInfo.name,
                  },
                ]"
              >
                Full Name
              </label>
              <span v-if="errors.name" class="mt-1 text-sm text-red-500">{{
                errors.name
              }}</span>
            </div>

            <!-- Email -->
            <div class="floating-label-container">
              <input
                id="email"
                v-model="personalInfo.email"
                readonly
                type="email"
                required
                class="floating-input"
                :class="{ 'border-red-500': errors.email }"
                @focus="setLabelActive('email', true)"
                @blur="setLabelActive('email', false)"
              />
              <label
                :class="[
                  'floating-label',
                  { 'active text-orange-600': labelStates.email || personalInfo.email },
                ]"
              >
                E-mail
              </label>
              <span v-if="errors.email" class="mt-1 text-sm text-red-500">{{
                errors.email
              }}</span>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
            <!-- Phone Number -->
            <div class="floating-label-container">
              <input
                id="phone"
                v-model="personalInfo.phone"
                type="tel"
                required
                class="floating-input"
                :class="{ 'border-red-500': errors.phone }"
                @focus="setLabelActive('phone', true)"
                @blur="setLabelActive('phone', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.phone || personalInfo.phone,
                  },
                ]"
              >
                Phone Number
              </label>
              <span v-if="errors.phone" class="mt-1 text-sm text-red-500">{{
                errors.phone
              }}</span>
            </div>
          </div>

          <!-- Save Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              @click="updatePersonalInfo"
              :disabled="isLoadingPersonalInfo"
              class="px-6 py-3 font-medium text-white bg-gray-600 rounded-lg transition-colors hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <span v-if="isLoadingPersonalInfo">Saving...</span>
              <span v-else>Save Changes</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Section 2: Change Password -->
      <div class="material-section">
        <h3 class="mb-6 text-lg font-medium text-gray-900">Change Password</h3>
        
        <form @submit.prevent="updatePassword" class="space-y-6">
          <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
            <!-- Current Password -->
            <div>
              <label for="currentPassword" class="block mb-2 text-sm font-medium text-gray-700">
                Current Password
              </label>
              <div class="relative">
                <input
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  :type="showCurrentPassword ? 'text' : 'password'"
                  required
                  class="px-4 py-3 pr-12 w-full bg-white rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  @click="showCurrentPassword = !showCurrentPassword"
                  class="flex absolute inset-y-0 right-0 items-center pr-3"
                >
                  <Eye v-if="!showCurrentPassword" :size="16" class="text-gray-400 hover:text-gray-600" />
                  <EyeOff v-else :size="16" class="text-gray-400 hover:text-gray-600" />
                </button>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
            <!-- New Password -->
            <div>
              <label for="newPassword" class="block mb-2 text-sm font-medium text-gray-700">
                New Password
              </label>
              <div class="relative">
                <input
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  :type="showNewPassword ? 'text' : 'password'"
                  required
                  minlength="8"
                  class="px-4 py-3 pr-12 w-full bg-white rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  @click="showNewPassword = !showNewPassword"
                  class="flex absolute inset-y-0 right-0 items-center pr-3"
                >
                  <Eye v-if="!showNewPassword" :size="16" class="text-gray-400 hover:text-gray-600" />
                  <EyeOff v-else :size="16" class="text-gray-400 hover:text-gray-600" />
                </button>
              </div>
            </div>

            <!-- Confirm New Password -->
            <div>
              <label for="confirmPassword" class="block mb-2 text-sm font-medium text-gray-700">
                Confirm New Password
              </label>
              <div class="relative">
                <input
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  required
                  minlength="8"
                  class="px-4 py-3 pr-12 w-full bg-white rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  @click="showConfirmPassword = !showConfirmPassword"
                  class="flex absolute inset-y-0 right-0 items-center pr-3"
                >
                  <Eye v-if="!showConfirmPassword" :size="16" class="text-gray-400 hover:text-gray-600" />
                  <EyeOff v-else :size="16" class="text-gray-400 hover:text-gray-600" />
                </button>
              </div>
            </div>
          </div>

          <!-- Password Requirements -->
          <div class="p-4 bg-gray-50 rounded-lg">
            <h4 class="mb-2 text-sm font-medium text-gray-900">Password Requirements:</h4>
            <ul class="space-y-1 text-sm text-gray-600">
              <li class="flex items-center">
                <span :class="passwordForm.newPassword.length >= 8 ? 'text-green-600' : 'text-gray-400'" class="mr-2">✓</span>
                At least 8 characters long
              </li>
              <li class="flex items-center">
                <span :class="/[A-Z]/.test(passwordForm.newPassword) ? 'text-green-600' : 'text-gray-400'" class="mr-2">✓</span>
                Contains uppercase letter
              </li>
              <li class="flex items-center">
                <span :class="/[a-z]/.test(passwordForm.newPassword) ? 'text-green-600' : 'text-gray-400'" class="mr-2">✓</span>
                Contains lowercase letter
              </li>
              <li class="flex items-center">
                <span :class="/[0-9]/.test(passwordForm.newPassword) ? 'text-green-600' : 'text-gray-400'" class="mr-2">✓</span>
                Contains number
              </li>
            </ul>
          </div>

          <!-- Password Mismatch Warning -->
          <div v-if="passwordForm.newPassword && passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword" class="p-4 bg-red-50 rounded-lg border border-red-200">
            <p class="text-sm text-red-600">Passwords do not match</p>
          </div>

          <!-- Save Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="!isPasswordFormValid || isLoadingPassword"
              class="px-6 py-3 font-medium text-white bg-gray-600 rounded-lg transition-colors hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              <span v-if="isLoadingPassword">Updating...</span>
              <span v-else>Update Password</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
