<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Error Icon -->
      <div class="flex justify-center">
        <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center">
          <ShieldX :size="48" class="text-red-600" />
        </div>
      </div>

      <!-- Error Content -->
      <div class="space-y-4">
        <h1 class="text-6xl font-bold text-gray-900">403</h1>
        <h2 class="text-2xl font-semibold text-gray-700">Access Forbidden</h2>
        <p class="text-gray-500 max-w-sm mx-auto">
          You don't have permission to access this resource. Please contact your administrator if you believe this is an error.
        </p>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3">
        <button
          @click="goBack"
          class="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
        >
          <ArrowLeft :size="16" class="mr-2" />
          Go Back
        </button>
        
        <button
          @click="goHome"
          class="w-full flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
        >
          <Home :size="16" class="mr-2" />
          Go to Dashboard
        </button>
      </div>

      <!-- Additional Info -->
      <div class="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <div class="flex items-start">
          <AlertTriangle :size="20" class="text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
          <div class="text-left">
            <h3 class="text-sm font-medium text-yellow-800">Need Access?</h3>
            <p class="text-sm text-yellow-700 mt-1">
              Contact your system administrator to request the necessary permissions for this module.
            </p>
          </div>
        </div>
      </div>

      <!-- User Info (if available) -->
      <div v-if="userRole" class="mt-6 text-xs text-gray-400">
        Current Role: {{ userRole.role || 'No role assigned' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ShieldX, ArrowLeft, Home, AlertTriangle } from 'lucide-vue-next'
import { usePermissions } from '@/composables/permissions/usePermissions'

const router = useRouter()
const { userRole } = usePermissions()

const goBack = () => {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
/* Add any additional styling if needed */
</style>
