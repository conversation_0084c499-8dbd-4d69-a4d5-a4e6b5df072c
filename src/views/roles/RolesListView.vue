<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { Search, ChevronLeft, ChevronRight, Edit } from "lucide-vue-next";
import { useRole } from "@/composables/roles/useRole";
import { Role } from "@/types/roles";

const router = useRouter();
const searchQuery = ref("");
const currentPage = ref(1);
const itemsPerPage = 10;

const { getRoles } = useRole();

// Sample roles data
const roles = ref<Role[]>([]);

const filteredRoles = computed(() => {
  return roles.value.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      role.description.toLowerCase().includes(searchQuery.value.toLowerCase());

    return matchesSearch;
  });
});

const totalPages = computed(() => {
  return Math.ceil(filteredRoles.value.length / itemsPerPage);
});

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const editRole = (role: any) => {
  router.push(`/roles/${role.id}/edit`);
};

onMounted(async () => {
  roles.value = await getRoles();
});
</script>

<template>
  <div class="px-3 py-3 bg-white">
    <!-- Filters -->
    <div class="mb-6">
      <div
        class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between"
      >
        <!-- Search and Filters -->
        <div class="flex flex-col flex-1 gap-4 sm:flex-row sm:items-center">
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 w-4 h-4 text-gray-400 transform -translate-y-1/2"
            />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search Role"
              class="py-2 pr-4 pl-10 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:w-auto"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Roles Table -->
    <div
      class="overflow-hidden overflow-x-auto mb-6 bg-white rounded-lg border border-gray-200"
    >
      <table class="w-full min-w-full">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th
              class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6"
            >
              Role
            </th>
            <th
              class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6"
            >
              Description
            </th>
            <th
              class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6"
            >
              Edit
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr
            v-for="role in filteredRoles"
            :key="role.id"
            class="hover:bg-gray-50"
          >
            <td class="px-4 py-4 whitespace-nowrap md:px-6">
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ role.name }}
                </div>
              </div>
            </td>
            <td class="px-4 py-4 text-sm text-gray-500 md:px-6">
              {{ role.description }}
            </td>
            <td
              class="px-4 py-4 text-sm text-gray-500 whitespace-nowrap md:px-6"
            >
              <button
                @click="editRole(role)"
                class="text-gray-400 transition-colors hover:text-gray-600"
              >
                <Edit class="w-4 h-4" />
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft class="w-4 h-4" />
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>
