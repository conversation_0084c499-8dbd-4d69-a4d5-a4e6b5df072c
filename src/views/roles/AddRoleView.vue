<script setup lang="ts">
import { onMounted } from "vue";
import { ChevronLeft } from "lucide-vue-next";
import ToggleSwitch from "./ToggleSwitch.vue";
import { useRole } from "@/composables/roles/useRole";

const {
  module,
  permissions,
  form,
  labelStates,
  isEditMode,
  setLabelActive,
  goBack,
  handleSubmit,
  getModules,
  initializeEditMode,
  populateFormForEdit
} = useRole();


onMounted(async () => {
  // Initialize edit mode based on route
  initializeEditMode();

  // Load modules first
  await getModules();

  if (isEditMode.value) {
    // If in edit mode, populate form with existing role data
    await populateFormForEdit();
  } else {
    // If in create mode, initialize with default permissions
    permissions.value = module.value.map((m) => ({
      name: m.name,
      permissions: {
        id: m.id,
        list: false,
        create: false,
        view: false,
        edit: false,
      },
    }));
  }
});
</script>

<template>
  <div class="bg-white px-4 md:px-8 lg:px-[10%]">
    <!-- Breadcrumb inside body -->
    <div class="pt-4 mb-6">
      <button
        @click="goBack"
        class="flex items-center mb-4 text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft class="mr-1 w-5 h-5" />
        Back to Roles
      </button>
    </div>

    <!-- Roles Title with Icon -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-900">Roles</h1>
    </div>

    <!-- Form Container -->
    <div class="max-w-4xl">
      <!-- ROLE Section -->
      <div class="mb-8">
        <h2 class="mb-6 text-lg font-medium text-gray-900">
          {{ isEditMode ? 'EDIT ROLE' : 'NEW CUSTOM ROLE' }}
        </h2>

        <div class="space-y-6">
          <!-- Role Name -->
          <div class="floating-label-container">
            <input
              id="roleName"
              v-model="form.roleName"
              type="text"
              class="floating-input"
              @focus="setLabelActive('roleName', true)"
              @blur="setLabelActive('roleName', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.roleName || form.roleName,
                },
              ]"
            >
              Role Name
            </label>
          </div>

          <!-- Role Description -->
          <div class="floating-label-container">
            <textarea
              id="roleDescription"
              v-model="form.roleDescription"
              rows="4"
              class="floating-textarea"
              @focus="setLabelActive('roleDescription', true)"
              @blur="setLabelActive('roleDescription', false)"
            ></textarea>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.roleDescription || form.roleDescription,
                },
              ]"
            >
              Role Description
            </label>
          </div>
        </div>
      </div>

      <!-- Permissions Section -->
      <div class="mb-8">
        <h2 class="mb-6 text-lg font-medium text-gray-900">Permissions</h2>

        <!-- Permissions Table -->
        <div class="overflow-hidden bg-white rounded-lg border border-gray-200">
          <!-- Table Header -->
          <div
            class="grid grid-cols-5 gap-4 p-4 bg-gray-100 border-b border-gray-200"
          >
            <div
              class="text-sm font-medium tracking-wider text-gray-700 uppercase"
            >
              SECTION
            </div>
            <div
              class="text-sm font-medium tracking-wider text-center text-gray-700 uppercase"
            >
              LIST
            </div>
            <div
              class="text-sm font-medium tracking-wider text-center text-gray-700 uppercase"
            >
              CREATE
            </div>
            <div
              class="text-sm font-medium tracking-wider text-center text-gray-700 uppercase"
            >
              VIEW
            </div>
            <div
              class="text-sm font-medium tracking-wider text-center text-gray-700 uppercase"
            >
              EDIT
            </div>
          </div>

          <!-- Table Rows -->
          <div class="divide-y divide-gray-200">
            <div
              v-for="(section) in permissions"
              :key="section.name"
              class="grid grid-cols-5 gap-4 items-center p-4 hover:bg-gray-50"
            >
              <!-- Section Name -->
              <div class="text-sm font-medium text-gray-900">
                {{ section.name }}
              </div>

              <!-- List Toggle -->
              <div class="flex justify-center">
                <ToggleSwitch v-model="section.permissions.list" />
              </div>

              <!-- Create Toggle -->
              <div class="flex justify-center">
                <ToggleSwitch v-model="section.permissions.create" />
              </div>

              <!-- View Toggle -->
              <div class="flex justify-center">
                <ToggleSwitch v-model="section.permissions.view" />
              </div>

              <!-- Edit Toggle -->
              <div class="flex justify-center">
                <ToggleSwitch v-model="section.permissions.edit" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end pt-8 space-x-4">
        <button
          type="button"
          @click="goBack"
          class="px-8 py-3 font-medium text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
        >
          CANCEL
        </button>
        <button
          type="submit"
          @click="handleSubmit"
          class="px-8 py-3 font-medium text-white bg-gray-600 rounded-lg transition-colors hover:bg-gray-700"
        >
          {{ isEditMode ? 'UPDATE ROLE' : 'SAVE ROLE' }}
        </button>
      </div>
    </div>
  </div>
</template>
