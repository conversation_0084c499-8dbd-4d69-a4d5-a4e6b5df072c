
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, ChevronLeft, ChevronRight, Edit } from 'lucide-vue-next'
import { useUser } from '@/composables/users/useUser'
import { getBackgroundColor, getInitials } from '@/utils/image-background'

const router = useRouter()

// Use the useUser composable
const { users, roles, getUsers, getRoles } = useUser()

const searchQuery = ref('')
const selectedDepartment = ref('')
const selectedRole = ref('')
const selectedStatus = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Load data on component mount
onMounted(async () => {
  await getUsers()
  await getRoles()
})

// Map API response to display format
const mappedUsers = computed(() => {
  return users.value.map(user => {
    console.log(user)
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      department: user.department || 'N/A',
      role: user.role || 'N/A',
      status: user.status ? 'Active' : 'Inactive', // Convert boolean to string
      isSuperUser: user.isSuperUser || user.is_super_user,
      avatar: user.pic || user.avatar // Handle different field names
    }
  })
})

const filteredUsers = computed(() => {
  return mappedUsers.value.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesDepartment = !selectedDepartment.value || user.department === selectedDepartment.value
    const matchesRole = !selectedRole.value || user.role === selectedRole.value
    const matchesStatus = !selectedStatus.value || user.status === selectedStatus.value

    return matchesSearch && matchesDepartment && matchesRole && matchesStatus
  })
})

const totalPages = computed(() => {
  return Math.ceil(filteredUsers.value.length / itemsPerPage)
})

const toggleStatus = (user: any) => {
  // Find the original user in the users array and update the boolean status
  const originalUser = users.value.find(u => u.id === user.id)
  if (originalUser) {
    originalUser.status = !originalUser.status
    // You would typically make an API call here to update the status on the server
    // await usersApi.updateUser(user.id, { status: originalUser.status })
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}



const editUser = (user: any) => {
  router.push(`/users/${user.id}/edit`)
}
</script>

<template>
  <div class="px-3 bg-white">

    <!-- Filters -->
    <div class="pt-4 mb-6">
      <div class="flex flex-col gap-4 w-full md:flex-row md:items-center md:justify-between">
        <!-- Search -->
        <div class="flex flex-col flex-1 gap-4 justify-end sm:flex-row sm:items-center">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 w-4 h-4 text-gray-400 transform -translate-y-1/2" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search User"
              class="py-2 pr-4 pl-10 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:w-auto"
            />
          </div>

          <!-- Filters Row -->
          <div class="flex flex-wrap gap-4">
            <!-- Role Filter -->
            <select
              v-model="selectedRole"
              class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 min-w-[100px] appearance-none pr-8 bg-no-repeat bg-right"
              style="background-image: url('data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'12\' height=\'12\' viewBox=\'0 0 12 12\'><path fill=\'%23666\' d=\'M6 8L2 4h8z\'/></svg>'); background-position: right 8px center; background-size: 12px 12px;"
            >
              <option value="">Role</option>
              <option v-for="role in roles" :key="role.id" :value="role.name">
                {{ role.name }}
              </option>
            </select>

            <!-- Status Filter -->
            <select
              v-model="selectedStatus"
              class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 min-w-[100px] appearance-none pr-8 bg-no-repeat bg-right"
              style="background-image: url('data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'12\' height=\'12\' viewBox=\'0 0 12 12\'><path fill=\'%23666\' d=\'M6 8L2 4h8z\'/></svg>'); background-position: right 8px center; background-size: 12px 12px;"
            >
              <option value="">Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="overflow-hidden overflow-x-auto mb-6 bg-white rounded-lg border border-gray-200">
      <table class="w-full min-w-full">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Name</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Email</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Is Super Admin</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Role</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Status</th>
            <th class="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase whitespace-nowrap md:px-6">Edit</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50">
            <td class="px-4 py-4 whitespace-nowrap md:px-6">
              <div class="flex items-center space-x-3 min-w-0">
                <img
                  v-if="user.avatar"
                  :src="user.avatar"
                  :alt="user.name"
                  class="object-cover flex-shrink-0 w-10 h-10 rounded-full"
                />
                <div
                  v-else
                  :class="[
                    'flex justify-center items-center w-10 h-10 rounded-full border-2 border-gray-200 font-medium text-sm flex-shrink-0',
                    getBackgroundColor(user.name),
                  ]"
                >
                  {{ getInitials(user.name) }}
                </div>
                <div class="text-sm font-medium text-gray-900">
                  {{ user.name }}
                </div>
              </div>
            </td>
            <td class="px-4 py-4 text-sm text-gray-500 whitespace-nowrap md:px-6">
              {{ user.email }}
            </td>
            <td class="px-4 py-4 text-sm text-gray-500 whitespace-nowrap md:px-6">
              <span v-if="user.isSuperUser" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium text-purple-800 bg-purple-100 rounded-full">
                Superuser
              </span>
              <span v-else>{{ user.department }}</span>
            </td>
            <td class="px-4 py-4 text-sm text-gray-500 whitespace-nowrap md:px-6">
              <span v-if="user.isSuperUser" class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium text-purple-800 bg-purple-100 rounded-full">
                All Permissions
              </span>
              <span v-else>{{ user.role }}</span>
            </td>
            <td class="px-4 py-4 whitespace-nowrap md:px-6">
              <button
                @click="toggleStatus(user)"
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-pointer transition-colors',
                  user.status === 'Active' 
                    ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                    : 'bg-red-100 text-red-800 hover:bg-red-200'
                ]"
              >
                {{ user.status }}
              </button>
            </td>
            <td class="px-4 py-4 text-sm text-gray-500 whitespace-nowrap md:px-6">
              <button
                @click="editUser(user)"
                class="text-gray-400 transition-colors hover:text-gray-600"
              >
                <Edit class="w-4 h-4" />
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft class="w-4 h-4" />
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>
