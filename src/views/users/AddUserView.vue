<script setup lang="ts">
import { onMounted } from "vue";
import { ChevronLeft } from "lucide-vue-next";
import { useUser } from "@/composables/users/useUser";
import { usePermissions } from "@/composables/permissions/usePermissions";
import { ModuleId } from "@/types/permissions";

const {
  form,
  labelStates,
  errors,
  isEditMode,
  roles,
  setLabelActive,
  goBack,
  handleSubmit,
  getRoles,
  loadUser,
} = useUser();

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions();
const canCreateUsers = canCreate(ModuleId.USERS);
const canEditUsers = canEdit(ModuleId.USERS);

// Load roles on component mount
onMounted(async () => {
  if (isEditMode.value) {
    await loadUser();
  }
  await getRoles();
});
</script>

<template>
  <div class="bg-white px-4 md:px-8 lg:px-[10%]">
    <!-- Breadcrumb inside body -->
    <div class="pt-4 mb-6">
      <button
        @click="goBack"
        class="flex items-center mb-4 text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft class="mr-1 w-5 h-5" />
        Back to Users
      </button>
    </div>

    <!-- Users Title -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-900">Users</h1>
    </div>



    <!-- Form Container -->
    <div class="max-w-4xl">
      <form v-if="isSuperuser || (isEditMode ? canEditUsers : canCreateUsers)" @submit.prevent="handleSubmit" class="space-y-6">
        <!-- First Row - Full Name and Email -->
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div class="floating-label-container">
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="floating-input"
              :class="{ 'border-red-500': errors.name }"
              @focus="setLabelActive('name', true)"
              @blur="setLabelActive('name', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.name || form.name,
                },
              ]"
            >
              Full Name
            </label>
            <span v-if="errors.name" class="mt-1 text-sm text-red-500">{{
              errors.name
            }}</span>
          </div>

          <div class="floating-label-container">
            <input
              id="email"
              v-model="form.email"
              type="email"
              required
              class="floating-input"
              :class="{ 'border-red-500': errors.email }"
              @focus="setLabelActive('email', true)"
              @blur="setLabelActive('email', false)"
            />
            <label
              :class="[
                'floating-label',
                { 'active text-orange-600': labelStates.email || form.email },
              ]"
            >
              E-mail
            </label>
            <span v-if="errors.email" class="mt-1 text-sm text-red-500">{{
              errors.email
            }}</span>
          </div>
        </div>

        <!-- Second Row - Phone Number and Password -->
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div class="floating-label-container">
            <input
              id="phone"
              v-model="form.phone"
              type="tel"
              required
              class="floating-input"
              :class="{ 'border-red-500': errors.phone }"
              @focus="setLabelActive('phone', true)"
              @blur="setLabelActive('phone', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.phone || form.phone,
                },
              ]"
            >
              Phone Number
            </label>
            <span v-if="errors.phone" class="mt-1 text-sm text-red-500">{{
              errors.phone
            }}</span>
          </div>

          <div class="floating-label-container">
            <input
              id="password"
              v-model="form.password"
              type="password"
              :required="!isEditMode"
              class="floating-input"
              :class="{ 'border-red-500': errors.password }"
              @focus="setLabelActive('password', true)"
              @blur="setLabelActive('password', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.password || form.password,
                },
              ]"
            >
              Password
            </label>
            <span v-if="errors.password" class="mt-1 text-sm text-red-500">{{
              errors.password
            }}</span>
          </div>
        </div>

        <!-- Third Row - Is Superadmin Toggle -->
        <div class="grid grid-cols-1 gap-6">
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700"
              >Is Superadmin:</label
            >
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="form.isSuperuser"
                  :value="true"
                  class="mr-2 text-orange-600 focus:ring-orange-500"
                />
                <span class="text-sm text-gray-700">Yes</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="form.isSuperuser"
                  :value="false"
                  class="mr-2 text-orange-600 focus:ring-orange-500"
                />
                <span class="text-sm text-gray-700">No</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Fourth Row - Department and Role (Conditional) -->
        <div
          v-if="!form.isSuperuser"
          class="grid grid-cols-1 gap-6 md:grid-cols-2"
        >
          <div class="floating-label-container">
            <select
              id="department"
              v-model="form.department"
              :required="!form.isSuperuser"
              class="floating-select"
              :class="{ 'border-red-500': errors.department }"
              @focus="setLabelActive('department', true)"
              @blur="setLabelActive('department', false)"
            >
              <option value="Finance">Finance</option>
              <option value="IT">IT</option>
              <option value="HR">HR</option>
              <option value="Operations">Operations</option>
              <option value="Marketing">Marketing</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.department || form.department,
                },
              ]"
            >
              Department
            </label>
            <span v-if="errors.department" class="mt-1 text-sm text-red-500">{{
              errors.department
            }}</span>
          </div>

          <div class="floating-label-container">
            <select
              id="roleId"
              v-model="form.roleId"
              :required="!form.isSuperuser"
              class="floating-select"
              :class="{ 'border-red-500': errors.roleId }"
              @focus="setLabelActive('roleId', true)"
              @blur="setLabelActive('roleId', false)"
            >
              <option v-for="role in roles" :key="role.id" :value="role.id">
                {{ role.name }}
              </option>
            </select>
            <label
              :class="[
                'floating-label',
                { 'active text-orange-600': labelStates.roleId || form.roleId },
              ]"
            >
              Role
            </label>
            <span v-if="errors.roleId" class="mt-1 text-sm text-red-500">{{
              errors.roleId
            }}</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end pt-8 space-x-4">
          <button
            type="button"
            @click="goBack"
            class="px-8 py-3 font-medium text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
          >
            CANCEL
          </button>
          <button
            type="submit"
            class="px-8 py-3 font-medium text-white bg-gray-600 rounded-lg transition-colors hover:bg-gray-700"
          >
            {{ isEditMode ? "UPDATE USER" : "ADD USER" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>
