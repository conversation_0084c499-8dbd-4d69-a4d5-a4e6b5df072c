<script setup lang="ts">
import { Search, Edit } from 'lucide-vue-next'
import { useEquipmentList } from '@/composables/equipment/useEquipmentList'

const {
  // State
  isLoading,
  searchQuery,
  categoryFilter,

  // Computed
  filteredEquipment,
  categories,

  // Methods
  handleEditEquipment,
  getStatusBadge
} = useEquipmentList()
</script>

<template>
  <div class="p-4 mx-auto w-full max-w-none bg-white">

    <!-- Search and Filter Controls -->
    <div class="flex items-center mb-6 space-x-4">
      <!-- Search Input -->
      <div class="relative flex-1 max-w-md">
        <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
          <Search :size="16" class="text-gray-400" />
        </div>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search equipment..."
          class="py-2 pr-4 pl-10 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <!-- Category Filter -->
      <div class="relative">
        <select
          v-model="categoryFilter"
          class="px-4 py-2 pr-8 bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="">All Categories</option>
          <option v-for="category in categories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"></div>
    </div>

    <!-- Equipment Table -->
    <div v-else class="overflow-hidden bg-white rounded-lg border border-gray-200">
      <!-- Table Header -->
      <div class="grid grid-cols-7 gap-4 px-6 py-3 text-xs font-medium tracking-wider text-gray-500 uppercase bg-gray-50 border-b border-gray-200">
        <div>Equipment Name</div>
        <div>Equipment ID</div>
        <div>Category</div>
        <div>Model</div>
        <div>Assigned To</div>
        <div>Status</div>
        <div>Actions</div>
      </div>

      <!-- Table Body -->
      <div class="divide-y divide-gray-200">
        <div
          v-for="equipment in filteredEquipment"
          :key="equipment.id"
          class="grid grid-cols-7 gap-4 items-center px-6 py-4 hover:bg-gray-50"
        >
          <!-- Equipment Name -->
          <div class="text-sm font-medium text-gray-900">
            {{ equipment.name }}
          </div>

          <!-- Equipment ID -->
          <div class="text-sm text-gray-600">
            {{ equipment.equipmentId }}
          </div>

          <!-- Category -->
          <div class="text-sm text-gray-600">
            {{ equipment.category }}
          </div>

          <!-- Model -->
          <div class="text-sm text-gray-600">
            {{ equipment.model || 'N/A' }}
          </div>

          <!-- Assigned To -->
          <div class="text-sm text-gray-600">
            {{ equipment.assignedTo }}
          </div>

          <!-- Status -->
          <div>
            <span
              :class="[
                'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                getStatusBadge(equipment).class
              ]"
            >
              {{ getStatusBadge(equipment).text }}
            </span>
          </div>

          <!-- Actions -->
          <div class="flex space-x-2">
            <button
              @click="handleEditEquipment(equipment.id!)"
              class="p-1 text-gray-400 transition-colors hover:text-orange-600"
              title="Edit equipment"
            >
              <Edit :size="16" />
            </button>
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-if="filteredEquipment.length === 0"
          class="px-6 py-12 text-center"
        >
          <div class="text-gray-500">
            <p class="text-lg font-medium">No equipment found</p>
            <p class="mt-1">{{ searchQuery || categoryFilter ? 'Try adjusting your search or filters' : 'Get started by adding your first equipment' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
