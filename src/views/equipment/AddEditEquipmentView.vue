<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ChevronLeft, ChevronDown, X } from 'lucide-vue-next'
import CalendarComponent from '@/components/Calendar.vue'
import { useEquipment } from '@/composables/equipment/useEquipment'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const route = useRoute()

const {
  // State
  equipmentForm,
  isLoading,
  isSubmitting,
  showDatePicker,
  labelStates,
  fieldErrors,
  
  // Computed
  isEditMode,
  pageTitle,
  submitButtonText,
  categoryOptions,
  
  // Methods
  setLabelActive,
  toggleDatePicker,
  handleDateSelect,
  clearPurchaseDate,
  validateField,
  loadEquipment,
  submitEquipment,
  cancelEdit
} = useEquipment()

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions()
const canCreateEquipment = canCreate(ModuleId.EQUIPMENT)
const canEditEquipment = canEdit(ModuleId.EQUIPMENT)

// Helper function for date formatting
const datePickerFormatter = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  })
}

// Click outside handler to close date picker
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showDatePicker.value = false
  }
}

onMounted(async () => {
  if (isEditMode.value && route.params.id) {
    await loadEquipment(route.params.id as string)
  }
  
  // Add click outside listener
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // Remove click outside listener
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="mx-auto w-full max-w-none" style="width: 90%;">
    <!-- Breadcrumb Navigation -->
    <div class="mb-4">
      <button 
        @click="$router.push('/equipment')"
        class="flex items-center text-sm font-medium text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft :size="16" class="mr-1" />
        Equipment
      </button>
    </div>

    <!-- Page Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-3">
          <div class="flex justify-center items-center w-12 h-12 bg-orange-500 rounded-full">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-white">
              <path d="M4 6h16v2H4V6zm0 5h16v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6z" fill="currentColor"/>
            </svg>
          </div>
          <div>
            <h1 class="text-xl font-semibold text-gray-900">{{ pageTitle }}</h1>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"></div>
    </div>

    <!-- Form Content -->
    <div v-else class="space-y-8">
      <!-- Equipment Information -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <!-- Equipment Name -->
          <div class="floating-label-container">
            <input
              v-model="equipmentForm.name"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.name }"
              @focus="setLabelActive('name', true)"
              @blur="() => { setLabelActive('name', false); validateField('name', equipmentForm.name) }"
              required
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.name || equipmentForm.name }
              ]"
            >
              Equipment Name
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.name" class="text-sm text-red-600">
                {{ fieldErrors.name }}
              </div>
            </div>
          </div>

          <!-- Category -->
          <div>
            <div class="relative">
              <select
                v-model="equipmentForm.category"
                class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': fieldErrors.category }"
                @blur="validateField('category', equipmentForm.category)"
                @change="validateField('category', equipmentForm.category)"
              >
                <option value="">Select a category...</option>
                <option 
                  v-for="category in categoryOptions" 
                  :key="category" 
                  :value="category"
                >
                  {{ category }}
                </option>
              </select>
              <ChevronDown :size="16" class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none" />
              <label class="absolute -top-2 left-3 px-1 text-xs text-gray-500 bg-white">
                Category
              </label>
            </div>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.category" class="text-sm text-red-600">
                {{ fieldErrors.category }}
              </div>
            </div>
          </div>

          <!-- Serial Number -->
          <div class="floating-label-container">
            <input
              v-model="equipmentForm.serialNumber"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.serialNumber }"
              @focus="setLabelActive('serialNumber', true)"
              @blur="() => { setLabelActive('serialNumber', false); validateField('serialNumber', equipmentForm.serialNumber) }"
              required
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.serialNumber || equipmentForm.serialNumber }
              ]"
            >
              Serial Number
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.serialNumber" class="text-sm text-red-600">
                {{ fieldErrors.serialNumber }}
              </div>
            </div>
          </div>

          <!-- ID -->
          <div class="floating-label-container">
            <input
              v-model="equipmentForm.equipmentId"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.equipmentId }"
              @focus="setLabelActive('equipmentId', true)"
              @blur="() => { setLabelActive('equipmentId', false); validateField('equipmentId', equipmentForm.equipmentId) }"
              required
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.equipmentId || equipmentForm.equipmentId }
              ]"
            >
              ID
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.equipmentId" class="text-sm text-red-600">
                {{ fieldErrors.equipmentId }}
              </div>
            </div>
          </div>
        </div>

        <!-- Second Row -->
        <div class="grid grid-cols-1 gap-6 mt-6 md:grid-cols-4">
          <!-- Model -->
          <div class="floating-label-container">
            <input
              v-model="equipmentForm.model"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.model }"
              placeholder="Model"
              @focus="setLabelActive('model', true)"
              @blur="() => { setLabelActive('model', false); validateField('model', equipmentForm.model) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.model || equipmentForm.model }
              ]"
            >
              Model
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.model" class="text-sm text-red-600">
                {{ fieldErrors.model }}
              </div>
            </div>
          </div>

          <!-- Purchase Date -->
          <div class="floating-label-container">
            <div class="relative">
              <input
                :value="equipmentForm.purchaseDate ? datePickerFormatter(new Date(equipmentForm.purchaseDate)) : ''"
                type="text"
                readonly
                class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                :class="{ 'border-red-500': fieldErrors.purchaseDate }"
                @click="toggleDatePicker"
                @focus="setLabelActive('purchaseDate', true)"
                @blur="setLabelActive('purchaseDate', false)"
              />
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.purchaseDate || equipmentForm.purchaseDate }
                ]"
              >
                Purchase Date
              </label>
              <!-- Clear button -->
              <button
                v-if="equipmentForm.purchaseDate"
                @click.stop="clearPurchaseDate"
                class="absolute right-3 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
              >
                <X :size="16" />
              </button>
              <CalendarComponent
                :is-open="showDatePicker"
                :selected-date="equipmentForm.purchaseDate ? new Date(equipmentForm.purchaseDate) : new Date()"
                @select-date="handleDateSelect"
                @close="showDatePicker = false"
              />
            </div>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.purchaseDate" class="text-sm text-red-600">
                {{ fieldErrors.purchaseDate }}
              </div>
            </div>
          </div>

          <!-- Assigned To -->
          <div v-if="isEditMode" class="floating-label-container">
            <input
              :value="equipmentForm.assignedTo"
              readonly
              type="text"
              class="floating-input"
              @focus="setLabelActive('assignedTo', true)"
              @blur="() => { setLabelActive('assignedTo', false); validateField('assignedTo', equipmentForm.assignedTo) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.assignedTo || equipmentForm.assignedTo }
              ]"
            >
              Assigned To
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.assignedTo" class="text-sm text-red-600">
                {{ fieldErrors.assignedTo }}
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- Action Buttons -->
      <div v-if="isSuperuser || (isEditMode ? canEditEquipment : canCreateEquipment)" class="flex justify-end pt-6 space-x-4">
        <button
          v-if="!isEditMode"
          @click="cancelEdit"
          class="px-8 py-3 text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
        >
          CANCEL
        </button>
        <button
          @click="submitEquipment"
          :disabled="isSubmitting"
          class="px-8 py-3 text-white bg-gray-800 rounded-lg transition-colors hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ isSubmitting ? 'SUBMITTING...' : submitButtonText }}
        </button>
      </div>
    </div>
  </div>
</template>
