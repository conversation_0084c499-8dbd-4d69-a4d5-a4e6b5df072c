
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Search,
  Edit,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next'

import { useClient } from '@/composables/clients/useClient'
import { ClientForm } from '@/types/master/client'

const router = useRouter()
const { 
  getListOfClients 

} = useClient()

// Reactive data
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

const clients = ref<ClientForm[]>()

// Computed properties
const filteredClients = computed(() => {
  let filtered = clients.value

  // Filter by search query
  if (searchQuery.value && filtered?.length) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(client => 
      client.name.toLowerCase().includes(query) ||
      client.clientId.includes(query) ||
      client.contactManager.toLowerCase().includes(query)
    )
  }

  // Filter by status
  if (statusFilter.value) {
    filtered = filtered?.filter(client => client.status === statusFilter.value)
  }

  return filtered
})

const totalPages = computed(() => {
  return filteredClients.value ? Math.ceil(filteredClients.value.length / itemsPerPage) : 0
})

// Methods
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  })
}

const handleEditClient = (clientId: number) => {
  router.push(`/clients/${clientId}`)
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

onMounted(async () => {
  clients.value = await getListOfClients()
})
</script>

<template>
  <div class="px-3 py-3 bg-white">
    <!-- Search and Filter Controls -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-4">
        <!-- Search Input -->
        <div class="relative">
          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
            <Search :size="16" class="text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search Client or ID"
            class="py-2 pr-4 pl-10 w-80 rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>

        <!-- Status Filter -->
        <div class="relative">
          <select
            v-model="statusFilter"
            class="px-4 py-2 pr-8 bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          >
            <option value="">Status</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>
          <!-- <ChevronDown :size="16" class="absolute right-2 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none" /> -->
        </div>
      </div>
    </div>

    <!-- Clients Table -->
    <div class="overflow-hidden bg-white rounded-lg border border-gray-200">
      <!-- Table Header -->
      <div class="grid grid-cols-[2fr_1fr_2fr_1fr_1fr_1fr_80px] gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-500 uppercase tracking-wider">
        <div>NAME</div>
        <div>ID</div>
        <div>CONTACT</div>
        <div>START DATE</div>
        <div>Employees</div>
        <div>STATUS</div>
        <div>EDIT</div>
      </div>

      <!-- Table Rows -->
      <div class="divide-y divide-gray-100">
        <div
          v-for="client in filteredClients"
          :key="client.id"
          class="grid grid-cols-[2fr_1fr_2fr_1fr_1fr_1fr_80px] gap-4 px-6 py-4 items-center hover:bg-gray-50 transition-colors"
        >
          <!-- Name -->
          <div class="text-sm font-medium text-gray-900">
            {{ client.name }}
          </div>

          <!-- ID -->
          <div class="text-sm text-gray-600">
            {{ client.clientId }}
          </div>

          <!-- Contact -->
          <div class="text-sm text-gray-600">
            <div class="font-medium">{{ client.contactManager }}</div>
            <div class="text-xs text-gray-500">{{ client.contactManagerEmail }}</div>
            <div class="text-xs text-gray-500">{{ client.contactManagerPhone }}</div>
          </div>
          
          <!-- Start Date -->
          <div class="text-sm text-gray-600">
            {{ formatDate(client.startDate) }}
          </div>
          
          <!-- Employees Count -->
          <div class="text-sm text-center text-gray-900">
            {{ client.empCount }}
          </div>

          <!-- Status -->
          <div>
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                client.isActive
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              ]"
            >
              {{ client.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>

          <!-- Edit Button -->
          <div class="flex justify-center">
            <button
              @click="handleEditClient(client.id!)"
              class="p-2 text-gray-400 transition-colors hover:text-gray-600"
            >
              <Edit :size="16" />
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredClients?.length === 0" class="py-12 text-center">
        <div class="text-sm text-gray-500">No clients found</div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-700">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft :size="16" />
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight :size="16" />
        </button>
      </div>
    </div>
  </div>
</template>
