<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Building2, ChevronDown, ChevronLeft, ExternalLink, FileText, Plus, X, Search } from 'lucide-vue-next'
import CalendarComponent from '@/components/Calendar.vue'
import { useClient } from '@/composables/clients/useClient'
import { formatDate } from '@/utils/date-formatting'
import { getBackgroundColor, getInitials } from '@/utils/image-background'

const route = useRoute()

const {
  // State
  clientForm,
  isLoading,
  isSubmitting,
  showDatePicker,
  labelStates,
  assignedEmployees,
  fieldErrors,

  // Computed
  isEditMode,
  pageTitle,
  submitButtonText,
  countryOptions,

  // Methods
  setLabelActive,
  toggleDatePicker,
  toggleEndDatePicker,
  handleDateSelect,
  clearStartDate,
  validateField,
  handleFileUpload,
  removeAttachment,
  loadClient,
  submitClient,
  cancelEdit
} = useClient()

// Helper functions
const datePickerFormatter = (date: Date) => {
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Create local references to avoid TypeScript issues
const { showEndDatePicker: endDatePickerRef, handleEndDateSelect: endDateSelectHandler, clearEndDate: clearEndDateHandler } = useClient()

// Search functionality for assigned employees
const searchQuery = ref('')

// Computed property for filtered employees
const filteredEmployees = computed(() => {
  if (!searchQuery.value) {
    return assignedEmployees.value
  }

  return assignedEmployees.value.filter(employee => {
    const fullName = `${employee.firstName} ${employee.lastName}`.toLowerCase()
    const position = employee.position.toLowerCase()
    const query = searchQuery.value.toLowerCase()

    return fullName.includes(query) || position.includes(query)
  })
})

// Helper function to navigate to talent profile
const handleTalentNavigation = (employeeId: number) => {
  window.open(`/talent/${employeeId}/profile`, '_blank')
}

// Helper function to handle image error
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  if (target) {
    target.style.display = 'none'
  }
}

// Click outside handler to close date pickers
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showDatePicker.value = false
    endDatePickerRef.value = false
  }
}

onMounted(async () => {
  if (isEditMode.value && route.params.id) {
    await loadClient(route.params.id as string)
  }

  // Add click outside listener
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // Remove click outside listener
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="mx-auto w-full max-w-none" style="width: 90%;">
    <!-- Breadcrumb Navigation -->
    <div class="mb-4">
      <button
        @click="$router.push('/clients')"
        class="flex items-center text-sm font-medium text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft :size="16" class="mr-1" />
        Clients
      </button>
    </div>

    <!-- Page Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-3">
          <div class="flex justify-center items-center w-12 h-12 bg-orange-500 rounded-full">
            <Building2 :size="24" class="text-white" />
          </div>
          <div>
            <h1 class="text-xl font-semibold text-gray-900">{{ pageTitle }}</h1>
          </div>
        </div>

        <!-- Status Badge (Edit Mode Only) -->
        <div v-if="isEditMode" class="flex items-center">
          <span
            :class="[
              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
              clientForm.status === 'Active'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-red-100 text-red-800'
            ]"
          >
            {{ clientForm.status }}
          </span>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"></div>
    </div>

    <!-- Form Content -->
    <div v-else class="space-y-8">
      <!-- Basic Information -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <!-- Client Name -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.name"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.name }"
              @focus="setLabelActive('name', true)"
              @blur="() => { setLabelActive('name', false); validateField('name', clientForm.name) }"
              required
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.name || clientForm.name }
              ]"
            >
              Client Name
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.name" class="text-sm text-red-600">
                {{ fieldErrors.name }}
              </div>
            </div>
          </div>

          <!-- Client ID -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.clientId"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.clientId }"
              @focus="setLabelActive('clientId', true)"
              @blur="() => { setLabelActive('clientId', false); validateField('clientId', clientForm.clientId) }"
              required
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.clientId || clientForm.clientId }
              ]"
            >
              Client ID
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.clientId" class="text-sm text-red-600">
                {{ fieldErrors.clientId }}
              </div>
            </div>
          </div>

          <!-- Start Date -->
          <div class="floating-label-container">
            <div class="relative">
              <input
                :value="clientForm.startDate ? datePickerFormatter(new Date(clientForm.startDate)) : ''"
                type="text"
                readonly
                class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                :class="{ 'border-red-500': fieldErrors.startDate }"
                @click="toggleDatePicker"
                @focus="setLabelActive('startDate', true)"
                @blur="setLabelActive('startDate', false)"
              />
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.startDate || clientForm.startDate }
                ]"
              >
                Start Date
              </label>
              <!-- Clear button -->
              <button
                v-if="clientForm.startDate"
                @click.stop="clearStartDate"
                class="absolute right-3 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
              >
                <X :size="16" />
              </button>
              <CalendarComponent
                :is-open="showDatePicker"
                :selected-date="clientForm.startDate ? new Date(clientForm.startDate) : new Date()"
                @select-date="handleDateSelect"
                @close="showDatePicker = false"
              />
            </div>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.startDate" class="text-sm text-red-600">
                {{ fieldErrors.startDate }}
              </div>
            </div>
          </div>
        </div>

        <!-- End Date (Edit Mode Only) -->
        <div v-if="isEditMode" class="mt-6">
          <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div class="floating-label-container">
              <div class="relative">
                <input
                  :value="clientForm.endDate ? datePickerFormatter(new Date(clientForm.endDate)) : ''"
                  type="text"
                  readonly
                  class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                  @click="toggleEndDatePicker"
                  @focus="setLabelActive('endDate', true)"
                  @blur="setLabelActive('endDate', false)"
                />
                <label
                  :class="[
                    'floating-label',
                    { active: labelStates.endDate || clientForm.endDate }
                  ]"
                >
                  End Date
                </label>
                <!-- Clear button -->
                <button
                  v-if="clientForm.endDate"
                  @click.stop="clearEndDateHandler"
                  class="absolute right-3 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                >
                  <X :size="16" />
                </button>
                <CalendarComponent
                  :is-open="endDatePickerRef"
                  :selected-date="clientForm.endDate ? new Date(clientForm.endDate) : new Date()"
                  @select-date="endDateSelectHandler"
                  @close="endDatePickerRef = false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Address Section -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <h3 class="mb-6 text-lg font-medium text-gray-900">Address</h3>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          <!-- Street Address -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.streetAddress"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.streetAddress }"
              @focus="setLabelActive('streetAddress', true)"
              @blur="() => { setLabelActive('streetAddress', false); validateField('streetAddress', clientForm.streetAddress) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.streetAddress || clientForm.streetAddress }
              ]"
            >
              Street Address
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.streetAddress" class="text-sm text-red-600">
                {{ fieldErrors.streetAddress }}
              </div>
            </div>
          </div>

          <!-- City -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.city"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.city }"
              @focus="setLabelActive('city', true)"
              @blur="() => { setLabelActive('city', false); validateField('city', clientForm.city) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.city || clientForm.city }
              ]"
            >
              City
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.city" class="text-sm text-red-600">
                {{ fieldErrors.city }}
              </div>
            </div>
          </div>

          <!-- ZIP / Postcode -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.zipCode"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.zipCode }"
              @focus="setLabelActive('zipCode', true)"
              @blur="() => { setLabelActive('zipCode', false); validateField('zipCode', clientForm.zipCode) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.zipCode || clientForm.zipCode }
              ]"
            >
              ZIP / Postcode
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.zipCode" class="text-sm text-red-600">
                {{ fieldErrors.zipCode }}
              </div>
            </div>
          </div>

          <!-- Country -->
          <div>
            <div class="relative">
              <select
                v-model="clientForm.country"
                class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': fieldErrors.country }"
                @blur="validateField('country', clientForm.country)"
              >
                <option value="">Select a country</option>
                <option v-for="country in countryOptions" :key="country" :value="country">
                  {{ country }}
                </option>
              </select>
              <ChevronDown :size="16" class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none" />
              <label class="absolute -top-2 left-3 px-1 text-xs text-gray-500 bg-white">
                Country
              </label>
            </div>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.country" class="text-sm text-red-600">
                {{ fieldErrors.country }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Manager Section -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <h3 class="mb-6 text-lg font-medium text-gray-900">Contact Manager</h3>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <!-- Contact Name -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.contactManager"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.contactManager }"
              @focus="setLabelActive('contactManager', true)"
              @blur="() => { setLabelActive('contactManager', false); validateField('contactManager', clientForm.contactManager) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.contactManager || clientForm.contactManager }
              ]"
            >
              Contact Name
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.contactManager" class="text-sm text-red-600">
                {{ fieldErrors.contactManager }}
              </div>
            </div>
          </div>

          <!-- Email -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.contactManagerEmail"
              type="email"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.contactManagerEmail }"
              @focus="setLabelActive('contactManagerEmail', true)"
              @blur="() => { setLabelActive('contactManagerEmail', false); validateField('contactManagerEmail', clientForm.contactManagerEmail) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.contactManagerEmail || clientForm.contactManagerEmail }
              ]"
            >
              Email
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.contactManagerEmail" class="text-sm text-red-600">
                {{ fieldErrors.contactManagerEmail }}
              </div>
            </div>
          </div>

          <!-- Phone -->
          <div class="floating-label-container">
            <input
              v-model="clientForm.contactManagerPhone"
              type="tel"
              class="floating-input"
              :class="{ 'border-red-500': fieldErrors.contactManagerPhone }"
              @focus="setLabelActive('contactManagerPhone', true)"
              @blur="() => { setLabelActive('contactManagerPhone', false); validateField('contactManagerPhone', clientForm.contactManagerPhone) }"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.contactManagerPhone || clientForm.contactManagerPhone }
              ]"
            >
              Phone
            </label>
            <div class="mt-1 h-5">
              <div v-if="fieldErrors.contactManagerPhone" class="text-sm text-red-600">
                {{ fieldErrors.contactManagerPhone }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Assigned Employees Section (Edit Mode Only) -->
      <div v-if="isEditMode && assignedEmployees.length > 0" class="p-6 bg-white rounded-lg border border-gray-200">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-medium text-gray-900">Assigned Employees</h3>

          <!-- Search Input -->
          <div class="relative w-64">
            <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <Search :size="16" class="text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              class="py-2 pr-4 pl-10 w-full text-sm rounded-lg border border-gray-300 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Search employees..."
            />
          </div>
        </div>

        <!-- Table Header -->
        <div class="grid grid-cols-[2fr_1fr_1fr_60px] gap-4 pb-3 border-b border-gray-200 text-xs font-medium text-gray-500 uppercase tracking-wider">
          <div>NAME</div>
          <div>ASSIGNED DATE</div>
          <div>POSITION</div>
          <div></div>
        </div>

        <!-- Employee Rows with Fixed Height and Scroll -->
        <div class="overflow-y-auto max-h-64">
          <div class="divide-y divide-gray-100">
            <div
              v-for="employee in filteredEmployees"
              :key="employee.id"
              class="grid grid-cols-[2fr_1fr_1fr_60px] gap-4 py-4 items-center"
            >
              <!-- Name with Avatar -->
              <div class="flex items-center space-x-3">
                <!-- Avatar with Initials Fallback -->
                <div class="relative">
                  <img
                    v-if="employee.avatar"
                    :src="employee.avatar"
                    :alt="`${employee.firstName} ${employee.lastName}`"
                    class="object-cover w-8 h-8 rounded-full"
                    @error="handleImageError"
                  />
                  <div
                    v-if="!employee.avatar"
                    :class="[
                      'w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium',
                      getBackgroundColor(`${employee.firstName} ${employee.lastName}`)
                    ]"
                  >
                    {{ getInitials(`${employee.firstName} ${employee.lastName}`) }}
                  </div>
                </div>
                <span class="text-sm font-medium text-gray-900">{{ employee.firstName }} {{ employee.lastName }}</span>
              </div>

              <!-- Assigned Date -->
              <div class="text-sm text-gray-600">
                {{ formatDate(employee.startDate) }}
              </div>

              <!-- Position -->
              <div class="text-sm font-medium text-orange-600">
                {{ employee.position }}
              </div>

              <!-- External Link Icon -->
              <div class="flex justify-center">
                <button
                  @click="handleTalentNavigation(employee.id)"
                  class="p-1 text-gray-400 transition-colors hover:text-gray-600"
                  title="View talent profile"
                >
                  <ExternalLink :size="16" />
                </button>
              </div>
            </div>

            <!-- No Results Message -->
            <div
              v-if="filteredEmployees.length === 0 && searchQuery"
              class="py-8 text-center text-gray-500"
            >
              <p class="text-sm">No employees found matching "{{ searchQuery }}"</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes & Attachments Section -->
      <div class="p-6 bg-white rounded-lg border border-gray-200">
        <h3 class="mb-6 text-lg font-medium text-gray-900">Notes & Attachments</h3>

        <!-- Notes -->
        <div class="mb-6">
          <div class="floating-label-container">
            <textarea
              v-model="clientForm.notes"
              class="floating-input min-h-[120px] resize-none"
              @focus="setLabelActive('notes', true)"
              @blur="setLabelActive('notes', false)"
            ></textarea>
            <label
              :class="[
                'floating-label',
                { active: labelStates.notes || clientForm.notes }
              ]"
            >
              Notes
            </label>
          </div>
        </div>

        <!-- Attachments -->
        <div class="space-y-4">
          <!-- Existing Attachments -->
          <div v-if="clientForm.attachments.length > 0" class="space-y-2">
            <div
              v-for="(attachment, index) in clientForm.attachments"
              :key="index"
              class="flex justify-between items-center p-3 bg-orange-50 rounded-lg border border-orange-200"
            >
              <div class="flex items-center space-x-3">
                <FileText :size="16" class="text-orange-600" />
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ attachment.name }}</div>
                  <div class="text-xs text-gray-500">{{ formatFileSize(attachment.size) }}</div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button class="text-xs text-orange-600 hover:text-orange-700">
                  View file
                </button>
                <button
                  @click="removeAttachment(index)"
                  class="text-xs text-red-600 hover:text-red-700"
                >
                  Remove
                </button>
              </div>
            </div>
          </div>

          <!-- Add Files Button -->
          <!-- <div class="relative">
            <input
              type="file"
              multiple
              @change="handleFileUpload"
              class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <button class="flex justify-center items-center p-4 w-full text-orange-600 rounded-lg border-2 border-orange-300 border-dashed transition-colors hover:border-orange-400 hover:text-orange-700">
              <Plus :size="16" class="mr-2" />
              <span class="text-sm font-medium">Add Files</span>
              <span class="ml-2 text-xs text-orange-500">Choose File</span>
            </button>
          </div> -->
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end pt-6 space-x-4">
        <button
          @click="cancelEdit"
          class="px-8 py-3 text-gray-700 rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
        >
          CANCEL
        </button>
        <button
          @click="submitClient"
          :disabled="isSubmitting"
          class="px-8 py-3 text-white bg-gray-800 rounded-lg transition-colors hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ isSubmitting ? 'SUBMITTING...' : submitButtonText }}
        </button>
      </div>
    </div>
  </div>
</template>

