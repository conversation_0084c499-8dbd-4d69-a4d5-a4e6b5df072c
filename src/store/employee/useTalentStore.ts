import { defineStore } from 'pinia'
import { ref } from 'vue'
import type {  TalentProfileResponse } from '@/types/talent'

export const useTalentStore = defineStore('talent', () => {
  // State
  const talents = ref<TalentProfileResponse[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Actions
  const setEmployees = (data: TalentProfileResponse[]) => {
    talents.value = data
  }

  const removeEmployee = (id: number) => {
    const index = talents.value.findIndex(emp => emp.id === id)
    if (index !== -1) {
      talents.value.splice(index, 1)
    }
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    talents,
    isLoading,
    error,

    // Actions
    setEmployees,
    removeEmployee,
    setLoading,
    setError,
    clearError
  }
})
