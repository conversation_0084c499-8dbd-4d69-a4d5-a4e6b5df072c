import { defineStore } from "pinia";
import { ref } from "vue";

export const useAuthStore = defineStore("auth", () => {
  const email = ref<string>("");
  const password = ref<string>("");
  const rememberMe = ref<boolean>(false);
  const showPassword = ref<boolean>(false);
  const isLoading = ref<boolean>(false);
  const error = ref<boolean>(false);

  const togglePassword = () => {
    showPassword.value = !showPassword.value;
  };

  const toggleRememberMe = () => {
    rememberMe.value = !rememberMe.value;
  };

  const updateLoading = (data: boolean) => {
    isLoading.value = data;
  };

  const updateError = (data: boolean) => {
    error.value = data;
  };

  return {
    email,
    password,
    rememberMe,
    showPassword,
    isLoading,
    error,
    togglePassword,
    toggleRememberMe,
    updateLoading,
    updateError,
  };
});
