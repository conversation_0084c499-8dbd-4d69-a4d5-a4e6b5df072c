import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserPermissions, ModulePermission, ModuleId, PermissionType } from '@/types/permissions'
import { settingsApi } from '@/apis/settings/settings'
import { getModuleEnum } from '@/utils/moduleMapping'

export const usePermissionsStore = defineStore('permissions', () => {
  // State
  const userPermissions = ref<UserPermissions | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false)
  const fetchPromise = ref<Promise<any> | null>(null)

  // Getters
  const isSuperuser = computed(() => {
    return userPermissions.value?.is_superuser || false
  })

  const userRole = computed(() => {
    return userPermissions.value?.permissions || null
  })

  const modulePermissions = computed(() => {
    // If superuser, return empty array since they have all permissions
    if (isSuperuser.value) {
      return []
    }
    return userPermissions.value?.permissions?.module || []
  })

  // Actions
  const setUserPermissions = (permissions: UserPermissions) => {
    userPermissions.value = permissions
    error.value = null
    isInitialized.value = true
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string) => {
    error.value = errorMessage
  }

  const clearPermissions = () => {
    userPermissions.value = null
    error.value = null
    isLoading.value = false
    isInitialized.value = false
    fetchPromise.value = null
  }

  // Fetch user permissions from API with caching
  const fetchUserPermissions = async (forceRefresh: boolean = false) => {
    // If permissions are already loaded and not forcing refresh, return cached data
    if (!forceRefresh && isInitialized.value && userPermissions.value) {
      return userPermissions.value
    }

    // If there's already a fetch in progress, return that promise
    if (!forceRefresh && fetchPromise.value) {
      return fetchPromise.value
    }

    // Create new fetch promise
    fetchPromise.value = (async () => {
      try {
        setLoading(true)
        setError("Loading permissions...")

        const response = await settingsApi.getUserPermissions()

        if (response.status_code === 200 && response.data) {
          setUserPermissions(response.data)
          return response.data
        } else {
          throw new Error(response.message || 'Failed to fetch user permissions')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user permissions'
        setError(errorMessage)
        console.error('Error fetching user permissions:', err)
        throw err
      } finally {
        setLoading(false)
        fetchPromise.value = null
      }
    })()

    return fetchPromise.value
  }

  // Force refresh user permissions (ignores cache)
  const refreshUserPermissions = async () => {
    return fetchUserPermissions(true)
  }

  // Check if user has specific permission for a module
  const hasPermission = (moduleId: ModuleId, permissionType: PermissionType): boolean => {
    // Superusers have all permissions
    if (isSuperuser.value) {
      return true
    }

    // If no permissions object exists and not superuser, deny access
    if (!userPermissions.value?.permissions?.module) {
      return false
    }

    // Find the module permission by matching the API module_id with our enum
    // The API returns module_id as numbers (1-14), we need to find the matching one
    const modulePermission = modulePermissions.value.find(
      (module: ModulePermission) => {
        const apiModuleEnum = getModuleEnum(module.module_id)
        return apiModuleEnum === moduleId
      }
    )

    if (!modulePermission) {
      return false
    }

    // Check the specific permission
    return modulePermission[permissionType] || false
  }

  // Check if user can view a module
  const canView = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'view')
  }

  // Check if user can edit in a module
  const canEdit = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'edit')
  }

  // Check if user can create in a module
  const canCreate = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'create')
  }

  // Check if user can list/access a module
  const canList = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'list')
  }

  // Check if user can access a module (either view or list permission)
  const canAccess = (moduleId: ModuleId): boolean => {
    return isSuperuser.value || canView(moduleId) || canList(moduleId)
  }

  // Get all accessible modules
  const getAccessibleModules = (): ModuleId[] => {
    if (isSuperuser.value) {
      // Superusers can access all modules
      return Object.values(ModuleId).filter(id => typeof id === 'number') as ModuleId[]
    }

    // If no permissions object exists, return empty array
    if (!userPermissions.value?.permissions?.module) {
      return []
    }

    return modulePermissions.value
      .filter((module: ModulePermission) => module.view || module.list)
      .map((module: ModulePermission) => {
        const moduleEnum = getModuleEnum(module.module_id)
        return moduleEnum
      })
      .filter((moduleId): moduleId is ModuleId => moduleId !== null)
  }

  // Check multiple permissions at once
  const hasAnyPermission = (moduleId: ModuleId, permissions: PermissionType[]): boolean => {
    return permissions.some(permission => hasPermission(moduleId, permission))
  }

  // Check if user has all specified permissions
  const hasAllPermissions = (moduleId: ModuleId, permissions: PermissionType[]): boolean => {
    return permissions.every(permission => hasPermission(moduleId, permission))
  }

  // Get user's permissions for a specific module
  const getModulePermissions = (moduleId: ModuleId): ModulePermission | null => {
    if (isSuperuser.value) {
      // Superusers have all permissions
      return {
        module_id: moduleId,
        view: true,
        edit: true,
        create: true,
        list: true
      }
    }

    if (!userPermissions.value?.permissions?.module) {
      return null
    }

    return modulePermissions.value.find(
      (module: ModulePermission) => {
        const apiModuleEnum = getModuleEnum(module.module_id)
        return apiModuleEnum === moduleId
      }
    ) || null
  }

  // Check if user has any talent-related permissions
  const hasAnyTalentPermission = (): boolean => {
    if (isSuperuser.value) {
      return true
    }

    if (!userPermissions.value?.permissions?.module) {
      return false
    }

    // Check if user has any permission for any talent-related module
    // Talent modules are API module IDs 1-9
    const talentApiModuleIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]

    return talentApiModuleIds.some(apiModuleId => {
      const modulePermission = modulePermissions.value.find(
        (module: ModulePermission) => module.module_id === apiModuleId
      )

      if (!modulePermission) {
        return false
      }

      return modulePermission.view || modulePermission.edit || modulePermission.create || modulePermission.list
    })
  }

  // Check if user can access history logs (only superusers)
  const canAccessHistoryLogs = (): boolean => {
    return isSuperuser.value
  }

  // Check if user has permission for a specific talent module
  const hasTalentModulePermission = (moduleId: ModuleId, permissionType: PermissionType): boolean => {
    if (isSuperuser.value) {
      return true
    }

    if (!userPermissions.value?.permissions?.module) {
      return false
    }

    return hasPermission(moduleId, permissionType)
  }

  return {
    // State
    userPermissions,
    isLoading,
    error,
    isInitialized,

    // Getters
    isSuperuser,
    userRole,
    modulePermissions,

    // Actions
    setUserPermissions,
    setLoading,
    setError,
    clearPermissions,
    fetchUserPermissions,
    refreshUserPermissions,

    // Permission checks
    hasPermission,
    canView,
    canEdit,
    canCreate,
    canList,
    canAccess,
    getAccessibleModules,
    hasAnyPermission,
    hasAllPermissions,
    getModulePermissions,
    hasAnyTalentPermission,
    canAccessHistoryLogs,
    hasTalentModulePermission
  }
})
