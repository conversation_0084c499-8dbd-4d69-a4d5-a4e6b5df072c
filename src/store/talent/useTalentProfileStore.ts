import { defineStore } from 'pinia'
import { ref } from 'vue'
import { FormData } from '@/types/talent'

export const useTalentProfileStore = defineStore('talentProfile', () => {
  // State
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  const form = ref<FormData>({
    firstName: '',
    middleName: '',
    lastName: '',
    primaryContact: '',
    secondaryContact: '',
    personalEmail: '',
    dateOfBirth: '',
    gender: '',
    address1: '',
    aptUnit: '',
    address2: '',
    postalCode: '',
    city: '',
    state: '',
    country: '',
    imms: '',
    curp: '',
    rfc: '',
    pic: '',
    primaryCountryCode: '+52',
    secondaryCountryCode: '+52',
    emergencyContacts: [
      {
        firstName: '',
        middleName: '',
        lastName: '',
        countryCode: '+52',
        phone: '',
        email: '',
        relationship: '',
      },
    ],
    skills: [],
    writtenEnglish: '',
    spokenEnglish: '',
    englishType: '',
    yearsOfExperience: null,
    notes: '',
    uploadedFiles: [],
  })

  // UI State
  const labelStates = ref<Record<string, boolean>>({})
  const contactLabelStates = ref<Record<number, any>>({
    0: { countryCode: false, relationship: false }
  })

  // Actions
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const updateField = <K extends keyof FormData>(field: K, value: FormData[K]) => {
    form.value[field] = value
  }

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field] = active
  }

  const setContactLabelActive = (contactIndex: number, field: string, active: boolean) => {
    if (!contactLabelStates.value[contactIndex]) {
      contactLabelStates.value[contactIndex] = {}
    }
    contactLabelStates.value[contactIndex][field] = active
  }

  const addEmergencyContact = () => {
    form.value.emergencyContacts.push({
      firstName: '',
      middleName: '',
      lastName: '',
      countryCode: '+52',
      phone: '',
      email: '',
      relationship: '',
    })
  }

  const removeEmergencyContact = (index: number) => {
    if (form.value.emergencyContacts.length > 1) {
      form.value.emergencyContacts.splice(index, 1)
    }
  }

  const resetForm = () => {
    form.value = {
      firstName: '',
      middleName: '',
      lastName: '',
      primaryContact: '',
      secondaryContact: '',
      personalEmail: '',
      dateOfBirth: '',
      gender: '',
      address1: '',
      aptUnit: '',
      address2: '',
      postalCode: '',
      city: '',
      state: '',
      country: '',
      imms: '',
      curp: '',
      rfc: '',
      pic: '',
      primaryCountryCode: '+52',
      secondaryCountryCode: '+52',
      emergencyContacts: [
        {
          firstName: '',
          middleName: '',
          lastName: '',
          countryCode: '+52',
          phone: '',
          email: '',
          relationship: '',
        },
      ],
      skills: [],
      writtenEnglish: '',
      spokenEnglish: '',
      englishType: '',
      yearsOfExperience: null,
      notes: '',
      uploadedFiles: [],
    }
    labelStates.value = {}
    contactLabelStates.value = { 0: { countryCode: false, relationship: false } }
  }

  return {
    // State
    isLoading,
    error,
    form,
    labelStates,
    contactLabelStates,
    
    // Actions
    setLoading,
    setError,
    updateField,
    setLabelActive,
    setContactLabelActive,
    addEmergencyContact,
    removeEmergencyContact,
    resetForm,
  }
})
