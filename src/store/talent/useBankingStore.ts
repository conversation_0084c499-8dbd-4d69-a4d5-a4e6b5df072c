import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { BankingInformation } from '@/types/banking'

export const useBankingStore = defineStore('banking', () => {
  const bankingInfo = ref<BankingInformation>({
    talentProfileId: 0,
    bankName: "",
    accountNumber: "",
    accountType: "",
    clabe: "",
    swiftCode: "",
  })

  const isCreateMode = ref(true)

  const resetBankingInfo = () => {
    bankingInfo.value = {
      talentProfileId: 0,
      bankName: "",
      accountNumber: "",
      accountType: "",
      clabe: "",
      swiftCode: "",
    }
    isCreateMode.value = true
  }

  return {
    bankingInfo,
    isCreateMode,
    resetBankingInfo,
  }
})