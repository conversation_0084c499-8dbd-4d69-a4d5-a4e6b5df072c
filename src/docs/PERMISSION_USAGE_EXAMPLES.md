# Permission Usage Examples

## 1. Component-Level Permission Checks

### Basic Permission Check in Component

```vue
<script setup lang="ts">
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const {
  canCreateTalent,
  canEditTalent,
  canViewTalent,
  isSuperuser,
  hasPermission
} = usePermissions()

// Custom permission check
const canDeleteTalent = computed(() => {
  return isSuperuser.value || hasPermission(ModuleId.TALENT, 'edit')
})
</script>

<template>
  <div>
    <!-- Show create button only if user can create -->
    <button v-if="canCreateTalent" @click="createTalent">
      Create New Talent
    </button>

    <!-- Show edit button only if user can edit -->
    <button v-if="canEditTalent" @click="editTalent">
      Edit Talent
    </button>

    <!-- Show delete button only for superusers or users with edit permission -->
    <button v-if="canDeleteTalent" @click="deleteTalent">
      Delete Talent
    </button>
  </div>
</template>
```

## 2. Route-Level Permission Protection

### In Component (like AddTalentPage.vue)

```vue
<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissions } from '@/composables/permissions/usePermissions'

const router = useRouter()
const { canAccessTalent, canCreateTalent, canViewTalent, isSuperuser } = usePermissions()

// Check permissions based on route
const hasRequiredPermission = computed(() => {
  if (isSuperuser.value) return true
  
  const isEditMode = !!route.params.id
  if (isEditMode) {
    return canViewTalent.value
  } else {
    return canCreateTalent.value
  }
})

onMounted(() => {
  // Redirect to 403 if no permission
  if (!canAccessTalent.value || !hasRequiredPermission.value) {
    router.push('/403')
    return
  }
  
  // Continue with component logic...
})
</script>
```

## 3. Navigation Menu with Permissions

### Sidebar Component Example

```vue
<script setup lang="ts">
import { computed } from 'vue'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const {
  canAccessTalent,
  canAccessUsers,
  canAccessRoles,
  canAccessClients
} = usePermissions()

const menuItems = computed(() => [
  {
    name: 'Talent',
    path: '/talent',
    icon: Users,
    show: canAccessTalent.value
  },
  {
    name: 'Users',
    path: '/users',
    icon: UserCheck,
    show: canAccessUsers.value
  },
  {
    name: 'Roles',
    path: '/roles',
    icon: Shield,
    show: canAccessRoles.value
  },
  {
    name: 'Clients',
    path: '/clients',
    icon: Building2,
    show: canAccessClients.value
  }
].filter(item => item.show))
</script>

<template>
  <nav>
    <router-link
      v-for="item in menuItems"
      :key="item.name"
      :to="item.path"
      class="nav-item"
    >
      <component :is="item.icon" />
      {{ item.name }}
    </router-link>
  </nav>
</template>
```

## 4. Using Permission Directives

### Template Directives

```vue
<template>
  <div>
    <!-- Show only if user can create in Talent module -->
    <button v-permission="{ moduleId: ModuleId.TALENT, permission: 'create' }">
      Create Talent
    </button>

    <!-- Show only for superusers -->
    <div v-superuser>
      <h3>Admin Panel</h3>
      <button>Delete All Data</button>
    </div>

    <!-- Show if user can access Users module -->
    <section v-module-access="ModuleId.USERS">
      <h2>User Management</h2>
      <!-- User management content -->
    </section>
  </div>
</template>
```

## 5. Form Field Permissions

### Conditional Form Fields

```vue
<script setup lang="ts">
const { canEditTalent, canViewTalent, isSuperuser } = usePermissions()

const isReadOnly = computed(() => {
  return !canEditTalent.value && !isSuperuser.value
})
</script>

<template>
  <form>
    <!-- Editable field for users with edit permission -->
    <input
      v-if="canEditTalent"
      v-model="form.name"
      type="text"
      placeholder="Enter name"
    />
    
    <!-- Read-only field for users with only view permission -->
    <div v-else-if="canViewTalent" class="read-only-field">
      {{ form.name }}
    </div>

    <!-- Hidden field for users without any permission -->
    <div v-else class="permission-denied">
      Access denied
    </div>

    <!-- Submit button only for users who can edit -->
    <button v-if="canEditTalent" type="submit">
      Save Changes
    </button>
  </form>
</template>
```

## 6. API Call Permissions

### Before Making API Calls

```typescript
const { canCreateTalent, canEditTalent } = usePermissions()

const createTalent = async (talentData: any) => {
  // Check permission before API call
  if (!canCreateTalent.value) {
    toast.error('You do not have permission to create talent')
    return
  }

  try {
    await talentApi.create(talentData)
    toast.success('Talent created successfully')
  } catch (error) {
    toast.error('Failed to create talent')
  }
}

const updateTalent = async (id: number, talentData: any) => {
  if (!canEditTalent.value) {
    toast.error('You do not have permission to edit talent')
    return
  }

  try {
    await talentApi.update(id, talentData)
    toast.success('Talent updated successfully')
  } catch (error) {
    toast.error('Failed to update talent')
  }
}
```

## 7. Tab-Based Permissions

### Dynamic Tab Filtering

```vue
<script setup lang="ts">
const { canViewTalent, canEditTalent, isSuperuser } = usePermissions()

const allTabs = [
  { id: 'profile', name: 'Profile', requiredPermission: 'view' },
  { id: 'banking', name: 'Banking', requiredPermission: 'view' },
  { id: 'documents', name: 'Documents', requiredPermission: 'view' },
  { id: 'settings', name: 'Settings', requiredPermission: 'edit' }
]

const availableTabs = computed(() => {
  if (isSuperuser.value) return allTabs

  return allTabs.filter(tab => {
    if (tab.requiredPermission === 'edit') {
      return canEditTalent.value
    }
    return canViewTalent.value
  })
})
</script>

<template>
  <div class="tabs">
    <button
      v-for="tab in availableTabs"
      :key="tab.id"
      @click="activeTab = tab.id"
      :class="{ active: activeTab === tab.id }"
    >
      {{ tab.name }}
    </button>
  </div>
</template>
```

## 8. Error Handling for Permissions

### Permission Error Component

```vue
<template>
  <div v-if="!hasPermission" class="permission-error">
    <div class="error-icon">
      <ShieldX :size="48" />
    </div>
    <h3>Access Denied</h3>
    <p>{{ errorMessage }}</p>
    <button @click="goBack">Go Back</button>
  </div>
  
  <div v-else>
    <!-- Component content -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissions } from '@/composables/permissions/usePermissions'

const router = useRouter()
const { canViewTalent, canEditTalent } = usePermissions()

const hasPermission = computed(() => canViewTalent.value || canEditTalent.value)

const errorMessage = computed(() => {
  return 'You do not have permission to access this talent information.'
})

const goBack = () => {
  router.go(-1)
}
</script>
```

## Key Points

1. **Always check permissions early** - in onMounted or route guards
2. **Use computed properties** for reactive permission checks
3. **Handle both superuser and regular user cases**
4. **Provide clear error messages** when access is denied
5. **Use directives for simple show/hide logic**
6. **Use composables for complex permission logic**
7. **Check permissions before API calls** to provide better UX
