# Permission System Documentation

## Overview

The permission system provides role-based access control (RBAC) throughout the application. It supports both superuser access (full permissions) and granular module-based permissions.

## Architecture

### 1. Types (`src/types/permissions.ts`)
- `ModulePermission` - Individual module permissions (view, edit, create, list)
- `UserRole` - User role with module permissions
- `UserPermissions` - Complete user permission structure
- `ModuleId` - Enum of all available modules

### 2. Store (`src/store/permissions/usePermissionsStore.ts`)
- Centralized permission state management using Pinia
- Permission checking methods
- API integration for fetching permissions

### 3. Composable (`src/composables/permissions/usePermissions.ts`)
- Easy-to-use composable for components
- Computed properties for common permission checks
- Helper methods for navigation and actions

### 4. Directives (`src/directives/permissions.ts`)
- `v-permission` - Show/hide based on specific permission
- `v-superuser` - Show/hide for superusers only
- `v-module-access` - Show/hide based on module access

## Usage Examples

### 1. In Components (Composable)

```vue
<script setup lang="ts">
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const {
  isSuperuser,
  canCreateTalent,
  canEditUsers,
  hasPermission
} = usePermissions()

// Check specific permission
const canDeleteTalent = hasPermission(ModuleId.TALENT, 'edit')
</script>

<template>
  <!-- Conditional rendering -->
  <button v-if="canCreateTalent">Create Talent</button>
  <button v-if="isSuperuser">Admin Action</button>
</template>
```

### 2. Using Directives

```vue
<template>
  <!-- Show only if user can create in Talent module -->
  <div v-permission="{ moduleId: ModuleId.TALENT, permission: 'create' }">
    <button>Create New Talent</button>
  </div>

  <!-- Show only for superusers -->
  <div v-superuser>
    <button>Admin Panel</button>
  </div>

  <!-- Show if user can access Users module -->
  <div v-module-access="ModuleId.USERS">
    <a href="/users">Manage Users</a>
  </div>
</template>
```

### 3. In Navigation (Sidebar Example)

```vue
<script setup lang="ts">
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const { canAccessTalent, canAccessUsers } = usePermissions()

const menuItems = computed(() => [
  { name: 'Talent', show: canAccessTalent },
  { name: 'Users', show: canAccessUsers }
].filter(item => item.show.value))
</script>
```

## Module IDs

```typescript
export enum ModuleId {
  TALENT = 1,
  USERS = 2,
  ROLES = 3,
  CLIENTS = 4,
  EQUIPMENT = 5,
  HISTORY_LOG = 6,
  SETTINGS = 7,
  DASHBOARD = 8,
  REPORTS = 9,
  PROFILE = 10,
  NOTIFICATIONS = 11,
  AUDIT = 12,
  BACKUP = 13,
  SYSTEM = 14
}
```

## Permission Types

- `view` - Can view/read data
- `edit` - Can modify existing data
- `create` - Can create new data
- `list` - Can access/list the module

## API Response Format

### Regular User Response
```json
{
  "is_superuser": false,
  "permissions": {
    "id": 2,
    "role": "Manager",
    "description": "Manager role",
    "module": [
      {
        "module_id": 1,
        "view": true,
        "edit": true,
        "create": false,
        "list": true
      }
    ]
  }
}
```

### Superuser Response
```json
{
  "is_superuser": true,
  "permissions": {},
  "is_active": true
}
```

**Note**: When `is_superuser` is `true`, the permissions object may be empty since superusers have access to everything.

## Initialization

The permission system is automatically initialized in `App.vue`:

```typescript
import { usePermissions } from '@/composables/permissions/usePermissions'

const { initializePermissions } = usePermissions()

onMounted(async () => {
  await initializePermissions()
})
```

## Superuser Behavior

### Key Points
- **Never see 403 pages**: Superusers are never redirected to unauthorized pages
- **All permissions granted**: Superusers bypass all permission checks
- **Empty permissions object**: When `is_superuser: true`, the permissions object may be empty
- **Full access**: Superusers can access all modules, tabs, and actions

### Implementation
```typescript
// Always check superuser first
if (isSuperuser.value) {
  return true // Grant access
}

// Then check specific permissions for regular users
return hasPermission(moduleId, permission)
```

## Best Practices

1. **Use composable for complex logic**: Use `usePermissions()` in components for multiple permission checks
2. **Use directives for simple show/hide**: Use `v-permission` for simple conditional rendering
3. **Check permissions early**: Validate permissions before navigation or API calls
4. **Superuser handling**: Always check `isSuperuser` first - they have all permissions
5. **Error handling**: Handle permission loading errors gracefully
6. **Talent permissions**: Use `hasAnyTalentPermission()` for sidebar visibility

## Common Patterns

### Navigation Guard
```typescript
// In router or component
if (!canAccess(ModuleId.USERS)) {
  router.push('/unauthorized')
}
```

### Action Buttons
```vue
<template>
  <div class="action-buttons">
    <button v-if="canCreate(ModuleId.TALENT)">Create</button>
    <button v-if="canEdit(ModuleId.TALENT)">Edit</button>
    <button v-if="isSuperuser">Delete</button>
  </div>
</template>
```

### Form Fields
```vue
<template>
  <form>
    <input v-if="canEdit(ModuleId.TALENT)" />
    <span v-else>{{ readOnlyValue }}</span>
  </form>
</template>
```

## Troubleshooting

1. **Permissions not loading**: Check API endpoint and network requests
2. **Always showing/hiding**: Verify module IDs match backend configuration
3. **Superuser not working**: Check `is_superuser` field in API response
4. **Directives not working**: Ensure permission plugin is registered in main.ts
