# Updated Permission System Documentation

## Overview

The permission system has been updated to handle your 14 specific modules with proper talent-related grouping and history log restrictions.

## Module Structure

### API Module IDs (from your API response)
```
1. "Talent Profile"
2. "Talent Banking Info" 
3. "Talent Documents"
4. "Talent Work History"
5. "Talent BPO Related Info"
6. "Talent Equipment & Software"
7. "Talent Health"
8. "Talent 3rd Party Integrations"
9. "Talent History Logs"
10. "Users"
11. "Roles"
12. "Clients"
13. "Equipment"
14. "System History Logs"
```

### Permission Rules Implemented

#### 1. Talent Section Visibility
- **Rule**: Show "Talent" in sidebar only if user has at least one permission (create, list, edit, view) for ANY talent-related module (modules 1-9)
- **Implementation**: `hasAnyTalentPermission()` checks modules 1-9 from API response

#### 2. Individual Talent Tabs
- **Rule**: Each talent tab is visible based on specific module permissions
- **Implementation**: Each tab checks its corresponding module ID for any permission

#### 3. History Logs Restriction
- **Rule**: History logs (both talent and system) are only visible to superusers
- **Implementation**: 
  - Talent History Logs (module 9): Only shown to superusers
  - System History Logs (module 14): Only shown to superusers

#### 4. Other Modules
- **Rule**: Standard permission checking - visible if user has any permission for that specific module
- **Implementation**: Individual permission checks for Users, Roles, Clients, Equipment

## Code Structure

### 1. Module Mapping (`src/utils/moduleMapping.ts`)
```typescript
// Maps API module IDs to enum values and names
export const MODULE_ID_TO_ENUM: Record<number, ModuleId> = {
  1: ModuleId.TALENT_PROFILE,
  2: ModuleId.TALENT_BANKING_INFO,
  // ... etc
}
```

### 2. Permission Store (`src/store/permissions/usePermissionsStore.ts`)
- Updated to map API module IDs to enum values
- `hasAnyTalentPermission()` checks modules 1-9
- `canAccessHistoryLogs()` only returns true for superusers

### 3. Permission Composable (`src/composables/permissions/usePermissions.ts`)
- Individual access helpers for each talent module
- Grouped talent access via `canAccessTalent`
- History log access restricted to superusers

### 4. Sidebar Component (`src/components/Sidebar.vue`)
- Uses `hasAnyTalentPermission()` for talent visibility
- Uses `canAccessHistoryLogs()` for history log visibility

### 5. Talent Tabs (`src/views/talent/AddTalentPage.vue`)
- Each tab mapped to specific module ID
- History log tab only visible to superusers
- Individual permission checking per tab

## Usage Examples

### Check if user can access talent section
```typescript
const { canAccessTalent } = usePermissions()
// Returns true if user has ANY permission for modules 1-9
```

### Check specific talent module permissions
```typescript
const { canViewTalentProfile, canEditTalentBanking } = usePermissions()
// Individual module permission checks
```

### Check history log access
```typescript
const { canAccessHistoryLog } = usePermissions()
// Only returns true for superusers
```

## API Response Handling

Your API response structure is properly handled:
```json
{
  "data": {
    "is_superuser": false,
    "permissions": {
      "role": "HR",
      "module": [
        {
          "module_id": 1,
          "view": true,
          "edit": true,
          "create": true,
          "list": true
        }
        // ... more modules
      ]
    }
  }
}
```

The system automatically maps `module_id` values to the correct enum values and applies the permission rules.

## Testing

Use the `PermissionDemoComponent` to test the permission system:
```vue
<PermissionDemoComponent />
```

This component shows:
- User role information
- Sidebar visibility status
- Individual talent module permissions
- Applied permission rules
- Raw API data

## Key Features

1. **Talent Grouping**: All talent modules (1-9) are grouped for sidebar visibility
2. **Individual Tab Control**: Each talent tab has individual permission checking
3. **Superuser Restrictions**: History logs only visible to superusers
4. **Flexible Permissions**: Supports view, edit, create, list permissions per module
5. **API Compatibility**: Works with your existing API response structure
