import { Directive, DirectiveBinding } from 'vue'
import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { ModuleId, PermissionType } from '@/types/permissions'

interface PermissionBinding {
  moduleId: ModuleId
  permission: PermissionType
}

// Permission directive
export const vPermission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding>) {
    const permissionsStore = usePermissionsStore()
    const { moduleId, permission } = binding.value

    if (!permissionsStore.hasPermission(moduleId, permission)) {
      // Hide the element if user doesn't have permission
      el.style.display = 'none'
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding>) {
    const permissionsStore = usePermissionsStore()
    const { moduleId, permission } = binding.value

    if (!permissionsStore.hasPermission(moduleId, permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// Superuser directive - only show for superusers
export const vSuperuser: Directive = {
  mounted(el: HTMLElement) {
    const permissionsStore = usePermissionsStore()

    if (!permissionsStore.isSuperuser) {
      el.style.display = 'none'
    }
  },

  updated(el: HTMLElement) {
    const permissionsStore = usePermissionsStore()

    if (!permissionsStore.isSuperuser) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// Module access directive - show if user can access the module
export const vModuleAccess: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding<ModuleId>) {
    const permissionsStore = usePermissionsStore()
    const moduleId = binding.value

    if (!permissionsStore.canAccess(moduleId)) {
      el.style.display = 'none'
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding<ModuleId>) {
    const permissionsStore = usePermissionsStore()
    const moduleId = binding.value

    if (!permissionsStore.canAccess(moduleId)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}
