import { GeneralResponse } from "@/types/talent"
import { SettingsResponse, UpdatePersonalInfoRequest, UpdatePasswordRequest } from "@/types/settings"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, patchCallInstace, putCallInstace } from "@/utils/http-handler"

export const settingsApi = {
  // Get current user settings
  getUserSettings: async (): Promise<GeneralResponse<SettingsResponse>> => {
    const response = await getCallInstace('/profile/get')
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  // Update personal information
  updatePersonalInfo: async (data: UpdatePersonalInfoRequest): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace('/profile/update', snakeCaseData)
    return response
  },

  // Update password
  updatePassword: async (data: UpdatePasswordRequest): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await patchCallInstace('/profile/update-password', snakeCaseData)
    return response
  },

  getUserPermissions: async (): Promise<GeneralResponse<any>> => {
    const response = await getCallInstace('/profile/get-user-permissions')
    return response
  }
}
