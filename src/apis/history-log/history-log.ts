import type { 
  HistoryLog, 
  HistoryLogResponse, 
  HistoryLogCreate, 
  GeneralResponse,
  HistoryLogFilters 
} from '@/types/history-log'
import { getCallInstace, postCallInstace } from '@/utils/http-handler'
import { convertKeysToCamelCase, convertKeysToSnakeCase } from '@/utils/case-converter'

const prefix = '/talent/activity'

export const historyLogApi = {
  /**
   * Get all history logs with optional filters
   */
  getHistoryLogs: async (filters?: Partial<HistoryLogFilters>): Promise<GeneralResponse<HistoryLog[]>> => {
    const params = new URLSearchParams()
    
    if (filters?.searchQuery) {
      params.append('search', filters.searchQuery)
    }
    if (filters?.startDate) {
      params.append('start_date', filters.startDate.toISOString().split('T')[0])
    }
    if (filters?.endDate) {
      params.append('end_date', filters.endDate.toISOString().split('T')[0])
    }
    if (filters?.eventType) {
      params.append('event_type', filters.eventType)
    }
    if (filters?.status && filters.status !== 'all') {
      params.append('status', filters.status)
    }
    if (filters?.user) {
      params.append('user', filters.user)
    }

    const queryString = params.toString()
    const url = queryString ? `${prefix}?${queryString}` : prefix
    
    const response = await getCallInstace(url)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Get history logs by talent profile ID
   */
  getHistoryLogsByTalentId: async (talentId: number): Promise<GeneralResponse<HistoryLog[]>> => {
    const response = await getCallInstace(`${prefix}/get-by-talent/${talentId}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Get history logs by user ID
   */
  getHistoryLogsByUserId: async (userId: number): Promise<GeneralResponse<HistoryLog[]>> => {
    const response = await getCallInstace(`${prefix}/user/${userId}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Create a new history log entry
   */
  createHistoryLog: async (data: HistoryLogCreate): Promise<GeneralResponse<HistoryLogResponse>> => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace(prefix, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Get history log by ID
   */
  getHistoryLogById: async (id: number): Promise<GeneralResponse<HistoryLog>> => {
    const response = await getCallInstace(`${prefix}/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  }
}
