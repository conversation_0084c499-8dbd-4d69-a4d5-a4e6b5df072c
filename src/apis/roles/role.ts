import { Role, RolePermissionRequest } from "@/types/roles"
import { GeneralResponse } from "@/types/talent"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"
import { Module } from "@/types/roles"

export const rolesAndModulesApi = {
  getRoles: async (): Promise<Role[]> => {
    const response = await getCallInstace('/master/role/list')
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  getModules: async (): Promise<GeneralResponse<Module[]>> => {
    const response = await getCallInstace('/master/module/')
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  createRole: async (data: RolePermissionRequest) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return await postCallInstace('/master/role/create', snakeCaseData)
  },

  getRoleById: async (id: number) => {
    const response = await getCallInstace(`/master/role/get-with-permissions/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  updateRole: async (id: number, data: RolePermissionRequest) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return await putCallInstace(`/master/role/update/${id}`, snakeCaseData)
  }
}
