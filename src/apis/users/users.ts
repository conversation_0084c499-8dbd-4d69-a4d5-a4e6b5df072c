import { GeneralResponse } from "@/types/talent"
import { User } from "@/types/user"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

export const usersApi = {
  getUsers: async (): Promise<GeneralResponse<User[]>> => {
    const response = await getCallInstace('/users/list')
    response.data = convertKeysToCamelCase(response.data)
    return response
  },
  getUserById: async (id: number): Promise<GeneralResponse<User>> => {
    const response = await getCallInstace(`/users/get/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },
  createUser: async (data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return await postCallInstace('/users', snakeCaseData)
  },
  updateUser: async (id: number, data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return await putCallInstace(`/users/update/${id}`, snakeCaseData)
  }
}
