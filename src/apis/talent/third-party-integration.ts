import { GeneralResponse } from "@/types/talent"
import { 
  ThirdPartyIntegrationCreate, 
  ThirdPartyIntegrationUpdate, 
  ThirdPartyIntegrationResponse 
} from "@/types/third-party-integration"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/talent/third-party-integration'

export const thirdPartyIntegrationApi = {
  /**
   * Get all third party integrations for a specific talent
   */
  async getByTalentId(talentId: number): Promise<GeneralResponse<ThirdPartyIntegrationResponse[]>> {
    const response = await getCallInstace(`${prefix}/get-by-talent/${talentId}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Create a new third party integration
   */
  async create(data: ThirdPartyIntegrationCreate): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Update an existing third party integration
   */
  async update(integrationId: number, data: ThirdPartyIntegrationUpdate): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace(`${prefix}/update/${integrationId}`, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  /**
   * Delete a third party integration (sets isActive to false)
   */
  async delete(integrationId: number): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    return await this.update(integrationId, { isActive: false })
  },

  /**
   * Toggle integration isActive state
   */
  async toggle(integrationId: number, currentIsActive: boolean): Promise<GeneralResponse<ThirdPartyIntegrationResponse>> {
    return await this.update(integrationId, { isActive: !currentIsActive })
  }
}
