import { EmergencyContact, EmergencyContactSnakeCase, GeneralResponse, TalentProfileResponse } from "@/types/talent"
import { convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/talent/emergency-contact'

export const createTalentEmergencyContact = async (data: EmergencyContact): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
  return response.data
}

export const getTalentEmergencyContactById = async (talent_id: number): Promise<GeneralResponse<EmergencyContactSnakeCase[]>> => {
  const response = await getCallInstace(`${prefix}/get-all/${talent_id}`)
  return response
}

export const updateTalentEmergencyContacts = async (id: number, data: EmergencyContact): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await putCallInstace(`${prefix}/update/${id}`, snakeCaseData)
  return response.data
}
