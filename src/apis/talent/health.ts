import {
  CreateOrUpdateAllergy,
  CreateOrUpdateChronicCondition,
  CreateOrUpdateOngoingHealthIssue,
  CreateOrUpdatePastHealthIssue,
} from "@/types/health";
import { GeneralResponse } from "@/types/talent";
import {
  convertKeysToCamelCase,
  convertKeysToSnakeCase,
} from "@/utils/case-converter";
import { getCallInstace, postCallInstace } from "@/utils/http-handler";

export const healthApi = {
  createAllergies: async (
    data: CreateOrUpdateAllergy
  ): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      "/talent/allergies/create-or-update",
      snakeCaseData
    );
    return response;
  },

  createOngoingHealthIssues: async (
    data: CreateOrUpdateOngoingHealthIssue
  ): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      "/talent/ongoing-health/create-or-update",
      snakeCaseData
    );
    return response;
  },

  createPastHealthIssues: async (
    data: CreateOrUpdatePastHealthIssue
  ): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      "/talent/past-health/create-or-update",
      snakeCaseData
    );
    return response;
  },

  createChronicConditions: async (
    data: CreateOrUpdateChronicCondition
  ): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      "/talent/chronic-conditions/create-or-update",
      snakeCaseData
    );
    return response;
  },

  getAllergies: async (
    talent_id: number
  ): Promise<GeneralResponse<CreateOrUpdateAllergy>> => {
    const response = await getCallInstace(`/talent/allergies/get/${talent_id}`);
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },

  getOnGoingHealthIssues: async (
    talent_id: number
  ): Promise<GeneralResponse<CreateOrUpdateOngoingHealthIssue>> => {
    const response = await getCallInstace(
      `/talent/ongoing-health/get-by-talent/${talent_id}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },

  getPastHealthIssues: async (
    talent_id: number
  ): Promise<GeneralResponse<CreateOrUpdatePastHealthIssue>> => {
    const response = await getCallInstace(
      `/talent/past-health/get-by-talent/${talent_id}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },

  getChronicConditions: async (
    talent_id: number
  ): Promise<GeneralResponse<CreateOrUpdateChronicCondition>> => {
    const response = await getCallInstace(
      `/talent/chronic-conditions/get-by-talent/${talent_id}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },
};
