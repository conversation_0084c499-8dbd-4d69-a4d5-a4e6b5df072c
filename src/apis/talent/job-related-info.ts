import type { JobRelatedInfoCreate } from '@/types/job-related-info'
import { convertKeysToCamelCase, convertKeysToSnakeCase } from '@/utils/case-converter'
import { getCallInstace, postCallInstace, putCallInstace } from '@/utils/http-handler'

export const jobRelatedInfoApi = {
  async create(data: JobRelatedInfoCreate) {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return await postCallInstace('/talent/client-info/create', snakeCaseData)
  },

  async getByTalentId(talentId: number) {
    const response = await getCallInstace(`/talent/client-info/get-talent-info/${talentId}`)
    response.data = convertKeysToCamelCase({...response.data})
    return response
  },

  async update(id: number, data: Partial<JobRelatedInfoCreate>) {
    const snakeCaseData = convertKeysToSnakeCase(data)
    return putCallInstace(`/talent/client-info/update/${id}`, snakeCaseData)
  }
}
