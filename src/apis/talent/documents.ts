import { DocumentResponse } from "@/types/documents"
import { GeneralResponse } from "@/types/talent"
import { convertKeysToCamelCase } from "@/utils/case-converter"
import { getCallInstace, postAxiosFileInstance} from "@/utils/http-handler"

const prefix = '/talent/documents'

export const createTalentDocuments = async (data: any) => {
  const response = await postAxiosFileInstance(`${prefix}/create`, data)
  return response.data
}

export const getTalentDocumentsApi = async (id: number): Promise<GeneralResponse<DocumentResponse[]>> => {
  const response = await getCallInstace(`${prefix}/list/${id}`)
  response.data = convertKeysToCamelCase(response.data)
  return response
}

