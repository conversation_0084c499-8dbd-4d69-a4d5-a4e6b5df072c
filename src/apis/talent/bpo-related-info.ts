import { BpoRelatedInfoCreate, VacationsInfo } from "@/types/bpo-related-info";
import { GeneralResponse } from "@/types/talent";
import {
  convertKeysToCamelCase,
  convertKeysToSnakeCase,
} from "@/utils/case-converter";
import { getCallInstace, patchCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler";

const prefix = "/talent/positions";
const vacationsPrefix = "/talent/vacation";

export const bpoRelatedInfoApi = {
  geetAvaialbleEquipment: async (): Promise<GeneralResponse<any>> => {
    const response = await getCallInstace(
      `/talent/equipment/get-available`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },

  create: async (
    data: BpoRelatedInfoCreate
  ): Promise<GeneralResponse<BpoRelatedInfoCreate>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      `${prefix}/create-or-update`,
      snakeCaseData
    );
    return response;
  },
  getByTalentId: async (
    talentProfileId: number
  ): Promise<GeneralResponse<BpoRelatedInfoCreate>> => {
    const response = await getCallInstace(
      `${prefix}/get-by-talent/${talentProfileId}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },
  createVacationMapping: async (
    data: VacationsInfo
  ): Promise<GeneralResponse<VacationsInfo>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      `${vacationsPrefix}/create-or-update`,
      snakeCaseData
    );
    return response;
  },
  getVacationMapping: async (
    talentProfileId: number
  ): Promise<GeneralResponse<VacationsInfo>> => {
    const response = await getCallInstace(
      `${vacationsPrefix}/get-vacation-mapping/${talentProfileId}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },
  createEquipment: async (data: any): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      `/talent/equipment/create`,
      snakeCaseData
    );
    return response;
  },
  getEquipment: async (talentProfileId: number): Promise<GeneralResponse<any>> => {
    const response = await getCallInstace(
      `/talent/equipment/get-by-talent/${talentProfileId}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },
  updateEquipment: async (id: number, data?: any): Promise<GeneralResponse<any>> => {
    const snakeCaseData = data ? convertKeysToSnakeCase(data) : {};
    const response = await patchCallInstace(
      `/talent/equipment/update/${id}`, snakeCaseData
    );
    return response;
  },
  createSoftware: async (data: any): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await postCallInstace(
      `/talent/software/create`,
      snakeCaseData
    );
    return response;
  },
  getSoftware: async (talentProfileId: number): Promise<GeneralResponse<any>> => {
    const response = await getCallInstace(
      `/talent/software/get-by-talent/${talentProfileId}`
    );
    response.data = convertKeysToCamelCase(response.data);
    return response;
  },
  updateSoftware: async (data: any): Promise<GeneralResponse<any>> => {
    const snakeCaseData = convertKeysToSnakeCase(data);
    const response = await putCallInstace(
      `/talent/software/update/${data.id}`,
      snakeCaseData
    );
    return response;
  },
};
