import { getCallInstace, postCallInstace, putCallInstace } from '@/utils/http-handler';
import { GeneralResponse } from '@/types/talent'

import type { BankingInformation, BankingInformationCreate, BankingInformationUpdate } from '@/types/banking'
import { convertKeysToCamelCase, convertKeysToSnakeCase } from '@/utils/case-converter';

export const bankingApi = {
  create: async (data: BankingInformationCreate): Promise<GeneralResponse<BankingInformation>> => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace('/talent/banking/create', snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  getByTalentId: async (talentProfileId: number): Promise<GeneralResponse<BankingInformation>> => {
    const response = await getCallInstace(`/talent/banking/get-by-talent/${talentProfileId}`)
    if (response.data) {
      response.data = convertKeysToCamelCase(response.data)
    }
    return response
  },

  update: async (bankingId: number, data: BankingInformationUpdate): Promise<GeneralResponse<BankingInformation>> => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace(`/talent/banking/update/${bankingId}`, snakeCaseData)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },
}
