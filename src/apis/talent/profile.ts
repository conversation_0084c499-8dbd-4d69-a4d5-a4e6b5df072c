import type { TalentProfileCreate, TalentProfileResponse, GeneralResponse } from '@/types/talent'
import { deleteCallInstace, getCallInstace, postAxiosFileInstance, postCallInstace, putCallInstace } from '@/utils/http-handler'
import { convertKeysToCamelCase, convertKeysToSnakeCase } from '@/utils/case-converter'

const prefix = '/talent/profile'

export const createTalentProfile = async (data: TalentProfileCreate): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
  return response
}

export const getTalentProfiles = async (): Promise<GeneralResponse<TalentProfileResponse[]>> => {
  const response = await getCallInstace(`${prefix}/all`)
  return response.data
}

export const getTalentProfileById = async (id: number): Promise<GeneralResponse<TalentProfileResponse>> => {
  const response = await getCallInstace(`${prefix}/detail/${id}`)
  response.data = convertKeysToCamelCase(response.data)
  return response
}

export const updateTalentProfileApi = async (id: number, data: Partial<TalentProfileCreate>): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await putCallInstace(`${prefix}/update/${id}`, snakeCaseData)
  return response.data
}

export const deleteTalentProfile = async (id: number): Promise<GeneralResponse<null>> => {
  const response = await deleteCallInstace(`${prefix}/delete/${id}`)
  return response
}


export const getAllTalents = async () => {
  const response = await getCallInstace(`${prefix}/all`)
  response.data = convertKeysToCamelCase(response.data)
  return response.data
}

export const uploadProfilePicture = async (talentId: number, file: File): Promise<GeneralResponse<TalentProfileResponse>> => {
  const response = await postAxiosFileInstance(`${prefix}/upload-profile-picture/${talentId}`, { file })
  return response
}

export const updateProfilePicture = async (talentId: number, file: File): Promise<GeneralResponse<TalentProfileResponse>> => {
  const response = await postAxiosFileInstance(`${prefix}/update-profile-picture/${talentId}`, { file })
  return response
}

export const deactivateTalent = async (talentId: number, data: { is_active: boolean; reason: string }) => {
  return await putCallInstace(`${prefix}/deactivate-talent/${talentId}`, data)
}
