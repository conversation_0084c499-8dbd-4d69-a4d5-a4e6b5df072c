import { GeneralResponse, TalentProfileResponse, TalentSkillSetCreate } from "@/types/talent"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/talent/skill-sets'

export const createTalentSkillSet = async (data: TalentSkillSetCreate): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
  return response.data
}

export const getTalentSkillSetById = async (talent_id: number): Promise<GeneralResponse<TalentSkillSetCreate>> => {
  const response = await getCallInstace(`${prefix}/get-by-talent/${talent_id}`)
  response.data = (response.data) ? convertKeysToCamelCase(response.data) : null
  return response
}

export const updateTalentSkillSet = async (talent_id: number, data: TalentSkillSetCreate): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await putCallInstace(`${prefix}/update/${talent_id}`, snakeCaseData)
  return response.data
}
