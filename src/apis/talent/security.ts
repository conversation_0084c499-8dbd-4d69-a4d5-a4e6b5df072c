import { GeneralResponse, TalentProfileResponse, TalentSecurityCreate } from "@/types/talent"
import { convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/talent/payroll'

export const createTalentSecurity = async (data: TalentSecurityCreate): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
  return response
}

export const getTalentSecurityById = async (talent_id: number): Promise<GeneralResponse<TalentSecurityCreate>> => {
  const response = await getCallInstace(`${prefix}/get-payroll-information/${talent_id}`)
  return response
}

export const updateTalentSecurity = async (id: number, data: TalentSecurityCreate): Promise<GeneralResponse<TalentProfileResponse>> => {
  const snakeCaseData = convertKeysToSnakeCase(data)
  const response = await putCallInstace(`${prefix}/update/${id}`, snakeCaseData)
  return response
}
