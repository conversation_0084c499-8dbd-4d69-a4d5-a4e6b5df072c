import { GeneralResponse } from "@/types/talent"
import { convertKeysToCamelCase } from "@/utils/case-converter"
import { getCallInstace, postAxiosFileInstance } from "@/utils/http-handler"

const prefix = '/talent/it-documents'

export interface ITDocumentCreate {
  talentProfileId: number
  documentType: string
  file: File
  notes?: string
}

export interface ITDocumentResponse {
  id: number
  createdAt?: string
  updatedAt?: string
  talentProfileId: number
  documentType: string
  fileName: string
  url: string
  notes?: string
}

export const createITDocument = async (data: ITDocumentCreate): Promise<GeneralResponse<ITDocumentResponse>> => {
  const formData = {
    talent_profile_id: data.talentProfileId,
    document_type: data.documentType,
    file: data.file,
    notes: data.notes || undefined,
  }

  const response = await postAxiosFileInstance(`${prefix}/create`, {...formData})
  return response
}

export const getITDocumentsByTalentId = async (talentId: number): Promise<GeneralResponse<ITDocumentResponse[]>> => {
  const response = await getCallInstace(`${prefix}/get-all/${talentId}`)
  if (response.data && Array.isArray(response.data)) {
    response.data = response.data.map((doc: any) => convertKeysToCamelCase(doc))
  }
  return response
}

export const getITDocumentById = async (documentId: number): Promise<GeneralResponse<ITDocumentResponse>> => {
  const response = await getCallInstace(`${prefix}/get-by-id/${documentId}`)
  response.data = convertKeysToCamelCase(response.data)
  return response
}
