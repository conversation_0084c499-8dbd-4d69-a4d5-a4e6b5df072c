import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter";
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler";
import { GeneralResponse } from "@/types/talent";
import { ClientForm } from "../../types/master/client";

const prefix = '/master/clients'
  
export const useClientsApi = {
  
  createClient: async (data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
    return response
  },

  getAllClients: async (): Promise<GeneralResponse<ClientForm[]>> => {
    const response = await getCallInstace(`${prefix}/list`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  getClientById: async (id: number): Promise<GeneralResponse<ClientForm>> => {
    const response = await getCallInstace(`${prefix}/detail/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  getAssignedEmployees: async (id: number): Promise<GeneralResponse<any[]>> => {
    const response = await getCallInstace(`${prefix}/assigned-employees/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },
   
  updateClient: async (id: number, data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace(`${prefix}/update/${id}`, snakeCaseData)
    return response
  }
}
