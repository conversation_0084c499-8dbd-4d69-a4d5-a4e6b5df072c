import type { Equipment } from '@/types/equipment'
import { GeneralResponse } from "@/types/talent"
import { convertKeysToCamelCase, convertKeysToSnakeCase } from "@/utils/case-converter"
import { getCallInstace, postCallInstace, putCallInstace } from "@/utils/http-handler"

const prefix = '/master/equipment'

export const useEquipmentsApi = {
  // Legacy API methods for backward compatibility
  createEquipment: async (data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await postCallInstace(`${prefix}/create`, snakeCaseData)
    return response
  },

  getAllEquipments: async (): Promise<GeneralResponse<Equipment[]>> => {
    const response = await getCallInstace(`${prefix}/list-all`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  getEquipmentById: async (id: number): Promise<GeneralResponse<Equipment>> => {
    const response = await getCallInstace(`${prefix}/get-by-id/${id}`)
    response.data = convertKeysToCamelCase(response.data)
    return response
  },

  updateEquipment: async (id: number, data: any) => {
    const snakeCaseData = convertKeysToSnakeCase(data)
    const response = await putCallInstace(`${prefix}/update/${id}`, snakeCaseData)
    return response
  }
}
