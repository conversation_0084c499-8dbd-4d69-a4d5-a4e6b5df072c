
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-sans bg-gray-50;
  }
}

@layer components {
  .sidebar-item {
    @apply flex items-center py-3 mx-1 text-gray-600 rounded-lg transition-colors duration-200 cursor-pointer hover:bg-amber-50 hover:text-primary-600;
  }
  
  .sidebar-item.active {
    @apply bg-amber-50 text-primary-600;
  }
  
  .table-row {
    @apply border-b border-gray-100 transition-colors duration-150 hover:bg-gray-50;
  }

  /* Material Design Input Styles */
  .material-input {
    @apply px-4 py-3 bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:shadow-md;
  }

  .material-select {
    @apply px-4 py-3 bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:shadow-md;
  }

  .material-textarea {
    @apply px-4 py-3 bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:shadow-md;
  }

  .material-button {
    @apply px-6 py-3 font-medium rounded-lg shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .material-card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .material-section {
    @apply p-6 bg-white rounded-xl border border-gray-200 shadow-sm transition-all duration-200;
  }

  /* Material Design Label Styles */
  .material-label {
    @apply block mb-2 text-sm font-medium text-gray-700;
  }

  /* Material Design Phone Input Styles */
  .material-phone-select {
    @apply px-3 py-3 bg-white rounded-l-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:shadow-md;
  }

  .material-phone-input {
    @apply flex-1 px-4 py-3 bg-white rounded-r-lg border-t border-r border-b border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hover:shadow-md;
  }

  /* Floating Label Styles */
  .floating-label-container {
    @apply relative;
  }

  .floating-label {
    @apply absolute top-3 left-4 px-1 text-sm text-gray-500 bg-white transition-all duration-200 pointer-events-none;
  }

  .floating-label.active {
    @apply -top-2 left-3 text-xs text-orange-600 bg-white;
  }

  .floating-input {
    @apply px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none hover:shadow-md;
    border-color: #e5e7eb; /* Tailwind gray-300 */
  }
  .floating-input:focus,
  .floating-input.active {
    border-color: #fb923c !important; /* Tailwind orange-500 */
    box-shadow: 0 0 0 2px #fb923c33;
  }

  .floating-select {
    @apply px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none hover:shadow-md;
    border-color: #e5e7eb; /* Tailwind gray-300 */
  }
  .floating-select:focus,
  .floating-select.active {
    border-color: #fb923c !important; /* Tailwind orange-500 */
    box-shadow: 0 0 0 2px #fb923c33;
  }

  .floating-textarea {
    @apply px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none hover:shadow-md;
    border-color: #e5e7eb; /* Tailwind gray-300 */
  }
  .floating-textarea:focus,
  .floating-textarea.active {
    border-color: #fb923c !important; /* Tailwind orange-500 */
    box-shadow: 0 0 0 2px #fb923c33;
  }

  /* Tab Navigation Styles */
  .tab-navigation {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
    scroll-behavior: smooth;
    cursor: grab;
  }

  .tab-navigation:active {
    cursor: grabbing;
  }

  .tab-navigation::-webkit-scrollbar {
    height: 4px;
  }

  .tab-navigation::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 2px;
  }

  .tab-navigation::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
  }

  .tab-navigation::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Tab Button Enhancements */
  .tab-button {
    transition: all 0.2s ease-in-out;
    position: relative;
  }

  .tab-button:hover {
    transform: translateY(-1px);
  }

  .tab-button.active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  /* Scroll fade indicators */
  .tab-navigation::before,
  .tab-navigation::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
    transition: opacity 0.3s ease;
  }

  .tab-navigation::before {
    left: 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  }

  .tab-navigation::after {
    right: 0;
    background: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  }

  /* Hide fade indicators when not needed */
  .tab-navigation:not(.scrollable)::before,
  .tab-navigation:not(.scrollable)::after {
    opacity: 0;
  }
}
