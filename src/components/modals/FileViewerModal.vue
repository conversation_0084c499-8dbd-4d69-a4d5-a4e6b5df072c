<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
    @click="closeModal"
  >
    <div
      class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden"
      @click.stop
    >
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <FileText :size="20" class="text-orange-500" />
          <div>
            <h3 class="text-lg font-medium text-gray-900">{{ fileName || 'Document Viewer' }}</h3>
            <p class="text-sm text-gray-500">{{ fileSize }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <!-- Download Button -->
          <a
            :href="fileUrl"
            :download="fileName"
            class="flex items-center px-3 py-1.5 space-x-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
          >
            <Download :size="14" />
            <span>Download</span>
          </a>
          <!-- Close Button -->
          <button
            @click="closeModal"
            class="p-2 text-gray-400 rounded-md hover:text-gray-600 hover:bg-gray-100 transition-colors"
          >
            <X :size="20" />
          </button>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="flex-1 overflow-auto" style="max-height: calc(90vh - 80px);">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center h-64">
          <div class="w-8 h-8 border-b-2 border-orange-500 rounded-full animate-spin"></div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="flex flex-col items-center justify-center h-64 text-center">
          <AlertCircle :size="48" class="mb-4 text-red-400" />
          <h4 class="mb-2 text-lg font-medium text-gray-900">Unable to preview file</h4>
          <p class="mb-4 text-sm text-gray-500">{{ error }}</p>
          <a
            :href="fileUrl"
            :download="fileName"
            class="flex items-center px-4 py-2 space-x-2 text-sm font-medium text-white bg-orange-500 rounded-md hover:bg-orange-600 transition-colors"
          >
            <Download :size="16" />
            <span>Download File</span>
          </a>
        </div>

        <!-- File Content -->
        <div v-else class="p-4">
          <!-- PDF Viewer -->
          <div v-if="fileType === 'pdf'" class="w-full">
            <iframe
              :src="fileUrl"
              class="w-full h-96 border border-gray-200 rounded-md"
              title="PDF Viewer"
            ></iframe>
          </div>

          <!-- Image Viewer -->
          <div v-else-if="fileType === 'image'" class="flex justify-center">
            <img
              :src="fileUrl"
              :alt="fileName"
              class="max-w-full max-h-96 rounded-md shadow-sm"
            />
          </div>

          <!-- Text Viewer -->
          <div v-else-if="fileType === 'text'" class="bg-gray-50 rounded-md p-4">
            <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ textContent }}</pre>
          </div>

          <!-- Unsupported File Type -->
          <div v-else class="flex flex-col items-center justify-center h-64 text-center">
            <FileText :size="48" class="mb-4 text-gray-400" />
            <h4 class="mb-2 text-lg font-medium text-gray-900">Preview not available</h4>
            <p class="mb-4 text-sm text-gray-500">
              This file type cannot be previewed in the browser.
            </p>
            <a
              :href="fileUrl"
              :download="fileName"
              class="flex items-center px-4 py-2 space-x-2 text-sm font-medium text-white bg-orange-500 rounded-md hover:bg-orange-600 transition-colors"
            >
              <Download :size="16" />
              <span>Download File</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { FileText, X, Download, AlertCircle } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  fileUrl: string
  fileName?: string
  fileSize?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const isLoading = ref(false)
const error = ref<string | null>(null)
const textContent = ref('')

// Determine file type from URL or filename
const fileType = computed(() => {
  const url = props.fileUrl.toLowerCase()
  const name = props.fileName?.toLowerCase() || ''
  
  if (url.includes('data:application/pdf') || name.endsWith('.pdf')) {
    return 'pdf'
  } else if (url.includes('data:image/') || /\.(jpg|jpeg|png|gif|webp)$/.test(name)) {
    return 'image'
  } else if (url.includes('data:text/') || /\.(txt|md|json|xml|csv)$/.test(name)) {
    return 'text'
  }
  
  return 'unsupported'
})

// Load text content for text files
const loadTextContent = async () => {
  if (fileType.value !== 'text') return
  
  try {
    isLoading.value = true
    error.value = null
    
    if (props.fileUrl.startsWith('data:')) {
      // Handle base64 encoded text
      const base64Data = props.fileUrl.split(',')[1]
      textContent.value = atob(base64Data)
    } else {
      // Handle regular URL
      const response = await fetch(props.fileUrl)
      if (!response.ok) throw new Error('Failed to load file')
      textContent.value = await response.text()
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load file content'
  } finally {
    isLoading.value = false
  }
}

// Watch for file changes
watch(() => props.fileUrl, () => {
  if (props.isOpen && props.fileUrl) {
    loadTextContent()
  }
}, { immediate: true })

const closeModal = () => {
  emit('close')
}

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.isOpen) {
    closeModal()
  }
}

// Add/remove event listeners
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.addEventListener('keydown', handleKeydown)
    document.body.style.overflow = 'hidden'
  } else {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
  }
})
</script>
