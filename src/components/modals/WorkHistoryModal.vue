<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { X, Calendar, ChevronDown } from 'lucide-vue-next'
import CalendarComponent from '@/components/Calendar.vue'
import { datePickerFormatter } from '@/utils/date-util'
import type { JobRelatedInfoCreate } from '@/types/job-related-info'
import type { ClientForm } from '@/types/master/client'

interface Props {
  isOpen: boolean
  isEdit: boolean
  workHistoryData?: JobRelatedInfoCreate
  clients: ClientForm[]
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: JobRelatedInfoCreate): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form data
const formData = ref<JobRelatedInfoCreate>({
  talentProfileId: 0,
  isFte: false,
  isContract: false,
  isOther: false,
  campaign: '',
  clientManager: '',
  reportingDepartment: '',
  positionType: '',
  role: '',
  title: '',
  clientId: 0,
  location: '',
  startDate: '',
  endDate: '',
  workDays: [],
  shiftStartTime: '',
  shiftEndTime: '',
  timeZone: '',
  baseWage: '',
  currency: '',
  frequency: '',
  paidDaysOff: '',
  vacationsUsed: '',
  remainingVacations: '',
  notes: ''
})

// Form states
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)
const isSubmitting = ref(false)

// Label states for floating labels
const labelStates = ref({
  client: false,
  campaign: false,
  role: false,
  title: false,
  responsibility: false,
  startDate: false,
  endDate: false,
})

// Form validation errors
const errors = ref<Record<string, string>>({})

// Modal title
const modalTitle = computed(() => 
  props.isEdit ? 'Edit Past Work History' : 'Add Past Work History'
)

// Validate required fields
const validateForm = () => {
  errors.value = {}
  
  if (!formData.value.clientId) {
    errors.value.clientId = 'Client is required'
  }
  if (!formData.value.campaign.trim()) {
    errors.value.campaign = 'Campaign is required'
  }
  if (!formData.value.role.trim()) {
    errors.value.role = 'Role is required'
  }
  if (!formData.value.title.trim()) {
    errors.value.title = 'Title is required'
  }
  if (!formData.value.startDate) {
    errors.value.startDate = 'Start date is required'
  }
  
  return Object.keys(errors.value).length === 0
}

// Date picker methods
const toggleStartDatePicker = () => {
  showStartDatePicker.value = !showStartDatePicker.value
}

const toggleEndDatePicker = () => {
  showEndDatePicker.value = !showEndDatePicker.value
}

const updateStartDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  formData.value.startDate = `${year}-${month}-${day}`
  showStartDatePicker.value = false
  if (errors.value.startDate) {
    delete errors.value.startDate
  }
}

const updateEndDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  formData.value.endDate = `${year}-${month}-${day}`
  showEndDatePicker.value = false
}

const clearStartDate = () => {
  formData.value.startDate = ''
}

const clearEndDate = () => {
  formData.value.endDate = ''
}

// Clear specific error
const clearError = (field: string) => {
  if (errors.value[field]) {
    delete errors.value[field]
  }
}

// Label state management
const setLabelActive = (field: keyof typeof labelStates.value, active: boolean) => {
  labelStates.value[field] = active
}

// Click outside handler to close calendars
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement

  // Check if click is outside any calendar component or date input
  const isCalendarClick =
    target.closest('.calendar-component') ||
    target.closest('[data-calendar-trigger]') ||
    target.closest('.floating-label-container')

  if (!isCalendarClick) {
    // Close all calendars
    showStartDatePicker.value = false
    showEndDatePicker.value = false
  }
}

// Reset form
const resetForm = () => {
  formData.value = {
    talentProfileId: 0,
    isFte: false,
    isContract: false,
    isOther: false,
    campaign: '',
    clientManager: '',
    reportingDepartment: '',
    positionType: '',
    role: '',
    title: '',
    clientId: 0,
    location: '',
    startDate: '',
    endDate: '',
    workDays: [],
    shiftStartTime: '',
    shiftEndTime: '',
    timeZone: '',
    baseWage: '',
    currency: '',
    frequency: '',
    paidDaysOff: '',
    vacationsUsed: '',
    remainingVacations: '',
    notes: ''
  }
  errors.value = {}
  showStartDatePicker.value = false
  showEndDatePicker.value = false

  // Reset label states
  Object.keys(labelStates.value).forEach(key => {
    labelStates.value[key as keyof typeof labelStates.value] = false
  })
}

// Close modal
const closeModal = () => {
  resetForm()
  emit('close')
}

// Save form
const saveForm = async () => {
  if (!validateForm()) {
    return
  }
  
  isSubmitting.value = true
  
  try {
    emit('save', { ...formData.value })
    closeModal()
  } catch (error) {
    console.error('Error saving work history:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Watch for modal open/close and populate form data
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.isEdit && props.workHistoryData) {
    formData.value = { ...props.workHistoryData }
  } else if (isOpen && !props.isEdit) {
    resetForm()
  }
})

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.isOpen) {
    closeModal()
  }
}

// Add/remove event listeners
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('click', handleClickOutside)
    document.body.style.overflow = 'hidden'
  } else {
    document.removeEventListener('keydown', handleKeydown)
    document.removeEventListener('click', handleClickOutside)
    document.body.style.overflow = ''
  }
})
</script>

<template>
  <div
    v-if="isOpen"
    class="flex fixed inset-0 z-50 justify-center items-center p-4 bg-black bg-opacity-50"
    @click="closeModal"
  >
    <div
      class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden"
      @click.stop
    >
      <!-- Modal Header -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ modalTitle }}</h3>
        <button
          @click="closeModal"
          class="p-2 text-gray-400 rounded-md transition-colors hover:text-gray-600 hover:bg-gray-100"
        >
          <X :size="20" />
        </button>
      </div>

      <!-- Modal Content -->
      <div class="overflow-y-auto flex-1 p-6" style="max-height: calc(90vh - 140px);">
        <div class="space-y-6">
          <!-- Client and Campaign Row -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Client -->
            <div>
              <div class="floating-label-container">
                <div class="relative">
                  <select
                    v-model="formData.clientId"
                    class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    :class="{ 'border-red-500': errors.clientId }"
                    @focus="setLabelActive('client', true); clearError('clientId');"
                    @blur="setLabelActive('client', false)"
                    @change="clearError('clientId')"
                  >
                    <option
                      v-for="client in clients"
                      :key="client.id"
                      :value="client.id"
                    >
                      {{ client.name }}
                    </option>
                  </select>
                  <label
                    :class="[
                      'floating-label',
                      {
                        active: labelStates.client || formData.clientId,
                      },
                    ]"
                  >
                    Client *
                  </label>
                  <ChevronDown
                    :size="16"
                    class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                  />
                </div>
              </div>
              <div v-if="errors.clientId" class="mt-1 text-sm text-red-600">
                {{ errors.clientId }}
              </div>
            </div>

            <!-- Campaign -->
            <div>
              <div class="floating-label-container">
                <input
                  v-model="formData.campaign"
                  type="text"
                  class="floating-input"
                  :class="{ 'border-red-500': errors.campaign }"
                  @focus="setLabelActive('campaign', true); clearError('campaign');"
                  @blur="setLabelActive('campaign', false)"
                  @input="clearError('campaign')"
                />
                <label
                  :class="[
                    'floating-label',
                    { active: labelStates.campaign || formData.campaign },
                  ]"
                >
                  Campaign *
                </label>
              </div>
              <div v-if="errors.campaign" class="mt-1 text-sm text-red-600">
                {{ errors.campaign }}
              </div>
            </div>
          </div>

          <!-- Role and Title Row -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Role -->
            <div>
              <div class="floating-label-container">
                <input
                  v-model="formData.role"
                  type="text"
                  class="floating-input"
                  :class="{ 'border-red-500': errors.role }"
                  @focus="setLabelActive('role', true); clearError('role');"
                  @blur="setLabelActive('role', false)"
                  @input="clearError('role')"
                />
                <label
                  :class="[
                    'floating-label',
                    { active: labelStates.role || formData.role },
                  ]"
                >
                  Role *
                </label>
              </div>
              <div v-if="errors.role" class="mt-1 text-sm text-red-600">
                {{ errors.role }}
              </div>
            </div>

            <!-- Title -->
            <div>
              <div class="floating-label-container">
                <input
                  v-model="formData.title"
                  type="text"
                  class="floating-input"
                  :class="{ 'border-red-500': errors.title }"
                  @focus="setLabelActive('title', true); clearError('title');"
                  @blur="setLabelActive('title', false)"
                  @input="clearError('title')"
                />
                <label
                  :class="[
                    'floating-label',
                    { active: labelStates.title || formData.title },
                  ]"
                >
                  Title *
                </label>
              </div>
              <div v-if="errors.title" class="mt-1 text-sm text-red-600">
                {{ errors.title }}
              </div>
            </div>
          </div>

          <!-- Responsibility -->
          <div>
            <div class="floating-label-container">
              <textarea
                v-model="formData.notes"
                class="resize-none floating-input"
                rows="3"
                @focus="setLabelActive('responsibility', true)"
                @blur="setLabelActive('responsibility', false)"
              ></textarea>
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.responsibility || formData.notes },
                ]"
              >
                Responsibility
              </label>
            </div>
          </div>

          <!-- Start Date and End Date Row -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Start Date -->
            <div>
              <div class="floating-label-container">
                <div class="relative">
                  <input
                    :value="
                      formData.startDate
                        ? datePickerFormatter(new Date(formData.startDate))
                        : ''
                    "
                    type="text"
                    readonly
                    data-calendar-trigger
                    class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                    :class="{ 'border-red-500': errors.startDate }"
                    @click="toggleStartDatePicker"
                    @focus="setLabelActive('startDate', true); clearError('startDate');"
                    @blur="setLabelActive('startDate', false)"
                  />
                  <label
                    :class="[
                      'floating-label',
                      { active: labelStates.startDate || formData.startDate },
                    ]"
                  >
                    Start Date *
                  </label>
                  <!-- Clear button -->
                  <button
                    v-if="formData.startDate"
                    @click.stop="clearStartDate"
                    class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                  >
                    <X :size="16" />
                  </button>
                  <!-- Calendar icon -->
                  <Calendar
                    :size="16"
                    class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                  />
                  <CalendarComponent
                    :is-open="showStartDatePicker"
                    :selected-date="
                      formData.startDate
                        ? new Date(formData.startDate)
                        : new Date()
                    "
                    @select-date="updateStartDate"
                    @close="showStartDatePicker = false"
                  />
                </div>
              </div>
              <div v-if="errors.startDate" class="mt-1 text-sm text-red-600">
                {{ errors.startDate }}
              </div>
            </div>

            <!-- End Date -->
            <div>
              <div class="floating-label-container">
                <div class="relative">
                  <input
                    :value="
                      formData.endDate
                        ? datePickerFormatter(new Date(formData.endDate))
                        : ''
                    "
                    type="text"
                    readonly
                    data-calendar-trigger
                    class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                    @click="toggleEndDatePicker"
                    @focus="setLabelActive('endDate', true)"
                    @blur="setLabelActive('endDate', false)"
                  />
                  <label
                    :class="[
                      'floating-label',
                      { active: labelStates.endDate || formData.endDate },
                    ]"
                  >
                    End Date
                  </label>
                  <!-- Clear button -->
                  <button
                    v-if="formData.endDate"
                    @click.stop="clearEndDate"
                    class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                  >
                    <X :size="16" />
                  </button>
                  <!-- Calendar icon -->
                  <Calendar
                    :size="16"
                    class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                  />
                  <CalendarComponent
                    :is-open="showEndDatePicker"
                    :selected-date="
                      formData.endDate
                        ? new Date(formData.endDate)
                        : new Date()
                    "
                    @select-date="updateEndDate"
                    @close="showEndDatePicker = false"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end p-6 space-x-3 border-t border-gray-200">
        <button
          @click="closeModal"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Cancel
        </button>
        <button
          @click="saveForm"
          :disabled="isSubmitting"
          class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSubmitting">Saving...</span>
          <span v-else>{{ isEdit ? 'Update' : 'Add' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>
