<script setup lang="ts">
import { onBeforeUnmount, onMounted } from 'vue'
import { FileText, Eye, X, Upload, Loader2 } from 'lucide-vue-next'
import { useDocuments } from '@/composables/talent/useDocuments'
import { useRoute } from 'vue-router'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const route = useRoute()

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions()
const canCreateDocuments = canCreate(ModuleId.TALENT_DOCUMENTS)
const canEditDocuments = canEdit(ModuleId.TALENT_DOCUMENTS)
const isEditMode = !!route.params.id

const {
    fileInput,
    selectedDocumentType,
    otherDocumentType,
    finalDocumentType,
    labelStates,
    previewFile,
    previewUrl,
    previewType,
    collectedDocuments,
    isViewModalOpen,
    isLoadingDocument,
    viewUrl,
    viewType,
    viewDocName,
    viewError,

    setLabelActive,
    triggerFileUpload,
    handleFileUpload,
    confirmAddDocument,
    cancelPreview,
    viewFile,
    closeViewModal,
    getTalentDocuments,
 } = useDocuments()



// Cleanup on unmount
onBeforeUnmount(() => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  collectedDocuments.value.forEach(doc => {
    if (doc.url) {
      URL.revokeObjectURL(doc.url)
    }
  })
})

onMounted(() => {
  if (route.params.id) {
    getTalentDocuments(Number.parseInt(route.params.id.toString()))
  }
})

</script>

<template>
  <div class="px-8 space-y-6">
    <!-- Documents Collected Section -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Documents Collected ({{ collectedDocuments.length }})</h3>
      
      <div class="space-y-3">
        <div 
          v-for="(doc, index) in collectedDocuments" 
          :key="index"
          class="flex justify-between items-center p-4 material-card"
        >
          <div class="flex items-center space-x-3">
            <FileText :size="20" class="text-orange-500" />
            <div>
              <div class="font-medium text-gray-900">{{ doc.name }}</div>
              <div class="text-sm text-gray-500">{{ doc.description }}</div>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <span class="px-3 py-1 text-xs text-gray-500 bg-gray-100 rounded-full">{{ doc.size }}</span>
            <button 
              @click="viewFile(doc)"
              class="px-3 py-1 text-sm font-medium text-blue-600 rounded-lg transition-all duration-200 hover:text-blue-700 hover:bg-blue-50"
            >
              <Eye :size="16" class="inline mr-1" />
              View file
            </button>
            <!-- <button 
              @click="removeDocument(index)"
              class="px-3 py-1 text-sm font-medium text-red-600 rounded-lg transition-all duration-200 hover:text-red-700 hover:bg-red-50"
            >
              <X :size="16" class="inline mr-1" />
              Remove
            </button> -->
          </div>
        </div>
      </div>
      
      <!-- Missing Documents -->
      <div class="p-4 mt-4 bg-gray-50 rounded-xl">
        <div class="text-sm text-gray-600">
          <span class="font-medium">Missing documents:</span> IMMS ID, CURP ID, RFC Document, W-8, Non-disclosure Agreement
        </div>
      </div>
    </div>

    <!-- Upload Documents Section -->
    <div v-if="isSuperuser || (isEditMode ? canEditDocuments : canCreateDocuments)" class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Upload Documents</h3>
      
      <div class="space-y-4">
        <!-- Document Type Selector -->
        <div class="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0">
          <!-- Document Type Dropdown -->
          <div class="max-w-xs floating-label-container">
            <select
              v-model="selectedDocumentType"
              class="floating-select"
              @focus="setLabelActive('documentType', true)"
              @blur="setLabelActive('documentType', false)"
            >
              <option value="Birth Certificate">Birth Certificate</option>
              <option value="Proof of Address">Proof of Address</option>
              <option value="IMMS ID">IMMS ID</option>
              <option value="CURP ID">CURP ID</option>
              <option value="RFC Document">RFC Document</option>
              <option value="W-8">W-8</option>
              <option value="Non-disclosure Agreement">Non-disclosure Agreement</option>
              <option value="Others">Others</option>
            </select>
            <label :class="['floating-label', { 'active text-orange-600': labelStates.documentType || selectedDocumentType }]">
              Type of Document
            </label>
          </div>

          <!-- Custom Document Type Input (shown when "Others" is selected) -->
          <div
            v-if="selectedDocumentType === 'Others'"
            class="max-w-xs floating-label-container"
          >
            <input
              v-model="otherDocumentType"
              type="text"
              class="floating-input"
              @focus="setLabelActive('otherDocumentType', true)"
              @blur="setLabelActive('otherDocumentType', false)"
            />
            <label :class="['floating-label', { 'active text-orange-600': labelStates.otherDocumentType || otherDocumentType }]">
              Please specify
            </label>
          </div>
        </div>

        <!-- File Upload Area -->
        <div v-if="!previewUrl">
          <input
            ref="fileInput"
            type="file"
            @change="handleFileUpload"
            class="hidden"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          />
          <button
            @click="triggerFileUpload"
            :disabled="!finalDocumentType || !!previewUrl"
            class="flex justify-center items-center px-4 py-8 w-full max-w-xs rounded-xl border-2 border-gray-300 border-dashed transition-all duration-200 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <div class="text-center">
              <Upload :size="24" class="mx-auto mb-2 text-gray-400" />
              <span class="text-sm text-gray-600">Upload File</span>
            </div>
          </button>
        </div>

        <!-- Preview Pane -->
        <div v-if="previewUrl" class="p-4 space-y-4 bg-gray-50 rounded-lg border">
          <h4 class="font-medium text-gray-900">
            Preview: <span class="text-gray-600">{{ previewFile?.name }}</span>
          </h4>

          <!-- Image Preview -->
          <div v-if="previewType.startsWith('image/')" class="flex justify-center">
            <img
              :src="previewUrl"
              alt="Image Preview"
              class="object-contain max-w-full max-h-64 rounded border"
            />
          </div>

          <!-- PDF Preview -->
          <div v-else-if="previewType.includes('pdf')" class="h-64">
            <embed
              :src="previewUrl"
              type="application/pdf"
              class="w-full h-full rounded border"
            />
          </div>

          <!-- Other file types -->
          <div v-else class="py-8 text-center text-gray-500">
            <FileText :size="48" class="mx-auto mb-2 text-gray-400" />
            <p class="text-sm">Preview not available for this file type</p>
            <a
              :href="previewUrl"
              target="_blank"
              class="text-sm text-blue-600 hover:underline"
            >Open File</a>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-center pt-5 space-x-2">
            <button
              @click="confirmAddDocument(Number.parseInt(route.params.id.toString()))"
              class="px-6 py-3 font-medium text-white rounded-lg shadow-sm transition-colors bg-primary-600 hover:bg-primary-700 hover:shadow-md"
            >
              Add Document
            </button>
            <button
              @click="cancelPreview"
              class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg transition-colors hover:bg-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
    
  </div>

  <!-- View File Modal -->
  <div
    v-if="isViewModalOpen"
    class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50"
    @click.self="closeViewModal"
  >
    <div class="bg-white rounded-lg overflow-hidden w-11/12 md:w-2/3 lg:w-1/2 max-h-[90vh] flex flex-col">
      <!-- Modal Header -->
      <div class="flex justify-between items-center p-4 border-b">
        <h4 class="text-lg font-medium text-gray-900">{{ viewDocName }}</h4>
        <button 
          @click="closeViewModal" 
          class="text-gray-500 transition-colors hover:text-gray-700"
        >
          <X :size="20" />
        </button>
      </div>

      <!-- Modal Content -->
      <div class="overflow-auto flex-1 p-4">
        <!-- Loading State -->
        <div v-if="isLoadingDocument" class="flex flex-col justify-center items-center py-12">
          <Loader2 :size="32" class="mb-4 text-gray-400 animate-spin" />
          <p class="text-gray-600">Loading document...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="viewError" class="flex flex-col justify-center items-center py-12">
          <X :size="32" class="mb-4 text-red-400" />
          <p class="text-center text-red-600">{{ viewError }}</p>
          <button 
            @click="viewFile(collectedDocuments.find(d => d.name === viewDocName)!)"
            class="px-4 py-2 mt-4 text-white bg-blue-600 rounded-lg transition-colors hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>

        <!-- Document Content -->
        <div v-else-if="viewUrl">
          <!-- Image Display -->
          <div v-if="viewType.startsWith('image/')" class="flex justify-center">
            <img
              :src="viewUrl"
              alt="Document Preview"
              class="max-h-[70vh] object-contain rounded"
            />
          </div>

          <!-- PDF Display -->
          <div v-else-if="viewType.includes('pdf')" class="h-[70vh]">
            <embed
              :src="viewUrl"
              type="application/pdf"
              class="w-full h-full rounded"
            />
          </div>

          <!-- Other file types -->
          <div v-else class="flex flex-col justify-center items-center py-12">
            <FileText :size="48" class="mb-4 text-gray-400" />
            <p class="mb-4 text-center text-gray-600">
              Preview not available for this file type
            </p>
            <a
              :href="viewUrl"
              target="_blank"
              class="px-4 py-2 text-white bg-blue-600 rounded-lg transition-colors hover:bg-blue-700"
            >
              Download File
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
