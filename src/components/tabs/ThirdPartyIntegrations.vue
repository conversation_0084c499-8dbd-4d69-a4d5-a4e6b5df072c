<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { Plus, Power, Trash2 } from 'lucide-vue-next'
import { useThirdPartyIntegrations } from '@/composables/talent/useThirdPartyIntegrations'
import { useBpoRelated } from '@/composables/talent/useBpoRelated'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'
import { useRoute } from 'vue-router'

// Composables
const {
  integrations,
  isLoading,
  error,
  fetchIntegrations,
  createIntegration,
  toggleIntegration,
  deleteIntegration,
  getStatusColor,
  getStatusText
} = useThirdPartyIntegrations()

const { bpoRelatedInfoForm, getTalentProfileBpoRelatedInfo } = useBpoRelated()

const route = useRoute()

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions()
const canCreateIntegrations = canCreate(ModuleId.TALENT_3RD_PARTY_INTEGRATIONS)
const canEditIntegrations = canEdit(ModuleId.TALENT_3RD_PARTY_INTEGRATIONS)
const isEditMode = !!route.params.id

// Check if bpo_mail is not empty
const hasBpoMail = computed(() => {
  return bpoRelatedInfoForm.value.bpoEmail && bpoRelatedInfoForm.value.bpoEmail.trim() !== ''
})

// Load integrations and BPO related info on component mount
onMounted(async () => {
  await fetchIntegrations()
  await getTalentProfileBpoRelatedInfo()
})

// Wrapper function for createIntegration with bpo_mail check
const handleCreateIntegration = (integrationName: string) => {
  if (integrationName === 'Zoho Mail' && !hasBpoMail.value) {
    alert('BPO Mail is required to enable Zoho Mail integration')
    return
  }
  createIntegration(integrationName)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-lg font-semibold text-gray-900">3rd Party Integrations</h2>
        <p class="mt-1 text-sm text-gray-600">Manage external service integrations for this talent</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="w-8 h-8 rounded-full border-b-2 border-orange-600 animate-spin"></div>
      <span class="ml-3 text-gray-600">Loading integrations...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="py-12 text-center">
      <div class="mb-4 text-red-600">
        <svg class="mx-auto w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="mb-2 text-lg font-medium text-gray-900">Error Loading Integrations</h3>
      <p class="mb-4 text-gray-500">{{ error }}</p>
      <button
        @click="fetchIntegrations"
        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md border border-transparent hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
      >
        Try Again
      </button>
    </div>

    <!-- Integrations Table -->
    <div v-else class="overflow-hidden bg-white rounded-lg border border-gray-200">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Service
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Description
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Status
              </th>
              <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="integration in integrations" :key="integration.name" class="hover:bg-gray-50">
              <!-- Service Name -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ integration.name }}
                </div>
              </td>

              <!-- Description -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-600">
                  {{ integration.description }}
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize',
                  getStatusColor(integration)
                ]">
                  {{ integration.status || getStatusText(integration) }}
                </span>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 text-sm font-medium whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <!-- Create/Setup Button - Only show if not created -->
                  <button
                    v-if="integration.id === 0 && (isSuperuser || (isEditMode ? canEditIntegrations : canCreateIntegrations))"
                    @click="handleCreateIntegration(integration.name)"
                    :disabled="isLoading || (integration.name === 'Zoho Mail' && !hasBpoMail)"
                    :class="[
                      'inline-flex items-center p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
                      integration.name === 'Zoho Mail' && !hasBpoMail
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                    ]"
                    :title="integration.name === 'Zoho Mail' && !hasBpoMail ? 'BPO Mail is required to enable Zoho Mail integration' : 'Create/Setup Integration'"
                  >
                    <Plus :size="16" />
                  </button>

                  <!-- Enable/Disable Toggle - Only show if created -->
                  <button
                    v-if="integration.id > 0 && (isSuperuser || canEditIntegrations)"
                    @click="toggleIntegration(integration)"
                    :disabled="isLoading || (integration.name === 'Zoho Mail' && !hasBpoMail)"
                    :class="[
                      'inline-flex items-center p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
                      integration.name === 'Zoho Mail' && !hasBpoMail
                        ? 'text-gray-400 cursor-not-allowed'
                        : integration.isActive
                          ? 'text-orange-600 hover:text-orange-900 hover:bg-orange-50'
                          : 'text-blue-600 hover:text-blue-900 hover:bg-blue-50'
                    ]"
                    :title="integration.name === 'Zoho Mail' && !hasBpoMail ? 'BPO Mail is required to manage Zoho Mail integration' : (integration.isActive ? 'Disable Integration' : 'Enable Integration')"
                  >
                    <Power :size="16" />
                  </button>

                  <!-- Delete Button - Only show if created -->
                  <button
                    v-if="integration.id > 0 && (isSuperuser || canEditIntegrations)"
                    @click="deleteIntegration(integration.name)"
                    :disabled="isLoading || (integration.name === 'Zoho Mail' && !hasBpoMail)"
                    :class="[
                      'inline-flex items-center p-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed',
                      integration.name === 'Zoho Mail' && !hasBpoMail
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-red-600 hover:text-red-900 hover:bg-red-50'
                    ]"
                    :title="integration.name === 'Zoho Mail' && !hasBpoMail ? 'BPO Mail is required to manage Zoho Mail integration' : 'Delete Integration'"
                  >
                    <Trash2 :size="16" />
                  </button>

                  <!-- Placeholder for not-created state -->
                  <div v-if="integration.id === 0" class="flex items-center space-x-3">
                    <!-- Disabled toggle button as visual indicator -->
                    <button
                      disabled
                      class="inline-flex items-center p-2 text-gray-400 rounded-md cursor-not-allowed"
                      title="Create integration first to enable/disable"
                    >
                      <Power :size="16" />
                    </button>
                    <!-- Disabled delete button as visual indicator -->
                    <button
                      disabled
                      class="inline-flex items-center p-2 text-gray-400 rounded-md cursor-not-allowed"
                      title="Create integration first to delete"
                    >
                      <Trash2 :size="16" />
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State (if no integrations) -->
    <div v-if="integrations.length === 0" class="py-12 text-center">
      <div class="mb-4 text-gray-400">
        <Plus :size="48" class="mx-auto" />
      </div>
      <h3 class="mb-2 text-lg font-medium text-gray-900">No Integrations</h3>
      <p class="text-gray-500">No third-party integrations have been configured for this talent.</p>
    </div>
  </div>
</template>
