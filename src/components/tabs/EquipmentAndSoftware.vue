<script setup lang="ts">
import {
  Plus,
  ChevronDown,
  Edit,
  FileText,
  ExternalLink,
} from "lucide-vue-next";
import { useBpoRelated } from "@/composables/talent/useBpoRelated";
import { useITDocuments } from "@/composables/talent/useITDocuments";
import FileViewerModal from "@/components/modals/FileViewerModal.vue";
import { onMounted, ref, watch } from "vue";
import type { Equipment } from "@/types/equipment";
import { useRoute } from "vue-router";
import { usePermissions } from "@/composables/permissions/usePermissions";
import { ModuleId } from "@/types/permissions";

const {
  // State
  labelStates,

  // Equipment and Software
  showAddEquipmentRow,
  showAddSoftwareRow,
  equipment,
  softwareList,
  newSoftware,
  editingSoftwareIndex,
  editingSoftware,

  // Methods
  setLabelActive,

  // Equipment and Software methods
  addEquipmentRow,
  addSoftwareRow,
  submitEquipment,
  submitSoftware,
  updateEquipment,
  updateSoftware,
  getTalentEquipment,
  getTalentSoftware,
  getAvailableEquipment,
} = useBpoRelated();

// IT Documents composable
const {
  // State
  isLoading: isLoadingDocuments,
  error: documentsError,
  itDocuments,

  // Form state
  selectedDocumentType,
  otherDocumentType,
  selectedFile,
  documentNotes,
  showAddDocumentForm,
  labelStates: documentLabelStates,

  // Computed
  finalDocumentType,

  // Methods
  setLabelActive: setDocumentLabelActive,
  loadITDocuments,
  createDocument,
  handleFileSelect,
  showAddForm,
  cancelAdd,
} = useITDocuments();

const route = useRoute();

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions();

// Check permissions for talent equipment & software module
const canCreateEquipmentSoftware = canCreate(ModuleId.TALENT_EQUIPMENT_SOFTWARE);
const canEditEquipmentSoftware = canEdit(ModuleId.TALENT_EQUIPMENT_SOFTWARE);

// File viewer modal state
const isFileViewerOpen = ref(false);
const selectedFileUrl = ref("");
const selectedFileName = ref("");
const selectedFileSize = ref("");

// Equipment dropdown state
const availableEquipment = ref<Equipment[]>([]);
const selectedEquipmentId = ref<number | null>(null);
const selectedEquipmentData = ref<Equipment | null>(null);

// Load available equipment from API
const loadAvailableEquipment = async () => {
  try {
    const response = await getAvailableEquipment();
    if (response) {
      availableEquipment.value = response;
    }
  } catch (error) {
    console.error("Error loading available equipment:", error);
  }
};

// Watch for equipment selection changes
watch(selectedEquipmentId, async (newId) => {
  if (newId) {
    const selected = availableEquipment.value.find((eq) => eq.id === newId);
    selectedEquipmentData.value = selected || null;
  } else {
    selectedEquipmentData.value = null;
  }
});

// Reset equipment selection and hide the form
const resetEquipmentSelection = () => {
  selectedEquipmentId.value = null;
  selectedEquipmentData.value = null;
  showAddEquipmentRow.value = false;
};

// Submit equipment assignment
const submitEquipmentAssignment = async () => {
  if (selectedEquipmentData.value) {
    // Add the selected equipment to the talent's equipment list
    await submitEquipment(
      selectedEquipmentData.value.id!,
      parseInt(route.params.id as string),
      true
    );

    // Reset selection and hide form
    resetEquipmentSelection();
    await loadAvailableEquipment();
  }
};

onMounted(async () => {
  await loadAvailableEquipment();

  // Load IT documents if we have a talent ID
  if (route.params.id) {
    await loadITDocuments(Number(route.params.id));
    await getTalentEquipment();
    await getTalentSoftware();
  }
});

// Helper function to get file size from base64 or regular URL
const getFileSize = (fileUrl: string) => {
  if (!fileUrl) return "N/A";

  // Check if it's a base64 encoded string
  if (fileUrl.startsWith("data:")) {
    try {
      // Extract the base64 part (after the comma)
      const base64Data = fileUrl.split(",")[1];
      if (base64Data) {
        // Calculate the original file size from base64
        // Base64 encoding increases size by ~33%, so we need to account for padding
        const base64Length = base64Data.length;
        const padding = (base64Data.match(/=/g) || []).length;
        const originalSize = (base64Length * 3) / 4 - padding;

        return formatFileSize(originalSize);
      }
    } catch (error) {
      console.error("Error calculating base64 file size:", error);
      return "N/A";
    }
  }

  // For regular URLs, we can't determine size without making a request
  // Return a placeholder or make a HEAD request if needed
  return "Unknown";
};

// Helper function to format file size in human readable format
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// File viewer functions
const openFileViewer = (document: any) => {
  selectedFileUrl.value = document.url || "";
  selectedFileName.value = document.documentType || "Document";
  selectedFileSize.value = getFileSize(document.url || "");
  isFileViewerOpen.value = true;
};

const closeFileViewer = () => {
  isFileViewerOpen.value = false;
  selectedFileUrl.value = "";
  selectedFileName.value = "";
  selectedFileSize.value = "";
};

// Software edit functions
const startEditSoftware = (index: number) => {
  editingSoftwareIndex.value = index;
  editingSoftware.value = { ...softwareList.value[index] };
};

const cancelEditSoftware = () => {
  editingSoftwareIndex.value = null;
  editingSoftware.value = {};
};

// Equipment status toggle function
const toggleEquipmentStatus = async (index: number) => {
  try {
    await updateEquipment(index);
  } catch (error) {
    alert("Failed to update equipment status. Please try again.");
  }
};
</script>

<template>
  <div>
    <!-- Equipment Section -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Equipment</h3>

      <!-- Equipment Table -->
      <div class="mb-4 material-card">
        <!-- Table Header -->
        <div
          class="grid grid-cols-[1.2fr_1fr_1fr_1fr_1fr_40px] gap-4 p-4 border-b items-center border-gray-200 text-xs font-medium text-gray-500 uppercase tracking-wider"
        >
          <div>NAME</div>
          <div>SERIAL NUMBER</div>
          <div>MODEL</div>
          <div>CATEGORY</div>
          <div>IS ACTIVE</div>
          <div></div>
        </div>

        <!-- Table Rows -->
        <div class="divide-y divide-gray-100">
          <div
            v-for="(item, index) in equipment"
            :key="index"
            class="grid grid-cols-[1.2fr_1fr_1fr_1fr_1fr_40px] gap-4 p-4 items-center hover:bg-gray-50 transition-colors"
          >
            <!-- View Mode -->
            <div class="text-sm text-gray-900">{{ item.name }}</div>
            <div class="text-sm text-gray-600">
              {{ item.serialNumber || "N/A" }}
            </div>
            <div class="text-sm text-gray-600">{{ item.model || "N/A" }}</div>
            <div class="text-sm text-gray-600">
              {{ (item as any).category || "N/A" }}
            </div>
            <div class="flex items-center">
              <!-- Orange Toggle Switch (Interactive) for Equipment -->
              <label
                v-if="isSuperuser || canEditEquipmentSoftware"
                class="inline-flex relative items-center cursor-pointer"
              >
                <input
                  type="checkbox"
                  :checked="item.isActive"
                  :disabled="!item.isActive"
                  @change="toggleEquipmentStatus(index)"
                  class="sr-only peer"
                />
                <div
                  class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"
                  :class="{
                    'opacity-50': !item.isActive,
                    'cursor-not-allowed': !item.isActive,
                  }"
                />
              </label>
              <!-- Read-only status display for users without edit permission -->
              <div
                v-else
                class="flex items-center"
              >
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ item.isActive ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
            <div></div>
          </div>
        </div>

        <!-- Equipment Selection Row -->
        <div
          v-if="showAddEquipmentRow"
          class="p-4 bg-gray-50 border-t border-gray-200"
        >
          <!-- Equipment Selection Dropdown -->
          <div class="mb-4">
            <label class="block mb-2 text-sm font-medium text-gray-700">
              Select Equipment to Assign
            </label>
            <div class="relative">
              <select
                v-model="selectedEquipmentId"
                class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Choose an equipment...</option>
                <option
                  v-for="equipment in availableEquipment"
                  :key="equipment.id"
                  :value="equipment.id"
                >
                  {{ equipment.name }} ({{ equipment.equipmentId }})
                </option>
              </select>
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />
            </div>
          </div>

          <!-- Equipment Details (Read-only) -->
          <div
            v-if="selectedEquipmentData"
            class="grid grid-cols-1 gap-4 mb-4 md:grid-cols-2 lg:grid-cols-4"
          >
            <!-- Name -->
            <div class="floating-label-container">
              <input
                :value="selectedEquipmentData.name"
                type="text"
                class="bg-gray-100 floating-input"
                readonly
              />
              <label class="floating-label active">Name</label>
            </div>

            <!-- Serial Number -->
            <div class="floating-label-container">
              <input
                :value="selectedEquipmentData.serialNumber || 'N/A'"
                type="text"
                class="bg-gray-100 floating-input"
                readonly
              />
              <label class="floating-label active">Serial Number</label>
            </div>

            <!-- Model -->
            <div class="floating-label-container">
              <input
                :value="selectedEquipmentData.model || 'N/A'"
                type="text"
                class="bg-gray-100 floating-input"
                readonly
              />
              <label class="floating-label active">Model</label>
            </div>

            <!-- Category -->
            <div class="floating-label-container">
              <input
                :value="selectedEquipmentData.category"
                type="text"
                class="bg-gray-100 floating-input"
                readonly
              />
              <label class="floating-label active">Category</label>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3">
            <button
              @click="resetEquipmentSelection"
              class="px-4 py-2 text-sm font-medium text-gray-600 bg-white rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              v-if="selectedEquipmentData"
              @click="submitEquipmentAssignment"
              class="px-4 py-2 text-sm font-medium text-white bg-orange-500 rounded-lg transition-colors hover:bg-orange-600"
            >
              + Assign Equipment
            </button>
          </div>
        </div>
      </div>

      <!-- Add Equipment Button -->
      <div v-if="isSuperuser || canCreateEquipmentSoftware" class="flex justify-end">
        <button
          @click="addEquipmentRow"
          class="flex items-center mt-4 text-gray-700 border border-gray-300 material-button hover:bg-gray-50"
        >
          <Plus :size="16" class="mr-2" />
          Add Equipment
        </button>
      </div>
    </div>

    <!-- Software Section -->
    <div class="mt-5 material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Software</h3>

      <!-- Software Table -->
      <div class="mb-4 material-card">
        <!-- Table Header -->
        <div
          class="grid grid-cols-[1.5fr_1.5fr_1fr_1.2fr_40px] gap-4 p-4 border-b border-gray-200 text-xs font-medium text-gray-500 uppercase tracking-wider"
        >
          <div>SOFTWARE NAME</div>
          <div>SERIAL NUMBER</div>
          <div>VERSION</div>
          <div>STATUS</div>
          <div>EDIT</div>
        </div>

        <!-- Table Rows -->
        <div class="divide-y divide-gray-100">
          <div
            v-for="(software, index) in softwareList"
            :key="index"
            class="grid grid-cols-[1.5fr_1.5fr_1fr_1.2fr_40px] gap-4 p-4 items-center hover:bg-gray-50 transition-colors"
          >
            <!-- Edit Mode -->
            <template v-if="editingSoftwareIndex === index">
              <div class="floating-label-container">
                <input
                  v-model="editingSoftware.software"
                  type="text"
                  class="floating-input"
                />
                <label class="floating-label active">Software Name</label>
              </div>
              <div class="floating-label-container">
                <input
                  v-model="editingSoftware.softwareKey"
                  type="text"
                  class="floating-input"
                />
                <label class="floating-label active">Serial Number</label>
              </div>
              <div class="floating-label-container">
                <input
                  v-model="editingSoftware.softwareVersion"
                  type="text"
                  class="floating-input"
                />
                <label class="floating-label active">Version</label>
              </div>
              <div class="flex items-center">
                <!-- Orange Toggle Switch for Edit Mode -->
                <label class="inline-flex relative items-center cursor-pointer">
                  <input
                    type="checkbox"
                    v-model="editingSoftware.status"
                    :true-value="true"
                    :false-value="false"
                    class="sr-only peer"
                  />
                  <div
                    class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"
                  ></div>
                </label>
              </div>
              <div class="flex justify-end space-x-1">
                <button
                  @click="updateSoftware(index)"
                  class="px-2 py-1 text-xs font-medium text-green-600 rounded transition-colors hover:text-green-700 hover:bg-green-50"
                >
                  Save
                </button>
                <button
                  @click="cancelEditSoftware"
                  class="px-2 py-1 text-xs font-medium text-gray-600 rounded transition-colors hover:text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </template>

            <!-- View Mode -->
            <template v-else>
              <div class="text-sm text-gray-900">{{ software.software }}</div>
              <div class="text-sm text-gray-600">
                {{ software.softwareKey }}
              </div>
              <div class="text-sm text-gray-600">
                {{ software.softwareVersion || "N/A" }}
              </div>
              <div class="flex items-center">
                <!-- Orange Toggle Switch (Read-only) -->
                <label class="inline-flex relative items-center">
                  <input
                    type="checkbox"
                    :checked="software.status"
                    disabled
                    class="sr-only peer"
                  />
                  <div
                    class="w-11 h-6 bg-gray-200 rounded-full opacity-75 peer peer-checked:bg-orange-500"
                  ></div>
                  <div
                    :class="[
                      'absolute top-[2px] left-[2px] bg-white border-gray-300 border rounded-full h-5 w-5 transition-all',
                      software.status ? 'translate-x-full' : '',
                    ]"
                  ></div>
                </label>
              </div>
              <div class="flex justify-end">
                <button
                  v-if="isSuperuser || canEditEquipmentSoftware"
                  @click="startEditSoftware(index)"
                  class="p-2 text-orange-600 rounded-lg transition-colors hover:text-orange-700 hover:bg-orange-50"
                >
                  <Edit :size="16" />
                </button>
              </div>
            </template>
          </div>
        </div>

        <!-- Add Software Row -->
        <div
          v-if="showAddSoftwareRow"
          class="grid grid-cols-5 gap-4 p-4 border-t border-gray-200"
        >
          <div class="floating-label-container">
            <input
              v-model="newSoftware.software"
              type="text"
              class="floating-input"
              @focus="setLabelActive('softwareName', true)"
              @blur="setLabelActive('softwareName', false)"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.softwareName || newSoftware.software },
              ]"
            >
              Software Name
            </label>
          </div>
          <div class="floating-label-container">
            <input
              v-model="newSoftware.softwareKey"
              type="text"
              class="floating-input"
              @focus="setLabelActive('softwareSerial', true)"
              @blur="setLabelActive('softwareSerial', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active: labelStates.softwareSerial || newSoftware.softwareKey,
                },
              ]"
            >
              Serial Number
            </label>
          </div>
          <div class="floating-label-container">
            <input
              v-model="newSoftware.softwareVersion"
              type="text"
              class="floating-input"
              @focus="setLabelActive('softwareVersion', true)"
              @blur="setLabelActive('softwareVersion', false)"
            />
            <label
              :class="[
                'floating-label',
                { active: (labelStates as any).softwareVersion || newSoftware.softwareVersion },
              ]"
            >
              Version
            </label>
          </div>
          <div class="flex items-center">
            <!-- Orange Toggle Switch for Add Row -->
            <label class="inline-flex relative items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="newSoftware.status"
                :true-value="true"
                :false-value="false"
                class="sr-only peer"
              />
              <div
                class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"
              ></div>
            </label>
          </div>
          <button
            @click="submitSoftware"
            class="px-3 py-1 text-sm font-medium text-green-600 rounded-lg transition-all duration-200 hover:text-green-700 hover:bg-green-50"
          >
            + Submit
          </button>
        </div>
      </div>

      <!-- Add Software Button -->
      <div v-if="isSuperuser || canCreateEquipmentSoftware" class="flex justify-end">
        <button
          @click="addSoftwareRow"
          class="flex items-center mt-4 text-gray-700 border border-gray-300 material-button hover:bg-gray-50"
        >
          <Plus :size="16" class="mr-2" />
          Add Software
        </button>
      </div>
    </div>

    <!-- IT Documents Section -->
    <div class="mt-5 material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">IT Documents</h3>

      <!-- Documents List -->
      <div class="mb-4">
        <!-- Loading State -->
        <div
          v-if="isLoadingDocuments"
          class="flex justify-center items-center p-8"
        >
          <div
            class="w-6 h-6 rounded-full border-b-2 border-orange-500 animate-spin"
          ></div>
        </div>

        <!-- Error State -->
        <div v-else-if="documentsError" class="p-4 text-center text-red-600">
          {{ documentsError }}
        </div>

        <!-- Empty State -->
        <div
          v-else-if="itDocuments.length === 0"
          class="p-8 text-center text-gray-500"
        >
          <FileText :size="48" class="mx-auto mb-4 text-gray-300" />
          <p class="text-sm">No IT documents found</p>
        </div>

        <!-- Documents List -->
        <div v-else class="space-y-2">
          <div
            v-for="document in itDocuments"
            :key="document.id"
            class="flex justify-between items-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300"
          >
            <!-- Left side: Icon and Document Info -->
            <div class="flex flex-1 items-center space-x-3 min-w-0">
              <!-- Document Icon -->
              <div
                class="flex flex-shrink-0 justify-center items-center w-8 h-8 bg-orange-50 rounded-lg"
              >
                <FileText :size="18" class="text-orange-500" />
              </div>

              <!-- Document Details -->
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 truncate">
                  {{ document.documentType }}
                </div>
                <div
                  v-if="document.notes"
                  class="mt-0.5 text-xs text-gray-500 truncate"
                >
                  {{ document.notes }}
                </div>
              </div>
            </div>

            <!-- Right side: File Size and View Button -->
            <div class="flex flex-shrink-0 items-center space-x-3">
              <!-- File Size -->
              <div class="hidden text-xs font-medium text-gray-400 sm:block">
                {{ getFileSize(document.url || "") }}
              </div>

              <!-- View File Button -->
              <button
                @click="openFileViewer(document)"
                class="flex items-center px-3 py-1.5 space-x-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-lg transition-all duration-200 hover:bg-blue-100 hover:text-blue-700"
              >
                <ExternalLink :size="12" />
                <span>View file</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Add Document Form -->
        <div
          v-if="showAddDocumentForm"
          class="p-4 bg-gray-50 border-t border-gray-200"
        >
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Document Type -->
            <div class="floating-label-container">
              <select
                v-model="selectedDocumentType"
                class="floating-select"
                @focus="setDocumentLabelActive('documentType', true)"
                @blur="setDocumentLabelActive('documentType', false)"
              >
                <option value="Dos and Don'ts">Dos and Don'ts</option>
                <option value="Software License">Software License</option>
                <option value="Setup Guide">Setup Guide</option>
                <option value="Configuration File">Configuration File</option>
                <option value="User Manual">User Manual</option>
                <option value="Technical Specification">
                  Technical Specification
                </option>
                <option value="Others">Others</option>
              </select>
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      documentLabelStates.documentType || selectedDocumentType,
                  },
                ]"
              >
                Document Type
              </label>
            </div>

            <!-- Custom Document Type (shown when "Others" is selected) -->
            <div
              v-if="selectedDocumentType === 'Others'"
              class="floating-label-container"
            >
              <input
                v-model="otherDocumentType"
                type="text"
                class="floating-input"
                @focus="setDocumentLabelActive('otherDocumentType', true)"
                @blur="setDocumentLabelActive('otherDocumentType', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      documentLabelStates.otherDocumentType ||
                      otherDocumentType,
                  },
                ]"
              >
                Please specify
              </label>
            </div>

            <!-- File Upload -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Upload Document
              </label>
              <div class="flex items-center space-x-3">
                <input
                  type="file"
                  @change="handleFileSelect"
                  class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100"
                  accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                />
              </div>
              <p class="text-xs text-gray-500">
                Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG (Max 10MB)
              </p>
            </div>

            <!-- Notes -->
            <div class="floating-label-container">
              <input
                v-model="documentNotes"
                type="text"
                class="floating-input"
                @focus="setDocumentLabelActive('documentNotes', true)"
                @blur="setDocumentLabelActive('documentNotes', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      documentLabelStates.documentNotes || documentNotes,
                  },
                ]"
              >
                Notes (Optional)
              </label>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end mt-4 space-x-3">
            <button
              @click="cancelAdd"
              class="px-4 py-2 text-sm font-medium text-gray-600 bg-white rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              @click="createDocument(Number(route.params.id))"
              :disabled="
                !finalDocumentType || !selectedFile || isLoadingDocuments
              "
              class="px-4 py-2 text-sm font-medium text-white bg-orange-500 rounded-lg transition-colors hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoadingDocuments">Uploading...</span>
              <span v-else>+ Upload Document</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Add Document Button -->
      <div v-if="isSuperuser || canCreateEquipmentSoftware" class="flex justify-end">
        <button
          @click="showAddForm"
          :disabled="showAddDocumentForm"
          class="flex items-center mt-4 text-gray-700 border border-gray-300 material-button hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Plus :size="16" class="mr-2" />
          Add IT Document
        </button>
      </div>
    </div>

    <!-- File Viewer Modal -->
    <FileViewerModal
      :is-open="isFileViewerOpen"
      :file-url="selectedFileUrl"
      :file-name="selectedFileName"
      :file-size="selectedFileSize"
      @close="closeFileViewer"
    />
  </div>
</template>
