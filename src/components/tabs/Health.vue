<script setup lang="ts">
import { useHealth } from "@/composables/talent/useHealth";
import { onMounted, ref } from "vue";
import { usePermissions } from "@/composables/permissions/usePermissions";
import { ModuleId } from "@/types/permissions";
import { useRoute } from "vue-router";

const {
  labelStates,
  form,
  setLabelActive,
  getAllergyInfo,
  getChronicConditions,
  getPastHealthIssues,
  getOngoingHealthIssues,
  submitHealthInfo,
} = useHealth();
const isLoading = ref(false);

const route = useRoute();

// Permission checks
const { canCreate, canEdit, isSuperuser } = usePermissions();
const canCreateHealth = canCreate(ModuleId.TALENT_HEALTH);
const canEditHealth = canEdit(ModuleId.TALENT_HEALTH);
const isEditMode = !!route.params.id;

const handleSubmit = async () => {
  isLoading.value = true;
  try {
    await submitHealthInfo();
  } finally {
    isLoading.value = false;
  }
};

onMounted(async () => {
  await getAllergyInfo();
  await getChronicConditions();
  await getPastHealthIssues();
  await getOngoingHealthIssues();
});
</script>

<template>
  <div class="px-8 space-y-6">
    <!-- Health Info Form -->
    <div class="material-section">
      <h3 class="mb-6 text-lg font-medium text-gray-900">Health Information</h3>
      <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
        <!-- Question 1 -->
        <div class="space-y-3">
          <div>
            <h3 class="mb-3 text-sm font-medium text-gray-900">
              1. Talent with an ongoing health related issue?
            </h3>
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  v-model="form.ongoingHealthIssue"
                  type="radio"
                  value="No"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">No</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="form.ongoingHealthIssue"
                  type="radio"
                  value="Yes"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">Yes</span>
              </label>
            </div>
          </div>

          <!-- Conditional text area for Question 1 -->
          <div v-if="form.ongoingHealthIssue === 'Yes'" class="mt-3">
            <div class="floating-label-container">
              <textarea
                v-model="form.ongoingHealthIssueDetails"
                rows="4"
                class="px-4 py-3 w-full text-sm bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @focus="setLabelActive('ongoingHealthIssueDetails', true)"
                @blur="setLabelActive('ongoingHealthIssueDetails', false)"
              ></textarea>
              <label
                :class="[
                  'floating-label text-xs',
                  {
                    'active text-orange-600':
                      labelStates.ongoingHealthIssueDetails ||
                      form.ongoingHealthIssueDetails,
                  },
                ]"
              >
                Health related issues
              </label>
            </div>
          </div>
        </div>

        <!-- Question 2 -->
        <div class="space-y-3">
          <div>
            <h3 class="mb-3 text-sm font-medium text-gray-900">
              2. Talent with any chronic condition?
            </h3>
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  v-model="form.chronicCondition"
                  type="radio"
                  value="No"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">No</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="form.chronicCondition"
                  type="radio"
                  value="Yes"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">Yes</span>
              </label>
            </div>
          </div>

          <!-- Conditional text area for Question 2 -->
          <div v-if="form.chronicCondition === 'Yes'" class="mt-3">
            <div class="floating-label-container">
              <textarea
                v-model="form.chronicConditionDetails"
                rows="4"
                class="px-4 py-3 w-full text-sm bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @focus="setLabelActive('chronicConditionDetails', true)"
                @blur="setLabelActive('chronicConditionDetails', false)"
              ></textarea>
              <label
                :class="[
                  'floating-label text-xs',
                  {
                    'active text-orange-600':
                      labelStates.chronicConditionDetails ||
                      form.chronicConditionDetails,
                  },
                ]"
              >
                Chronic conditions
              </label>
            </div>
          </div>
        </div>

        <!-- Question 3 -->
        <div class="space-y-3">
          <div>
            <h3 class="mb-3 text-sm font-medium text-gray-900">
              3. Talent with past health issues?
            </h3>
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  v-model="form.pastHealthIssues"
                  type="radio"
                  value="No"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">No</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="form.pastHealthIssues"
                  type="radio"
                  value="Yes"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">Yes</span>
              </label>
            </div>
          </div>

          <!-- Conditional text area for Question 3 -->
          <div v-if="form.pastHealthIssues === 'Yes'" class="mt-3">
            <div class="floating-label-container">
              <textarea
                v-model="form.pastHealthIssuesDetails"
                rows="4"
                class="px-4 py-3 w-full text-sm bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @focus="setLabelActive('pastHealthIssuesDetails', true)"
                @blur="setLabelActive('pastHealthIssuesDetails', false)"
              ></textarea>
              <label
                :class="[
                  'floating-label text-xs',
                  {
                    'active text-orange-600':
                      labelStates.pastHealthIssuesDetails ||
                      form.pastHealthIssuesDetails,
                  },
                ]"
              >
                Past health issues
              </label>
            </div>
          </div>
        </div>

        <!-- Question 4 -->
        <div class="space-y-3">
          <div>
            <h3 class="mb-3 text-sm font-medium text-gray-900">
              4. Talent with allergies?
            </h3>
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  v-model="form.allergies"
                  type="radio"
                  value="No"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">No</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="form.allergies"
                  type="radio"
                  value="Yes"
                  class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700">Yes</span>
              </label>
            </div>
          </div>

          <!-- Conditional text area for Question 4 -->
          <div v-if="form.allergies === 'Yes'" class="mt-3">
            <div class="floating-label-container">
              <textarea
                v-model="form.allergiesDetails"
                rows="4"
                class="px-4 py-3 w-full text-sm bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @focus="setLabelActive('allergiesDetails', true)"
                @blur="setLabelActive('allergiesDetails', false)"
              ></textarea>
              <label
                :class="[
                  'floating-label text-xs',
                  {
                    'active text-orange-600':
                      labelStates.allergiesDetails || form.allergiesDetails,
                  },
                ]"
              >
                Allergies
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Question 5 - Full Width -->
    <div class="material-section">
      <div>
        <h3 class="mb-3 text-sm font-medium text-gray-900">
          5. Talent taking any medication on a regular basis?
        </h3>
        <div class="flex items-center space-x-6">
          <label class="flex items-center">
            <input
              v-model="form.regularMedication"
              type="radio"
              value="No"
              class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span class="ml-2 text-sm text-gray-700">No</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="form.regularMedication"
              type="radio"
              value="Yes"
              class="w-4 h-4 border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span class="ml-2 text-sm text-gray-700">Yes</span>
          </label>
        </div>
      </div>

      <!-- Conditional text area for Question 5 -->
      <div v-if="form.regularMedication === 'Yes'" class="mt-4">
        <div class="max-w-md floating-label-container">
          <textarea
            v-model="form.regularMedicationDetails"
            rows="4"
            class="px-4 py-3 w-full text-sm bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
            @focus="setLabelActive('regularMedicationDetails', true)"
            @blur="setLabelActive('regularMedicationDetails', false)"
          ></textarea>
          <label
            :class="[
              'floating-label text-xs',
              {
                'active text-orange-600':
                  labelStates.regularMedicationDetails ||
                  form.regularMedicationDetails,
              },
            ]"
          >
            Medication details
          </label>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div v-if="isSuperuser || (isEditMode ? canEditHealth : canCreateHealth)" class="flex justify-end pt-6">
      <button
        @click="handleSubmit"
        :disabled="isLoading"
        class="px-6 py-3 font-medium text-white rounded-lg shadow-sm transition-colors bg-primary-600 hover:bg-primary-700 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isLoading" class="flex items-center">
          <svg
            class="mr-3 -ml-1 w-5 h-5 text-white animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Submitting...
        </span>
        <span v-else>SUBMIT</span>
      </button>
    </div>
  </div>
</template>
