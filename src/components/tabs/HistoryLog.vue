<script setup lang="ts">
import { Calendar, ChevronLeft, ChevronRight, Search, Filter } from 'lucide-vue-next'
import CalendarComponent from '@/components/Calendar.vue'
import { useHistoryLog } from '@/composables/history-log/useHistoryLog'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// Use the history log composable
const {
  // State
  isLoading,
  error,
  filteredLogs,

  // Filter state
  searchQuery,
  startDate,
  endDate,
  selectedEventType,
  selectedStatus,
  selectedUser,

  // UI state
  showStartDatePicker,
  showEndDatePicker,

  // Computed
  eventTypes,
  users,

  // Pagination
  currentPage,
  totalPages,
  paginatedItems,
  nextPage,
  previousPage,

  // Methods
  formatDate,
  toggleStartDatePicker,
  toggleEndDatePicker,
  updateStartDate,
  updateEndDate,
  clearFilters,
  fetchHistoryLogsByTalentId
} = useHistoryLog()

onMounted(async () => {
  await fetchHistoryLogsByTalentId(Number.parseInt(route.params.id.toString()))
})

</script>


<template>
  <div class="flex flex-col h-full bg-white">
    <!-- Search and Filters -->
    <div class="flex-shrink-0 p-2 mb-3 border-b border-gray-200">
      <div class="flex gap-2 items-center">
        <!-- Search Input -->
        <div class="relative flex-1 min-w-0" style="flex: 0 0 30%;">
          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
            <Search class="w-4 h-4 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by user, event, or description..."
            class="block py-2 pr-3 pl-10 w-full text-sm placeholder-gray-500 bg-white rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>

        <!-- Event Type Filter -->
        <select
          v-model="selectedEventType"
          class="flex-1 px-3 py-2 text-sm rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="all">All Events</option>
          <option v-for="eventType in eventTypes" :key="eventType" :value="eventType">
            {{ eventType }}
          </option>
        </select>

        <!-- Status Filter -->
        <select
          v-model="selectedStatus"
          class="flex-1 px-3 py-2 text-sm rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="all">All Status</option>
          <option value="Succeeded">Succeeded</option>
          <option value="Error">Error</option>
        </select>

        <!-- User Filter -->
        <select
          v-model="selectedUser"
          class="flex-1 px-3 py-2 text-sm rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="all">All Users</option>
          <option v-for="user in users" :key="user" :value="user">
            {{ user }}
          </option>
        </select>

        <!-- Date Pickers -->
        <div class="relative flex-shrink-0 calendar-container">
          <button
            @click="toggleStartDatePicker"
            class="flex items-center px-3 py-2 text-sm whitespace-nowrap bg-white rounded border border-gray-300 hover:bg-gray-50"
          >
            <Calendar :size="16" class="mr-2 text-orange-500" />
            <span>{{ formatDate(startDate) }}</span>
          </button>
          <CalendarComponent
            :is-open="showStartDatePicker"
            :selected-date="startDate"
            @select-date="updateStartDate"
            @close="showStartDatePicker = false"
          />
        </div>
        <div class="relative flex-shrink-0 calendar-container">
          <button
            @click="toggleEndDatePicker"
            class="flex items-center px-3 py-2 text-sm whitespace-nowrap bg-white rounded border border-gray-300 hover:bg-gray-50"
          >
            <Calendar :size="16" class="mr-2 text-orange-500" />
            <span>{{ formatDate(endDate) }}</span>
          </button>
          <CalendarComponent
            :is-open="showEndDatePicker"
            :selected-date="endDate"
            :position="'right'"
            @select-date="updateEndDate"
            @close="showEndDatePicker = false"
          />
        </div>

        <!-- Clear Filters Button -->
        <button
          v-if="searchQuery || selectedEventType !== 'all' || selectedStatus !== 'all' || selectedUser !== 'all'"
          @click="clearFilters"
          class="flex-shrink-0 px-3 py-2 text-sm font-medium text-gray-600 whitespace-nowrap rounded hover:text-gray-800 hover:bg-gray-100"
        >
          Clear Filters
        </button>
      </div>
    </div>

    <!-- History Log Content -->
    <div class="flex flex-col flex-1 min-h-0">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex flex-1 justify-center items-center">
        <div class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex flex-col flex-1 justify-center items-center">
        <div class="mb-2 text-red-500">{{ error }}</div>
        <button
          @click="$emit('retry')"
          class="px-4 py-2 text-white bg-orange-500 rounded-md hover:bg-orange-600"
        >
          Retry
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredLogs.length === 0" class="flex flex-col flex-1 justify-center items-center">
        <div class="mb-4 text-gray-400">
          <Filter class="mx-auto w-12 h-12" />
        </div>
        <h3 class="mb-2 text-lg font-medium text-gray-900">No history logs found</h3>
        <p class="mb-4 text-center text-gray-500">
          {{ searchQuery ? 'Try adjusting your search or filters.' : 'No history logs are available.' }}
        </p>
        <button
          v-if="searchQuery"
          @click="clearFilters"
          class="px-4 py-2 text-orange-700 bg-orange-100 rounded-md hover:bg-orange-200"
        >
          Clear filters
        </button>
      </div>

      <!-- Table Content -->
      <div v-else class="overflow-hidden flex-1">
        <!-- Table Header -->
        <div class="hidden grid-cols-12 gap-2 px-4 py-3 text-xs font-medium tracking-wider text-gray-500 uppercase bg-gray-50 border-b border-gray-200 md:grid">
          <div class="col-span-2">DATE & TIME</div>
          <div class="col-span-1">EVENT</div>
          <div class="col-span-2">USER</div>
          <div class="col-span-6">DESCRIPTION</div>
          <div class="col-span-1">STATUS</div>
        </div>
        <!-- Table Rows -->
        <div class="overflow-y-auto flex-1">
          <div
            v-for="log in paginatedItems"
            :key="log.id"
            class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50"
          >
            <!-- Mobile Layout -->
            <div class="space-y-2 md:hidden">
              <div class="flex justify-between items-start">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ log.user }}</div>
                  <div class="text-xs text-gray-500">{{ log.userRole }}</div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-xs font-medium text-gray-900">{{ log.event }}</span>
                  <span
                    :class="[
                      'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                      log.status === 'Succeeded'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ log.status }}
                  </span>
                </div>
              </div>
              <div class="text-xs text-gray-500">{{ log.date }} {{ log.time }}</div>
              <div class="text-sm">
                <div
                  v-for="(desc, index) in log.descriptions"
                  :key="index"
                  class="mb-1"
                >
                  <div class="break-words">
                    <span class="font-medium text-gray-900">{{ desc.field }}:</span>
                    <span :class="desc.type === 'email' ? 'text-blue-600' : 'text-gray-600'" class="ml-1">{{ desc.value }}</span>
                  </div>
                  <div v-if="desc.label" class="text-xs font-medium">
                    <span :class="desc.label === 'Before' ? 'text-orange-600' : 'text-blue-600'">{{ desc.label }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Desktop Layout -->
            <div class="hidden grid-cols-12 gap-2 items-start md:grid">
              <!-- Date & Time -->
              <div class="col-span-2 text-sm text-gray-900">
                <div class="font-medium">{{ log.date }}</div>
                <div class="text-xs text-gray-500">{{ log.time }}</div>
              </div>

              <!-- Event -->
              <div class="col-span-1 text-sm font-medium text-gray-900">
                {{ log.event }}
              </div>

              <!-- User -->
              <div class="col-span-2 text-sm">
                <div class="font-medium text-gray-900 truncate">{{ log.user }}</div>
                <div v-if="log.userRole" class="text-xs text-gray-500 truncate">{{ log.userRole }}</div>
              </div>

              <!-- Description -->
              <div class="col-span-6 text-sm">
                <div class="space-y-1">
                  <div
                    v-for="(desc, index) in log.descriptions"
                    :key="index"
                    class="space-y-0.5"
                  >
                    <div class="break-words">
                      <span class="font-medium text-gray-900">{{ desc.field }}:</span>
                      <span :class="desc.type === 'email' ? 'text-blue-600' : 'text-gray-600'" class="ml-1">{{ desc.value }}</span>
                    </div>
                    <div v-if="desc.label" class="text-xs font-medium">
                      <span :class="desc.label === 'Before' ? 'text-orange-600' : 'text-blue-600'">{{ desc.label }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Status -->
              <div class="flex col-span-1 justify-end">
                <span
                  :class="[
                    'inline-flex px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap',
                    log.status === 'Succeeded'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ log.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="filteredLogs.length > 0" class="flex flex-col flex-shrink-0 gap-2 justify-between items-center px-4 py-3 bg-gray-50 border-t border-gray-200 sm:flex-row">
      <div class="order-2 text-xs text-gray-600 sm:order-1">
        Showing {{ ((currentPage - 1) * 10) + 1 }} to {{ Math.min(currentPage * 10, filteredLogs.length) }} of {{ filteredLogs.length }}
      </div>
      <div v-if="totalPages > 1" class="flex order-1 items-center space-x-1 sm:order-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="p-1.5 rounded border border-gray-300 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft class="w-3 h-3" />
        </button>
        <span class="px-2 text-xs text-gray-600">
          {{ currentPage }} / {{ totalPages }}
        </span>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="p-1.5 rounded border border-gray-300 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight class="w-3 h-3" />
        </button>
      </div>
    </div>
  </div>
</template>
