<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useBanking } from "@/composables/useBanking";
import { useRoute } from "vue-router";

const route = useRoute();

const {
  bankingInfo,
  fieldErrors,
  isCreateMode,
  isLoading,
  validateField,
  validateForm,
  createBankingInfo,
  getBankingByTalentId,
  updateBankingInfo,
} = useBanking();

// Label states for floating labels
const labelStates = ref({
  bankName: false,
  accountNumber: false,
  clabe: false,
  swiftCode: false,
});

// Bank dropdown state
const showBankDropdown = ref(false);
const bankOptions = [
  "BANK OF MEXICO",
  "BBVA BANCOMER",
  "SANTANDER",
  "BANAMEX",
  "HSBC",
  "SCOTIABANK",
];

// Filtered bank options based on user input
const filteredBankOptions = computed(() => {
  if (!bankingInfo?.value?.bankName.trim()) {
    return bankOptions;
  }
  return bankOptions.filter((bank) =>
    bank.toLowerCase().includes(bankingInfo.value?.bankName.toLowerCase() || '')
  );
});

const setLabelActive = (field: string, active: boolean) => {
  labelStates.value[field as keyof typeof labelStates.value] = active;
};

const selectBank = (bank: string) => {
  bankingInfo.value.bankName = bank;
  showBankDropdown.value = false;
  setLabelActive("bankName", false);
  validateField("bankName", bankingInfo.value.bankName);
};

const handleBankFocus = () => {
  setLabelActive("bankName", true);
  showBankDropdown.value = true;
};

const handleBankInput = () => {
  showBankDropdown.value = true;
  validateField("bankName", bankingInfo.value?.bankName);
};

const handleBankBlur = () => {
  // Delay to allow click on dropdown options
  setTimeout(() => {
    setLabelActive("bankName", false);
    showBankDropdown.value = false;
    validateField("bankName", bankingInfo.value?.bankName);
  }, 150);
};

const submitBankingInfo = async () => {
  if (!validateForm().length) {
    bankingInfo.value.talentProfileId = Number(route.params.id);
    if (!isCreateMode.value) {
      await updateBankingInfo(bankingInfo.value.id!, bankingInfo.value);
    } else {
      await createBankingInfo(bankingInfo.value);
    }
  }
};

onMounted(async () => {
  if (route.params.id) {
    await getBankingByTalentId(Number(route.params.id));
  }
});
</script>

<template>
  <div class="px-8 space-y-6">
    <!-- Banking Info Form -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">
        Banking Information
      </h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <!-- Bank Name -->
        <div class="relative">
          <div class="floating-label-container">
            <input
              v-model="bankingInfo.bankName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.bankName }"
              @focus="handleBankFocus"
              @input="handleBankInput"
              @blur="handleBankBlur"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.bankName || bankingInfo.bankName,
                  'text-red-600': fieldErrors.bankName,
                },
              ]"
            >
              Bank Name
            </label>
          </div>

          <!-- Custom Dropdown -->
          <div
            v-if="showBankDropdown && filteredBankOptions.length > 0"
            class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
          >
            <div
              v-for="bank in filteredBankOptions"
              :key="bank"
              class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
              @click="selectBank(bank)"
            >
              {{ bank }}
            </div>
          </div>

          <div class="mt-1 min-h-[20px]">
            <div v-if="fieldErrors.bankName" class="text-sm text-red-600">
              {{ fieldErrors.bankName }}
            </div>
          </div>
        </div>

        <!-- Account Number -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bankingInfo.accountNumber"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.accountNumber }"
              @focus="setLabelActive('accountNumber', true)"
              @blur="
                setLabelActive('accountNumber', false);
                validateField('accountNumber', bankingInfo.accountNumber);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.accountNumber || bankingInfo.accountNumber,
                  'text-red-600': fieldErrors.accountNumber,
                },
              ]"
            >
              Account Number
            </label>
          </div>
          <div class="mt-1 min-h-[20px]">
            <div v-if="fieldErrors.accountNumber" class="text-sm text-red-600">
              {{ fieldErrors.accountNumber }}
            </div>
          </div>
        </div>

        <!-- CLABE -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bankingInfo.clabe"
              type="text"
              maxlength="18"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.clabe }"
              @focus="setLabelActive('clabe', true)"
              @blur="
                setLabelActive('clabe', false);
                validateField('clabe', bankingInfo.clabe);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600': labelStates.clabe || bankingInfo.clabe,
                  'text-red-600': fieldErrors.clabe,
                },
              ]"
            >
              CLABE
            </label>
          </div>
          <div class="mt-1 min-h-[20px]">
            <div v-if="fieldErrors.clabe" class="text-sm text-red-600">
              {{ fieldErrors.clabe }}
            </div>
          </div>
        </div>

        <!-- SWIFT Code -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bankingInfo.swiftCode"
              type="text"
              maxlength="11"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.swiftCode }"
              @focus="setLabelActive('swiftCode', true)"
              @blur="
                setLabelActive('swiftCode', false);
                validateField('swiftCode', bankingInfo.swiftCode);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.swiftCode || bankingInfo.swiftCode,
                  'text-red-600': fieldErrors.swiftCode,
                },
              ]"
            >
              SWIFT Code
            </label>
          </div>
          <div class="mt-1 min-h-[20px]">
            <div v-if="fieldErrors.swiftCode" class="text-sm text-red-600">
              {{ fieldErrors.swiftCode }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end pt-6">
      <button
        @click="submitBankingInfo"
        :disabled="isLoading"
        class="px-6 py-3 font-medium text-white rounded-lg shadow-sm transition-colors bg-primary-600 hover:bg-primary-700 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isLoading" class="flex items-center">
          <svg class="mr-3 -ml-1 w-5 h-5 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Submitting...
        </span>
        <span v-else>SUBMIT</span>
      </button>
    </div>
  </div>
</template>
