<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { ChevronLeft, ChevronRight, Edit, Search, ChevronDown } from "lucide-vue-next";
import StatusModal from "./StatusModal.vue";
import dayjs from "dayjs";

// Composables
import { useTalentTable } from "@/composables/talent/useTalentTable";
import { usePagination } from "@/composables/common/usePagination";
import { usePhoneInput } from "@/composables/talent/usePhoneInput";

// Types
import type { TalentProfileResponse } from "@/types/talent";
import { getBackgroundColor, getInitials } from "@/utils/image-background";

// Composables setup
const { fetchAllTalents, updateTalentStatus, talents, isLoading } =
  useTalentTable();
const { formatPhoneDisplay } = usePhoneInput();

// Filter states
const searchQuery = ref("");
const selectedStatus = ref("all");
const selectedBpoManager = ref("all");

// Dropdown states
const showStatusDropdown = ref(false);
const showBpoManagerDropdown = ref(false);

// Computed filtered talents
const filteredTalents = computed(() => {
  let filtered = talents.value;

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter((talent: TalentProfileResponse) => {
      const fullName = `${talent.talentName}`.toLowerCase();
      const phone = talent.phone?.toLowerCase() || "";
      const position = talent.jobPosition?.toLowerCase() || "";
      const supervisor = talent.supervisor?.toLowerCase() || "";

      return fullName.includes(query) ||
             phone.includes(query) ||
             position.includes(query) ||
             supervisor.includes(query);
    });
  }

  // Apply status filter
  if (selectedStatus.value !== "all") {
    filtered = filtered.filter((talent: TalentProfileResponse) => talent.status);
  }

  // Apply BPO manager filter
  if (selectedBpoManager.value !== "all") {
    filtered = filtered.filter((talent: TalentProfileResponse) =>
      talent.supervisor === selectedBpoManager.value
    );
  }

  return filtered;
});

// Get unique BPO managers for dropdown
const bpoManagers = computed(() => {
  const managers = new Set<string>();
  talents.value.forEach((talent: TalentProfileResponse) => {
    if (talent.supervisor) {
      managers.add(talent.supervisor);
    }
  });
  return Array.from(managers).sort();
});

const { currentPage, totalPages, paginatedItems, nextPage, previousPage, goToPage } =
  usePagination(filteredTalents, 10);

// Reset to page 1 when filters change
watch([searchQuery, selectedStatus, selectedBpoManager], () => {
  goToPage(1);
});

// Modal state
const showStatusModal = ref(false);
const modalAction = ref<"activate" | "deactivate">("activate");
const selectedTalent = ref<TalentProfileResponse | null>(null);

// Modal handlers
const openStatusModal = (talent: TalentProfileResponse) => {
  selectedTalent.value = talent;
  modalAction.value = talent.status ? "deactivate" : "activate";
  showStatusModal.value = true;
};

const closeStatusModal = () => {
  showStatusModal.value = false;
  selectedTalent.value = null;
};

const handleStatusChange = async (data: {
  action: string;
  reason: string;
  employeeName: string;
}) => {
  if (!selectedTalent.value) return;

  try {
    await updateTalentStatus(selectedTalent.value.id, data.action, data.reason);
    await fetchAllTalents();
    closeStatusModal();
  } catch (err) {
    console.error("Failed to update talent status:", err);
  }
};

// Navigation
const editTalent = (talent: TalentProfileResponse) => {
  window.location.href = `/talent/${talent.id}/profile`;
};

// Filter handlers
const clearFilters = () => {
  searchQuery.value = "";
  selectedStatus.value = "all";
  selectedBpoManager.value = "all";
};

const toggleStatusDropdown = () => {
  showStatusDropdown.value = !showStatusDropdown.value;
  showBpoManagerDropdown.value = false;
};

const toggleBpoManagerDropdown = () => {
  showBpoManagerDropdown.value = !showBpoManagerDropdown.value;
  showStatusDropdown.value = false;
};

const selectStatus = (status: string) => {
  selectedStatus.value = status;
  showStatusDropdown.value = false;
};

const selectBpoManager = (manager: string) => {
  selectedBpoManager.value = manager;
  showBpoManagerDropdown.value = false;
};

// Close dropdowns when clicking outside
const closeDropdowns = () => {
  showStatusDropdown.value = false;
  showBpoManagerDropdown.value = false;
};

// Generate visible page numbers for pagination
const getVisiblePages = () => {
  const pages: (number | string)[] = [];
  const total = totalPages.value;
  const current = currentPage.value;

  if (total <= 7) {
    // Show all pages if total is 7 or less
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (current <= 4) {
      // Show pages 2-5 and ellipsis
      for (let i = 2; i <= 5; i++) {
        pages.push(i);
      }
      pages.push('...');
    } else if (current >= total - 3) {
      // Show ellipsis and last 4 pages
      pages.push('...');
      for (let i = total - 4; i <= total - 1; i++) {
        pages.push(i);
      }
    } else {
      // Show ellipsis, current page area, and ellipsis
      pages.push('...');
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
    }

    // Always show last page
    if (total > 1) {
      pages.push(total);
    }
  }

  return pages;
};

// Lifecycle
onMounted(async () => {
  await fetchAllTalents();
});
</script>

<template>
  <div class="bg-white rounded-lg border border-gray-200 shadow-sm" @click="closeDropdowns">
    <!-- Filters Section -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex flex-col gap-4 justify-between items-start sm:flex-row sm:items-center">
        <!-- Search Input -->
        <div class="relative flex-1 max-w-md">
          <div class="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
            <Search class="w-5 h-5 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by name, phone, position, or supervisor..."
            class="block py-2 pr-3 pl-10 w-full text-sm leading-5 placeholder-gray-500 bg-white rounded-md border border-gray-300 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>

        <!-- Filter Dropdowns -->
        <div class="flex gap-3">
          <!-- Status Filter -->
          <div class="relative" @click.stop>
            <button
              @click="toggleStatusDropdown"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Status: {{ selectedStatus === 'all' ? 'All' : selectedStatus === 'active' ? 'Active' : 'Inactive' }}
              <ChevronDown class="ml-2 w-4 h-4" />
            </button>
            <div
              v-if="showStatusDropdown"
              class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg"
            >
              <div class="py-1">
                <button
                  @click="selectStatus('all')"
                  :class="[
                    'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                    selectedStatus === 'all' ? 'bg-orange-50 text-orange-700' : 'text-gray-700'
                  ]"
                >
                  All Status
                </button>
                <button
                  @click="selectStatus('active')"
                  :class="[
                    'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                    selectedStatus === 'active' ? 'bg-orange-50 text-orange-700' : 'text-gray-700'
                  ]"
                >
                  Active
                </button>
                <button
                  @click="selectStatus('inactive')"
                  :class="[
                    'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                    selectedStatus === 'inactive' ? 'bg-orange-50 text-orange-700' : 'text-gray-700'
                  ]"
                >
                  Inactive
                </button>
              </div>
            </div>
          </div>

          <!-- BPO Manager Filter -->
          <div class="relative" @click.stop>
            <button
              @click="toggleBpoManagerDropdown"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              BPO Manager: {{ selectedBpoManager === 'all' ? 'All' : selectedBpoManager }}
              <ChevronDown class="ml-2 w-4 h-4" />
            </button>
            <div
              v-if="showBpoManagerDropdown"
              class="overflow-y-auto absolute right-0 z-10 mt-2 w-64 max-h-60 bg-white rounded-md ring-1 ring-black ring-opacity-5 shadow-lg"
            >
              <div class="py-1">
                <button
                  @click="selectBpoManager('all')"
                  :class="[
                    'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                    selectedBpoManager === 'all' ? 'bg-orange-50 text-orange-700' : 'text-gray-700'
                  ]"
                >
                  All BPO Managers
                </button>
                <button
                  v-for="manager in bpoManagers"
                  :key="manager"
                  @click="selectBpoManager(manager)"
                  :class="[
                    'block w-full text-left px-4 py-2 text-sm hover:bg-gray-100',
                    selectedBpoManager === manager ? 'bg-orange-50 text-orange-700' : 'text-gray-700'
                  ]"
                >
                  {{ manager }}
                </button>
              </div>
            </div>
          </div>

          <!-- Clear Filters Button -->
          <button
            v-if="searchQuery || selectedStatus !== 'all' || selectedBpoManager !== 'all'"
            @click="clearFilters"
            class="px-4 py-2 text-sm font-medium text-gray-600 rounded-md transition-colors hover:text-gray-800 hover:bg-gray-100"
          >
            Clear Filters
          </button>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div v-if="searchQuery || selectedStatus !== 'all' || selectedBpoManager !== 'all'" class="flex flex-wrap gap-2 mt-3">
        <span v-if="searchQuery" class="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-800 bg-orange-100 rounded-full">
          Search: "{{ searchQuery }}"
        </span>
        <span v-if="selectedStatus !== 'all'" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
          Status: {{ selectedStatus === 'active' ? 'Active' : 'Inactive' }}
        </span>
        <span v-if="selectedBpoManager !== 'all'" class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
          BPO Manager: {{ selectedBpoManager }}
        </span>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center p-12">
      <div
        class="w-8 h-8 rounded-full border-b-2 border-orange-500 animate-spin"
      ></div>
    </div>

    <!-- Table Content -->
    <div v-else>
      <!-- Table Header -->
      <div
        class="grid grid-cols-7 gap-8 p-6 text-xs font-medium tracking-wider text-gray-500 uppercase border-b border-gray-200"
      >
        <div>NAME</div>
        <div>PHONE NUMBER</div>
        <div>DATE</div>
        <div>JOB POSITION</div>
        <div>SUPERVISOR</div>
        <div>STATUS</div>
        <div>EDIT</div>
      </div>
     
      <!-- Empty State -->
      <div v-if="filteredTalents.length === 0" class="py-12 text-center">
        <div class="mb-4 text-gray-400">
          <svg class="mx-auto w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
          </svg>
        </div>
        <h3 class="mb-2 text-lg font-medium text-gray-900">No talents found</h3>
        <p class="mb-4 text-gray-500">
          {{ searchQuery || selectedStatus !== 'all' || selectedBpoManager !== 'all'
             ? 'Try adjusting your filters to see more results.'
             : 'No talent records are available.' }}
        </p>
        <button
          v-if="searchQuery || selectedStatus !== 'all' || selectedBpoManager !== 'all'"
          @click="clearFilters"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 rounded-md border border-transparent hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Clear all filters
        </button>
      </div>

      <!-- Table Rows -->
      <div v-if="filteredTalents.length" class="divide-y divide-gray-100">
        <div
          v-for="talent in paginatedItems"
          :key="talent.id"
          class="grid grid-cols-7 gap-8 items-center p-6"
        >
          <!-- Name with Avatar -->
          <div class="flex items-center space-x-3 min-w-0">
            <img
              v-if="talent.pic"
              :src="talent.pic"
              :alt="talent.talentName"
              class="object-cover flex-shrink-0 w-10 h-10 rounded-full"
            />
            <div
              v-else
              :class="[
                'flex justify-center items-center w-10 h-10 rounded-full border-2 border-gray-200 font-medium text-sm flex-shrink-0',
                getBackgroundColor(talent.talentName),
              ]"
            >
              {{ getInitials(talent.talentName) }}
            </div>
            <span class="flex-1 min-w-0 text-sm font-medium leading-tight text-gray-900 line-clamp-2">
              {{
                `${talent.talentName}`
                  .replace(/\s+/g, " ")
                  .trim()
              }}
            </span>
          </div>

          <!-- Phone -->
          <div class="text-sm text-gray-600">
            {{ formatPhoneDisplay(talent.phone) }}
          </div>

          <!-- Date -->
          <div class="text-sm text-gray-600">
            {{ dayjs(talent.startDate).format("MMM D, YYYY") }}
          </div>

          <!-- Job Position -->
          <div>
            <span
              class="inline-flex px-3 py-1 text-xs font-medium text-orange-600 whitespace-nowrap bg-orange-50 rounded-full"
            >
              {{ (talent.jobPosition || "N/A").toLocaleUpperCase() }}
            </span>
          </div>

          <!-- Supervisor -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600 whitespace-nowrap">
              {{ talent?.supervisor }}
            </span>
          </div>

          <!-- Status -->
          <div>
            <button
              @click="openStatusModal(talent)"
              :class="[
                'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full hover:opacity-80 transition-opacity cursor-pointer',
                talent.status
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800',
              ]"
            >
              <span
                :class="[
                  'w-1.5 h-1.5 rounded-full mr-1',
                  talent.status ? 'bg-green-500' : 'bg-red-500',
                ]"
              ></span>
              {{ talent.status ? "Active" : "Inactive" }}
            </button>
          </div>

          <!-- Edit -->
          <div>
            <button
              @click="editTalent(talent)"
              class="p-2 text-gray-400 transition-colors hover:text-gray-600"
              :title="`Edit ${talent.talentName}`"
            >
              <Edit :size="16" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination - Moved to bottom -->
    <div
      v-if="filteredTalents.length"
      class="flex justify-between items-center px-6 py-4 bg-gray-50 border-t border-gray-200"
    >
      <div class="flex items-center text-sm text-gray-700">
        <span>
          Showing {{ ((currentPage - 1) * 10) + 1 }} to {{ Math.min(currentPage * 10, filteredTalents.length) }} of {{ filteredTalents.length }} results
        </span>
      </div>
      <div v-if="totalPages > 1" class="flex items-center space-x-2">
        <button
          @click="previousPage"
          :disabled="currentPage === 1"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
        >
          <ChevronLeft class="mr-1 w-4 h-4" />
          Previous
        </button>

        <!-- Page Numbers -->
        <div class="flex items-center space-x-1">
          <template v-for="page in getVisiblePages()" :key="page">
            <button
              v-if="page !== '...'"
              @click="goToPage(page as number)"
              :class="[
                'px-3 py-2 text-sm font-medium rounded-md',
                currentPage === page
                  ? 'bg-orange-500 text-white'
                  : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
            <span v-else class="px-2 py-2 text-sm text-gray-500">...</span>
          </template>
        </div>

        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
        >
          Next
          <ChevronRight class="ml-1 w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Status Modal -->
    <StatusModal
      :is-open="showStatusModal"
      :action="modalAction"
      :employee-name="selectedTalent?.talentName || ''"
      @close="closeStatusModal"
      @confirm="handleStatusChange"
    />
  </div>
</template>
