<template>
  <div class="p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Permission System Demo</h2>
    
    <!-- User Info -->
    <div class="mb-6 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">User Information</h3>
      <p><strong>Is Superuser:</strong> {{ isSuperuser ? 'Yes' : 'No' }}</p>
      <p><strong>Role:</strong> {{ userRole?.role || 'N/A' }}</p>
      <p><strong>Role Description:</strong> {{ userRole?.description || 'N/A' }}</p>
    </div>

    <!-- Sidebar Navigation Demo -->
    <div class="mb-6 p-4 bg-green-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Sidebar Navigation Visibility</h3>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p><strong>Talent:</strong> {{ canAccessTalent ? '✅ Visible' : '❌ Hidden' }}</p>
          <p><strong>Users:</strong> {{ canAccessUsers ? '✅ Visible' : '❌ Hidden' }}</p>
          <p><strong>Roles:</strong> {{ canAccessRoles ? '✅ Visible' : '❌ Hidden' }}</p>
        </div>
        <div>
          <p><strong>Clients:</strong> {{ canAccessClients ? '✅ Visible' : '❌ Hidden' }}</p>
          <p><strong>Equipment:</strong> {{ canAccessEquipment ? '✅ Visible' : '❌ Hidden' }}</p>
          <p><strong>History Log:</strong> {{ canAccessHistoryLog ? '✅ Visible' : '❌ Hidden' }}</p>
        </div>
      </div>
    </div>

    <!-- Talent Module Permissions -->
    <div class="mb-6 p-4 bg-yellow-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Talent Module Permissions</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 class="font-medium mb-2">Profile & Basic Info</h4>
          <p><strong>Talent Profile:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_PROFILE) }}</p>
          <p><strong>Banking Info:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_BANKING_INFO) }}</p>
          <p><strong>Documents:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_DOCUMENTS) }}</p>
        </div>
        <div>
          <h4 class="font-medium mb-2">Work & Health Info</h4>
          <p><strong>Work History:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_WORK_HISTORY) }}</p>
          <p><strong>BPO Info:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_BPO_RELATED_INFO) }}</p>
          <p><strong>Health:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_HEALTH) }}</p>
        </div>
        <div>
          <h4 class="font-medium mb-2">Technical & Integration</h4>
          <p><strong>Equipment & Software:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_EQUIPMENT_SOFTWARE) }}</p>
          <p><strong>3rd Party Integrations:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_3RD_PARTY_INTEGRATIONS) }}</p>
        </div>
        <div>
          <h4 class="font-medium mb-2">History Logs</h4>
          <p><strong>Talent History Logs:</strong> {{ getTalentPermissionStatus(ModuleId.TALENT_HISTORY_LOGS) }}</p>
          <p class="text-sm text-gray-600">(Only visible to superusers)</p>
        </div>
      </div>
    </div>

    <!-- Permission Rules Summary -->
    <div class="mb-6 p-4 bg-purple-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Permission Rules Applied</h3>
      <ul class="list-disc list-inside space-y-1 text-sm">
        <li><strong>Talent Section:</strong> Visible only if user has at least one permission (create, list, edit, view) for any talent module</li>
        <li><strong>Individual Talent Tabs:</strong> Each tab is visible based on specific module permissions</li>
        <li><strong>History Logs:</strong> Only visible to superusers (both talent and system history logs)</li>
        <li><strong>Other Modules:</strong> Visible based on individual module permissions</li>
        <li><strong>Superusers:</strong> Have access to all modules and features</li>
      </ul>
    </div>

    <!-- Raw Permissions Data -->
    <div class="p-4 bg-gray-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Raw Permissions Data</h3>
      <details class="cursor-pointer">
        <summary class="font-medium">Click to view raw API response</summary>
        <pre class="mt-2 text-xs bg-white p-2 rounded border overflow-auto">{{ JSON.stringify(userRole, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const {
  isSuperuser,
  userRole,
  canAccessTalent,
  canAccessUsers,
  canAccessRoles,
  canAccessClients,
  canAccessEquipment,
  canAccessHistoryLog,
  canView,
  canEdit,
  canCreate,
  canList
} = usePermissions()

// Helper function to get permission status for talent modules
const getTalentPermissionStatus = (moduleId: ModuleId): string => {
  if (isSuperuser.value) {
    return '✅ Full Access (Superuser)'
  }
  
  const permissions = []
  if (canView(moduleId)) permissions.push('View')
  if (canEdit(moduleId)) permissions.push('Edit')
  if (canCreate(moduleId)) permissions.push('Create')
  if (canList(moduleId)) permissions.push('List')
  
  if (permissions.length === 0) {
    return '❌ No Access'
  }
  
  return `✅ ${permissions.join(', ')}`
}
</script>
