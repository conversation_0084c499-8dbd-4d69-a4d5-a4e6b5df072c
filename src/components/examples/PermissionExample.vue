<template>
  <div class="p-6 space-y-6">
    <h2 class="text-2xl font-bold text-gray-900">Permission System Examples</h2>

    <!-- User Info -->
    <div class="p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold text-blue-900">Current User</h3>
      <p class="text-blue-700">
        <span v-if="isSuperuser" class="font-bold text-green-600">Superuser</span>
        <span v-else>Regular User</span>
        - Role: {{ userRole?.role || 'No role assigned' }}
      </p>
    </div>

    <!-- Permission Checks -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Talent Module -->
      <div class="p-4 border border-gray-200 rounded-lg">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Talent Module</h4>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canAccessTalent ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Access: {{ canAccessTalent ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canViewTalent ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can View: {{ canViewTalent ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canEditTalent ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Edit: {{ canEditTalent ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canCreateTalent ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Create: {{ canCreateTalent ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>

      <!-- Users Module -->
      <div class="p-4 border border-gray-200 rounded-lg">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Users Module</h4>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canAccessUsers ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Access: {{ canAccessUsers ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canViewUsers ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can View: {{ canViewUsers ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canEditUsers ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Edit: {{ canEditUsers ? 'Yes' : 'No' }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="w-2 h-2 rounded-full" :class="canCreateUsers ? 'bg-green-500' : 'bg-red-500'"></span>
            <span>Can Create: {{ canCreateUsers ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Directive Examples -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">Directive Examples</h3>
      
      <!-- Permission directive -->
      <div 
        v-permission="{ moduleId: ModuleId.TALENT, permission: 'create' }"
        class="p-3 bg-green-50 border border-green-200 rounded-lg"
      >
        <p class="text-green-800">This content is only visible if you can CREATE in Talent module</p>
      </div>

      <div 
        v-permission="{ moduleId: ModuleId.USERS, permission: 'edit' }"
        class="p-3 bg-blue-50 border border-blue-200 rounded-lg"
      >
        <p class="text-blue-800">This content is only visible if you can EDIT in Users module</p>
      </div>

      <!-- Superuser directive -->
      <div 
        v-superuser
        class="p-3 bg-purple-50 border border-purple-200 rounded-lg"
      >
        <p class="text-purple-800">This content is only visible to SUPERUSERS</p>
      </div>

      <!-- Module access directive -->
      <div 
        v-module-access="ModuleId.ROLES"
        class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
      >
        <p class="text-yellow-800">This content is only visible if you can ACCESS the Roles module</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">Action Buttons</h3>
      <div class="flex flex-wrap gap-3">
        <!-- Create Talent Button -->
        <button
          v-if="canCreateTalent"
          class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          Create Talent
        </button>

        <!-- Edit Users Button -->
        <button
          v-if="canEditUsers"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Edit Users
        </button>

        <!-- Superuser Only Button -->
        <button
          v-if="isSuperuser"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Superuser Action
        </button>

        <!-- Always Visible Button -->
        <button
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Always Visible
        </button>
      </div>
    </div>

    <!-- Accessible Modules -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900">Accessible Modules</h3>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="moduleId in accessibleModules"
          :key="moduleId"
          class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm"
        >
          Module {{ moduleId }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const {
  isSuperuser,
  userRole,
  canAccessTalent,
  canViewTalent,
  canEditTalent,
  canCreateTalent,
  canAccessUsers,
  canViewUsers,
  canEditUsers,
  canCreateUsers,
  getAccessibleModules
} = usePermissions()

const accessibleModules = computed(() => getAccessibleModules())
</script>
