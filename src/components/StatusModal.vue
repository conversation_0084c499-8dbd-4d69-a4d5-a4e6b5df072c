<script setup lang="ts">
import { ref, watch } from 'vue'
import { X } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  action: 'activate' | 'deactivate'
  employeeName: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { action: string, reason: string, employeeName: string }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const reason = ref('')

const closeModal = () => {
  reason.value = ''
  emit('close')
}

const confirmAction = () => {
  if (reason.value.trim()) {
    emit('confirm', {
      action: props.action,
      reason: reason.value.trim(),
      employeeName: props.employeeName
    })
    closeModal()
  }
}

// Reset reason when modal opens/closes
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    reason.value = ''
  }
})
</script>

<template>
  <div v-if="isOpen" class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-50">
    <div class="p-6 w-96 bg-white rounded-lg shadow-xl">
      <!-- Header -->
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ action === 'activate' ? 'Activate Talent' : 'Deactivate Talent' }} {{ employeeName }}
        </h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
          <X :size="20" />
        </button>
      </div>

      <!-- Content -->
      <div class="mb-6">
        <label class="block mb-2 text-sm font-medium text-gray-700">
          Add a reason for {{ action === 'activate' ? 'activation' : 'deactivation' }}
          <span class="text-red-500">*</span>
        </label>
        <textarea
          v-model="reason"
          placeholder="Write here..."
          class="px-3 py-2 w-full rounded-md border border-gray-300 resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          rows="3"
        ></textarea>
      </div>

      <!-- Buttons -->
      <div class="flex justify-end space-x-3">
        <button
          @click="closeModal"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
        <button
          @click="confirmAction"
          :disabled="!reason.trim()"
          :class="[
            'px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
            action === 'activate' 
              ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500 disabled:bg-green-300' 
              : 'bg-red-600 hover:bg-red-700 focus:ring-red-500 disabled:bg-red-300'
          ]"
        >
          {{ action === 'activate' ? 'Activate Talent' : 'Deactivate Talent' }}
        </button>
      </div>
    </div>
  </div>
</template>
