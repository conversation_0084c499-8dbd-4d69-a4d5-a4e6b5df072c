<template>
  <div v-if="isOpen" :class="calendarClass">
    <!-- Calendar Header -->
    <div class="flex justify-between items-center mb-4">
      <button 
        @click="previousMonth"
        class="p-1 rounded hover:bg-gray-100"
      >
        <ChevronLeft :size="16" />
      </button>
      <div class="flex items-center space-x-2">
        <select 
          v-model="currentMonth"
          class="text-sm font-semibold bg-transparent border-none cursor-pointer focus:outline-none"
        >
          <option v-for="(month, index) in monthNames" :key="index" :value="index">
            {{ month }}
          </option>
        </select>
        <select 
          v-model="currentYear"
          class="text-sm font-semibold bg-transparent border-none cursor-pointer focus:outline-none"
        >
          <option v-for="year in availableYears" :key="year" :value="year">
            {{ year }}
          </option>
        </select>
      </div>
      <button 
        @click="nextMonth"
        class="p-1 rounded hover:bg-gray-100"
      >
        <ChevronRight :size="16" />
      </button>
    </div>

    <!-- Calendar Grid -->
    <div class="grid grid-cols-7 gap-1 mb-2">
      <div 
        v-for="day in dayNames" 
        :key="day"
        class="p-2 text-xs font-medium text-center text-gray-500"
      >
        {{ day }}
      </div>
    </div>

    <div class="grid grid-cols-7 gap-1">
      <button
        v-for="date in calendarDates"
        :key="date.key"
        @click="selectDate(date)"
        :disabled="!date.isSelectable"
        :class="[
          'p-2 text-sm rounded transition-colors',
          {
            'text-gray-400': !date.isCurrentMonth,
            'text-gray-300 cursor-not-allowed': !date.isSelectable,
            'hover:bg-gray-100 cursor-pointer': date.isSelectable,
            'bg-primary-500 text-white hover:bg-primary-600': date.isSelected && date.isSelectable,
            'bg-gray-100': date.isToday && !date.isSelected && date.isSelectable,
            'font-semibold': date.isToday
          }
        ]"
      >
        {{ date.date }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

interface Props {
  isOpen: boolean
  selectedDate: Date
  position?: 'left' | 'right'
  maxDate?: Date
  minDate?: Date
}

interface Emits {
  (e: 'select-date', date: Date): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  position: 'left',
  maxDate: () => new Date(),
  minDate: () => new Date(1900, 0, 1)
})
const emit = defineEmits<Emits>()

const currentMonth = ref(props.selectedDate.getMonth())
const currentYear = ref(props.selectedDate.getFullYear())

const calendarClass = computed(() => [
  'absolute top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 w-80',
  props.position === 'right' ? 'right-0' : 'left-0'
])

const monthNames = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]

const dayNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S']

const calendarDates = computed(() => {
  const dates = []
  const firstDay = new Date(currentYear.value, currentMonth.value, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  // Create dates in local timezone to avoid UTC issues
  const today = new Date()
  const todayLocal = new Date(today.getFullYear(), today.getMonth(), today.getDate())
  
  const selectedDate = new Date(props.selectedDate)
  const selectedLocal = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
  
  const maxDate = new Date(props.maxDate)
  const maxLocal = new Date(maxDate.getFullYear(), maxDate.getMonth(), maxDate.getDate())
  
  const minDate = new Date(props.minDate)
  const minLocal = new Date(minDate.getFullYear(), minDate.getMonth(), minDate.getDate())

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    const dateLocal = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    
    const isSelectable = dateLocal >= minLocal && dateLocal <= maxLocal
    
    dates.push({
      date: date.getDate(),
      fullDate: dateLocal,
      isCurrentMonth: date.getMonth() === currentMonth.value,
      isToday: dateLocal.getTime() === todayLocal.getTime(),
      isSelected: dateLocal.getTime() === selectedLocal.getTime(),
      isSelectable: isSelectable,
      key: dateLocal.toDateString()
    })
  }

  return dates
})

const previousMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11
    currentYear.value--
  } else {
    currentMonth.value--
  }
}

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0
    currentYear.value++
  } else {
    currentMonth.value++
  }
}

const selectDate = (dateObj: any) => {
  if (dateObj.isSelectable) {
    emit('select-date', dateObj.fullDate)
    emit('close')
  }
}

watch(() => props.selectedDate, (newDate) => {
  currentMonth.value = newDate.getMonth()
  currentYear.value = newDate.getFullYear()
})

const availableYears = computed(() => {
  const years = []
  const startYear = props.minDate.getFullYear()
  const endYear = props.maxDate.getFullYear()
  
  for (let year = endYear; year >= startYear; year--) {
    years.push(year)
  }
  return years
})
</script>
