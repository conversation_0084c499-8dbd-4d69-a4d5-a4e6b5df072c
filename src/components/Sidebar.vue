<template>
  <div :class="sidebarClass">
    <!-- Header -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-center">
        <h1 v-if="!collapsed" class="text-2xl font-bold text-gray-800 cursor-pointer" @click="router.push('/dashboard')">BPO</h1>
        <h1 v-else class="text-xl font-bold text-gray-800">B</h1>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1">
      <div
        v-for="item in menuItems"
        :key="item.name"
        :class="[
          'sidebar-item',
          { active: item.name === activeItem },
          collapsed ? 'justify-center px-2' : 'px-4'
        ]"
        @click="navigateToItem(item.name)"
      >
        <component :is="item.icon" :size="20" class="flex-shrink-0" />
        <span v-if="!collapsed" class="ml-3 text-sm font-medium">{{ item.name }}</span>
      </div>
    </nav>

    <!-- Collapse Toggle -->
    <div class="px-2 py-4 border-t border-gray-200">
      <button 
        @click="toggleSidebar"
        :class="[
          'flex items-center w-full text-gray-600 hover:text-primary-600 transition-colors duration-200',
          collapsed ? 'justify-center px-2' : 'px-2'
        ]"
      >
        <Menu :size="20" />
        <span v-if="!collapsed" class="ml-3 text-sm font-medium">Collapse Sidebar</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'
import {
  Users,
  UserCheck,
  Shield,
  Building2,
  Wrench,
  History,
  Menu
} from 'lucide-vue-next'

const router = useRouter()
const {
  canAccessUsers,
  canAccessRoles,
  canAccessClients,
  canAccessEquipment,
  canAccessHistoryLog,
  hasAnyTalentPermission
} = usePermissions()

interface Props {
  collapsed: boolean
  activeItem: string
}

interface Emits {
  (e: 'toggle-sidebar'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// All possible menu items
const allMenuItems = [
  { name: 'Talent', icon: Users, moduleId: ModuleId.TALENT, canAccess: computed(() => hasAnyTalentPermission()) },
  { name: 'Users', icon: UserCheck, moduleId: ModuleId.USERS, canAccess: canAccessUsers },
  { name: 'Roles', icon: Shield, moduleId: ModuleId.ROLES, canAccess: canAccessRoles },
  { name: 'Clients', icon: Building2, moduleId: ModuleId.CLIENTS, canAccess: canAccessClients },
  { name: 'Equipment', icon: Wrench, moduleId: ModuleId.EQUIPMENT, canAccess: canAccessEquipment },
  { name: 'History Log', icon: History, moduleId: ModuleId.HISTORY_LOG, canAccess: canAccessHistoryLog },
]

// Filter menu items based on permissions
const menuItems = computed(() => {
  return allMenuItems.filter(item => item.canAccess.value)
})

const sidebarClass = computed(() => [
  'flex flex-col h-screen bg-white border-r border-gray-200 transition-all duration-300',
  props.collapsed ? 'w-16' : 'w-64'
])

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const navigateToItem = (item: string) => {
  const routes: Record<string, string> = {
    'Talent': '/talent',
    'Users': '/users',
    'Roles': '/roles',
    'Clients': '/clients',
    'Equipment': '/equipment',
    'History Log': '/history-log'
  }
  
  const route = routes[item]
  if (route) {
    router.push(route)
  }
}
</script>
