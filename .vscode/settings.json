{"basedpyright.analysis.typeCheckingMode": "strict", "basedpyright.analysis.autoImportCompletions": true, "basedpyright.analysis.autoSearchPaths": true, "editor.formatOnSave": true, "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "black-formatter.args": ["--line-length=88"], "keyboard.dispatch": "keyCode", "python.defaultInterpreterPath": "./.venv/bin/python", "python.analysis.extraPaths": ["./app"], "python.analysis.autoImportCompletions": true, "cursorpyright.analysis.autoImportCompletions": true, "cursorpyright.analysis.extraPaths": ["./app"], "css.validate": false, "scss.validate": false, "less.validate": false, "tailwindCSS.includeLanguages": {"css": "css", "scss": "scss"}, "files.associations": {"*.css": "tailwindcss"}}