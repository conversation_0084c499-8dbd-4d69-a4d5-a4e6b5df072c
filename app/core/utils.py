"""Utility functions for common operations across the application."""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List


def get_changed_data(
    old_data: Dict[str, Any], 
    new_data: Dict[str, Any], 
    exclude_fields: Optional[List[str]] = None
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Compare old and new data dictionaries to extract only changed fields.
    
    Args:
        old_data: Dictionary containing the original data
        new_data: Dictionary containing the updated data
        exclude_fields: List of field names to exclude from comparison (defaults to ['updated_at'])
        
    Returns:
        Tuple containing (changed_old_data, changed_new_data) dictionaries
        with only the fields that have different values
    """
    if exclude_fields is None:
        exclude_fields = ['updated_at']
    
    changed_old_data: Dict[str, Any] = {}
    changed_new_data: Dict[str, Any] = {}
    
    # Compare all keys from both dictionaries
    all_keys = set(old_data.keys()) | set(new_data.keys())
    
    for key in all_keys:
        # Skip excluded fields
        if key in exclude_fields:
            continue
            
        old_value = old_data.get(key)
        new_value = new_data.get(key)
        
        # Include field if values are different
        if old_value != new_value:
            if old_value is not None:
                changed_old_data[key] = old_value
            if new_value is not None:
                changed_new_data[key] = new_value
    
    return changed_old_data, changed_new_data