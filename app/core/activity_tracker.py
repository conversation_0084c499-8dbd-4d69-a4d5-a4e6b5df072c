"""Simple activity tracking utility for talent operations.

This module provides a utility function for tracking talent profile changes
and activities without complex decorator patterns.
"""

from datetime import datetime, date
from typing import Any, Dict, Optional
from fastapi import Request

from app.core.logs import log_error_with_context
from app.repositories.master.role_repository import RoleRepository
from app.repositories.user_repository import UserRepository


def serialize_datetime_objects(data: Dict[str, Any]) -> Dict[str, Any]:
    """Convert datetime objects in a dictionary to ISO format strings.
    
    Args:
        data: Dictionary that may contain datetime objects
        
    Returns:
        Dictionary with datetime objects converted to strings
    """
    if not isinstance(data, dict):
        return data
        
    result: Dict[str, Any] = {}
    for key, value in data.items():
        if isinstance(value, (datetime, date)):
            result[key] = value.isoformat()
        elif isinstance(value, dict):
            # Use type: ignore to suppress the type checker warning for recursive call
            result[key] = serialize_datetime_objects(value)  # type: ignore
        else:
            result[key] = value
    return result


async def track_talent_activity(
    talent_profile_id: Optional[int] = None,
    activity_type: str = '',
    activity_description: str = '',
    new_data: Optional[Dict[str, Any]] = None,
    old_data: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None,
    current_user: Optional[Any] = None
) -> bool:
    """Track talent activity with automatic context population.
    
    Args:
        talent_profile_id: ID of the talent profile
        activity_type: Type of activity (CREATE, UPDATE, DELETE, etc.)
        activity_description: Description of what was done
        new_data: New data after the operation
        old_data: Old data before the operation
        status: Status of the activity (completed, failed, etc.)
        request: FastAPI request object for extracting context
        current_user: Current user performing the action
        
    Returns:
        bool: True if tracking was successful, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from app.services.talent.activity_service import TalentActivityService
        from app.repositories.talent.activity_repository import TalentActivityRepository
        from app.db.session import get_session
        
        # Create repository and service instances
        session = next(get_session())
        repository = TalentActivityRepository(session)
        role_repository = RoleRepository(session)
        user_repository = UserRepository(session)
        activity_service = TalentActivityService(repository, user_repository, role_repository, current_user)
        
        # Extract context from request if available
        ip_address = None
        user_agent = None
        browser = None
        os = None
        device = None
        
        if request:
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get('user-agent', '')
            
            # Parse user agent for browser, OS, and device info
            if user_agent:
                user_agent_lower = user_agent.lower()
                
                # Extract browser
                if 'chrome' in user_agent_lower:
                    browser = 'Chrome'
                elif 'firefox' in user_agent_lower:
                    browser = 'Firefox'
                elif 'safari' in user_agent_lower:
                    browser = 'Safari'
                elif 'edge' in user_agent_lower:
                    browser = 'Edge'
                else:
                    browser = 'Unknown'
                
                # Extract OS
                if 'windows' in user_agent_lower:
                    os = 'Windows'
                elif 'mac' in user_agent_lower or 'darwin' in user_agent_lower:
                    os = 'macOS'
                elif 'linux' in user_agent_lower:
                    os = 'Linux'
                elif 'android' in user_agent_lower:
                    os = 'Android'
                elif 'ios' in user_agent_lower:
                    os = 'iOS'
                else:
                    os = 'Unknown'
                
                # Extract device type
                if 'mobile' in user_agent_lower or 'android' in user_agent_lower:
                    device = 'Mobile'
                elif 'tablet' in user_agent_lower or 'ipad' in user_agent_lower:
                    device = 'Tablet'
                else:
                    device = 'Desktop'
        
        # Serialize data to handle datetime objects
        serialized_old_data = serialize_datetime_objects(old_data) if old_data else None
        serialized_new_data = serialize_datetime_objects(new_data) if new_data else None
        
        # Use the generic create_activity method for all activity types
        activity_service.create_activity(
            talent_profile_id=talent_profile_id,
            activity_type=activity_type,
            activity=activity_description,
            old_data=serialized_old_data,
            new_data=serialized_new_data,
            ip_address=ip_address,
            user_agent=user_agent,
            browser=browser,
            os=os,
            device=device
        )
        
        return True
        
    except Exception as e:
        # Log error but don't fail the main operation
        log_error_with_context(e, {
            "talent_profile_id": talent_profile_id,
            "activity_type": activity_type,
            "activity_description": activity_description,
            "step": "track_talent_activity"
        })
        return False


def track_talent_activity_sync(
    talent_profile_id: Optional[int] = None,
    activity_type: str = '',
    activity_description: str = '',
    new_data: Optional[Dict[str, Any]] = None,
    old_data: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None,
    current_user: Optional[Any] = None
) -> bool:
    """Synchronous version of track_talent_activity.
    
    Args:
        talent_profile_id: ID of the talent profile
        activity_type: Type of activity (CREATE, UPDATE, DELETE, etc.)
        activity_description: Description of what was done
        new_data: New data after the operation
        old_data: Old data before the operation
        status: Status of the activity (completed, failed, etc.)
        request: FastAPI request object for extracting context
        current_user: Current user performing the action
        
    Returns:
        bool: True if tracking was successful, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from app.services.talent.activity_service import TalentActivityService
        from app.repositories.talent.activity_repository import TalentActivityRepository
        from app.db.session import get_session
        
        # Create repository and service instances
        session = next(get_session())
        repository = TalentActivityRepository(session)
        role_repository = RoleRepository(session)
        user_repository = UserRepository(session)
        activity_service = TalentActivityService(repository, user_repository, role_repository, current_user)
        
        # Extract context from request if available
        ip_address = None
        user_agent = None
        browser = None
        os = None
        device = None
        
        if request:
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get('user-agent', '')
            
            # Parse user agent for browser, OS, and device info
            if user_agent:
                user_agent_lower = user_agent.lower()
                
                # Extract browser
                if 'chrome' in user_agent_lower:
                    browser = 'Chrome'
                elif 'firefox' in user_agent_lower:
                    browser = 'Firefox'
                elif 'safari' in user_agent_lower:
                    browser = 'Safari'
                elif 'edge' in user_agent_lower:
                    browser = 'Edge'
                else:
                    browser = 'Unknown'
                
                # Extract OS
                if 'windows' in user_agent_lower:
                    os = 'Windows'
                elif 'mac' in user_agent_lower or 'darwin' in user_agent_lower:
                    os = 'macOS'
                elif 'linux' in user_agent_lower:
                    os = 'Linux'
                elif 'android' in user_agent_lower:
                    os = 'Android'
                elif 'ios' in user_agent_lower:
                    os = 'iOS'
                else:
                    os = 'Unknown'
                
                # Extract device type
                if 'mobile' in user_agent_lower or 'android' in user_agent_lower:
                    device = 'Mobile'
                elif 'tablet' in user_agent_lower or 'ipad' in user_agent_lower:
                    device = 'Tablet'
                else:
                    device = 'Desktop'
        
        # Serialize data to handle datetime objects
        serialized_old_data = serialize_datetime_objects(old_data) if old_data else None
        serialized_new_data = serialize_datetime_objects(new_data) if new_data else None
        
        # Use the generic create_activity method for all activity types
        activity_service.create_activity(
            talent_profile_id=talent_profile_id,
            activity_type=activity_type,
            activity=activity_description,
            old_data=serialized_old_data,
            new_data=serialized_new_data,
            ip_address=ip_address,
            user_agent=user_agent,
            browser=browser,
            os=os,
            device=device
        )
        
        return True
        
    except Exception as e:
        # Log error but don't fail the main operation
        log_error_with_context(e, {
            "talent_profile_id": talent_profile_id,
            "activity_type": activity_type,
            "activity_description": activity_description,
            "step": "track_talent_activity_sync"
        })
        return False
