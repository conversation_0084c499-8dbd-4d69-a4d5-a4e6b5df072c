from cryptography.fernet import Fernet

from app.core import settings


def encrypt(text: str) -> bytes:
    """
    Encrypts a string using Fernet symmetric encryption.

    Args:
        text (str): The plaintext string to encrypt

    Returns:
        bytes: The encrypted bytes
    """
    fernet = Fernet(settings.crypto_key)
    return fernet.encrypt(text.encode())


def decrypt(enc_message: str) -> str:
    """
    Decrypts a Fernet-encrypted message back to plaintext.

    Args:
        enc_message (str): The encrypted message to decrypt

    Returns:
        str: The decrypted plaintext string
    """
    fernet = Fernet(settings.crypto_key)
    return fernet.decrypt(enc_message).decode()
