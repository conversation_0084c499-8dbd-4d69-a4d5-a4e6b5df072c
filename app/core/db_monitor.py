"""Database connection pool monitoring utilities."""

import logging
from typing import Dict, Any
from app.db.init_db import engine

logger = logging.getLogger(__name__)


def get_pool_status() -> Dict[str, Any]:
    """Get current database connection pool status.
    
    Returns:
        Dict containing pool statistics including size, checked out connections,
        overflow, and invalid connections.
    """
    pool = engine.pool
    
    return {
        "pool_size": getattr(pool, '_pool_size', 20),
        "checked_out_connections": getattr(pool, '_checked_out', 0),
        "overflow_connections": getattr(pool, '_overflow', 0),
        "invalid_connections": getattr(pool, '_invalidated', 0),
        "total_capacity": getattr(pool, '_pool_size', 20) + getattr(pool, '_max_overflow', 10),
        "available_connections": getattr(pool, '_pool_size', 20) - getattr(pool, '_checked_out', 0),
        "pool_class": pool.__class__.__name__
    }


def log_pool_status(operation: str = "periodic_check") -> None:
    """Log current database connection pool status.
    
    Args:
        operation: The operation context for this status check
    """
    try:
        status = get_pool_status()
        logger.info(
            f"DB Pool Status [{operation}]: "
            f"Size: {status['pool_size']}, "
            f"Checked Out: {status['checked_out_connections']}, "
            f"Overflow: {status['overflow_connections']}, "
            f"Available: {status['available_connections']}, "
            f"Invalid: {status['invalid_connections']}"
        )
        
        # Log warning if pool utilization is high
        utilization = status['checked_out_connections'] / status['total_capacity']
        if utilization > 0.8:
            logger.warning(
                f"High database pool utilization: {utilization:.2%} "
                f"({status['checked_out_connections']}/{status['total_capacity']})"
            )
            
    except Exception as e:
        logger.error(f"Failed to get pool status: {e}")


def monitor_pool_on_error(error: Exception, operation: str) -> None:
    """Monitor pool status when database errors occur.
    
    Args:
        error: The database error that occurred
        operation: The operation that caused the error
    """
    if "QueuePool limit" in str(error) or "connection timed out" in str(error):
        logger.error(f"Connection pool exhaustion detected during {operation}")
        log_pool_status(f"error_{operation}")