
import json
from typing import Any
import requests

from app.core.config import settings

def gen_access_token(refresh_token: str):
    """Generate Zoho access token."""
    url = f"https://accounts.zoho.com/oauth/v2/token?refresh_token={refresh_token}&grant_type=refresh_token&client_id={settings.zoho_client_id}&client_secret={settings.zoho_client_secret}"

    req = requests.post(url)

    if 'error' in req.json():
        return req.json()
    return req.json()['access_token']


def get_organization_id(refresh_token: str):
    """Get Zoho organization ID."""

    url = "https://mail.zoho.com/api/organization"

    access_token = gen_access_token(refresh_token)
    if 'error' in access_token:
        return access_token
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    req = requests.get(url, headers=headers)
    if 'error' in req.json():
        return req.json()
    return req.json()['data']['zoid']


def get_all_org_users(refresh_token: str):
    """Get all Zoho organization users."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts?start=1&limit=1000"
    access_token = gen_access_token(refresh_token)
    if 'error' in access_token:
        return access_token
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    req = requests.get(url, headers=headers)
    if 'error' in req.json():
        return req.json()
    return req.json()['data']


def add_user_to_org(user: dict[str, str]) -> dict[str, Any]:
    """Add user to Zoho organization."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts"

    access_token = gen_access_token(settings.zoho_user_refresh_token)
    if 'error' in access_token:
        return access_token
    
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }

    data: dict[str, str | bool] = {
      "primaryEmailAddress": user['primaryEmailAddress'] + "@bposolutionsgroup.com",
      "password": "Asd@1234",
      "firstName": user['first_name'],
      "lastName": user['last_name'],
      "oneTimePassword": True,
      # "role": "member",
      # "timezone": user['timezone'],
    }
    print(json.dumps(data, indent=4))

    req = requests.post(url, headers=headers, json=data)
    print(json.dumps(req.json(), indent=4))
    if 'error' in req.json():
        return req.json()
    return req.json()['data']

def toggle_user(user: dict[str, str]):
    """Disable user in Zoho organization."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts/{user['accountId']}"

    access_token = gen_access_token(settings.zoho_organization_refresh_token)
    if 'error' in access_token:
        return access_token
    
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    data: dict[str, str | bool] = {}
    if user['status'] == 'Active':
        data = {
           "mode":"enableUser", 
           "unblockIncoming":"true",
           "zuid":user['zuid'],
        }
    else:
        data = {
          "mode":"disableUser",
          "blockIncoming":"true",
          "removeMailforward":"true",
          "removeGroupMembership":"true",
          "removeAlias":"true",
          "zuid":user['zuid'],
        }

    req = requests.put(url, headers=headers, data=data)
    print(json.dumps(req.json(), indent=4))
    if 'error' in req.json():
        return req.json()
    return req.json()['data']


def delete_user(user: list[str]):
    """Delete user in Zoho organization."""
    url = f"https://mail.zoho.com/api/organization/{settings.zoho_org_id}/accounts"

    access_token = gen_access_token(settings.zoho_organization_refresh_token)
    if 'error' in access_token:
        return access_token
    
    headers = {
        "Authorization": f"Zoho-oauthtoken {access_token}",
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    req = requests.delete(url, headers=headers, data=json.dumps(user))
    print(json.dumps(req.json(), indent=4))
    if 'error' in req.json():
        return req.json()
    return req.json()['data']
