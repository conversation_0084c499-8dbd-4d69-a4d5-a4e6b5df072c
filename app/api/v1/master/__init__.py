"""Master data API endpoints package.

This package contains API router modules for master data entities
including client information and other reference data.
"""

from .master_client_router import router as master_client_router
from .role_router import router as role_router
from .module_router import router as module_router
from .role_module_permission_mapping_router import router as role_module_permission_mapping_router
from .equipment_router import router as equipment_router

__all__ = [
    "master_client_router",
    "role_router",
    "module_router",
    "role_module_permission_mapping_router",
    "equipment_router",
]
