"""Router for master equipment management.

This module contains the FastAPI router for master equipment CRUD operations.
"""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Query, status
from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.master.equipment_schema import (
    MasterEquipmentCreate,
    MasterEquipmentUpdate
)
from app.services.master.equipment_service import EquipmentService

router = APIRouter()

service_dependency = Annotated[EquipmentService, Depends()]


@router.post(
    "/create",
    response_model=GeneralResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create new equipment",
    description="Create a new master equipment record"
)
async def create_equipment(
    equipment_data: MasterEquipmentCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new master equipment record.
    
    Args:
        equipment_data: Equipment data to create
        service: Injected equipment service
        
    Returns:
        GeneralResponse with created equipment data
    """
    try:
        service.create_equipment(equipment_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Equipment created successfully"
        )
    except Exception as e:
        log_api_error(
            endpoint="/equipment/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get(
    "/get-by-id/{equipment_id}",
    response_model=GeneralResponse,
    summary="Get equipment by ID",
    description="Retrieve a master equipment record by its ID"
)
async def get_equipment(
    equipment_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get master equipment record by ID.
    
    Args:
        equipment_id: Equipment ID to retrieve
        service: Injected equipment service
        
    Returns:
        GeneralResponse with equipment data
    """
    try:
        data = service.get_equipment_by_id(equipment_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment retrieved successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/equipment/{equipment_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get(
    "/list-all",
    response_model=GeneralResponse,
    summary="Get all equipment",
    description="Retrieve all master equipment records with pagination"
)
async def get_all_equipment(
    service: service_dependency,
    skip: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
) -> GeneralResponse:
    """Get all master equipment records with pagination.
    
    Args:
        service: Injected equipment service
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with list of equipment data
    """
    try:
        data = service.get_all_equipment(skip=skip, limit=limit)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(data)} equipment records",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/equipment/",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put(
    "/update/{equipment_id}",
    response_model=GeneralResponse,
    summary="Update equipment",
    description="Update an existing master equipment record"
)
async def update_equipment(
    equipment_id: int,
    equipment_data: MasterEquipmentUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing master equipment record.
    
    Args:
        equipment_id: Equipment ID to update
        equipment_data: Updated equipment data
        service: Injected equipment service
        
    Returns:
        GeneralResponse with updated equipment data
    """
    try:
        data = service.update_equipment(equipment_id, equipment_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment updated successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/equipment/{equipment_id}",
            method="PUT",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
