"""Router for role module permission mapping management.

This module contains the FastAPI router for role module permission mapping endpoints.
"""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from app.response_models.general_response import GeneralResponse
from app.services.master.role_module_permission_mapping_service import RoleModulePermissionMappingService
from app.schemas.master.role_module_permission_mapping_schema import (
    RoleModulePermissionMappingCreate,
    RoleModulePermissionMappingUpdate
)
from app.core.logs import log_api_error

router = APIRouter(prefix="/role-module-permission-mapping", tags=["Role Module Permission Mapping"])

__service = Annotated[RoleModulePermissionMappingService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_mapping(
    request: Request,
    mapping_data: RoleModulePermissionMappingCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new role module permission mapping.
    
    Args:
        request: FastAPI request object
        mapping_data: Mapping data to create
        service: Mapping service dependency
        
    Returns:
        GeneralResponse with created mapping data
    """
    try:
        mapping = service.create_mapping(mapping_data)
        return GeneralResponse(
            message="Mapping created successfully",
            status_code=status.HTTP_201_CREATED,
            data=mapping.model_dump()
        )
    except HTTPException as e:
        log_api_error(
            endpoint=str(request.url),
            method=request.method,
            status_code=e.status_code,
            error=str(e.detail),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise
    except Exception as e:
        log_api_error(
            endpoint=str(request.url),
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_mappings(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all role module permission mappings with pagination.
    
    Args:
        request: FastAPI request object
        service: Mapping service dependency
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with list of mappings
    """
    try:
        mappings = service.get_all_mappings(skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            message="Mappings retrieved successfully",
            status_code=status.HTTP_200_OK,
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/{mapping_id}", response_model=GeneralResponse)
async def get_mapping_by_id(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Get a role module permission mapping by its ID.
    
    Args:
        request: FastAPI request object
        mapping_id: ID of the mapping
        service: Mapping service dependency
        
    Returns:
        GeneralResponse with mapping data
    """
    try:
        mapping = service.get_mapping_by_id(mapping_id)
        return GeneralResponse(
            message="Mapping retrieved successfully",
            status_code=status.HTTP_200_OK,
            data=mapping.model_dump()
        )
    except HTTPException as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=e.status_code,
            error=str(e.detail),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/role/{role_id}", response_model=GeneralResponse)
async def get_mappings_by_role_id(
    request: Request,
    role_id: int,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get role module permission mappings by role ID.
    
    Args:
        request: FastAPI request object
        role_id: ID of the role
        service: Mapping service dependency
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with list of mappings for the role
    """
    try:
        mappings = service.get_mappings_by_role_id(role_id, skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            message="Mappings retrieved successfully",
            status_code=status.HTTP_200_OK,
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/module/{module_id}", response_model=GeneralResponse)
async def get_mappings_by_module_id(
    request: Request,
    module_id: int,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get role module permission mappings by module ID.
    
    Args:
        request: FastAPI request object
        module_id: ID of the module
        service: Mapping service dependency
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with list of mappings for the module
    """
    try:
        mappings = service.get_mappings_by_module_id(module_id, skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            message="Mappings retrieved successfully",
            status_code=status.HTTP_200_OK,
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/role/{role_id}/module/{module_id}", response_model=GeneralResponse)
async def get_mapping_by_role_and_module(
    request: Request,
    role_id: int,
    module_id: int,
    service: __service,
) -> GeneralResponse:
    """Get role module permission mapping by role ID and module ID.
    
    Args:
        request: FastAPI request object
        role_id: ID of the role
        module_id: ID of the module
        service: Mapping service dependency
        
    Returns:
        GeneralResponse with mapping data or 404 if not found
    """
    try:
        mapping = service.get_mapping_by_role_and_module(role_id, module_id)
        if not mapping:
            return GeneralResponse(
                message="Mapping not found",
                status_code=status.HTTP_404_NOT_FOUND,
                data=None
            )
        return GeneralResponse(
            message="Mapping retrieved successfully",
            status_code=status.HTTP_200_OK,
            data=mapping.model_dump()
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/{mapping_id}", response_model=GeneralResponse)
async def update_mapping(
    request: Request,
    mapping_id: int,
    mapping_data: RoleModulePermissionMappingUpdate,
    service: __service,
) -> GeneralResponse:
    """Update an existing role module permission mapping.
    
    Args:
        request: FastAPI request object
        mapping_id: ID of the mapping to update
        mapping_data: Updated mapping data
        service: Mapping service dependency
        
    Returns:
        GeneralResponse with updated mapping data
    """
    try:
        mapping = service.update_mapping(mapping_id, mapping_data)
        return GeneralResponse(
            message="Mapping updated successfully",
            status_code=status.HTTP_200_OK,
            data=mapping.model_dump()
        )
    except HTTPException as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=e.status_code,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/{mapping_id}", response_model=GeneralResponse)
async def delete_mapping(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete a role module permission mapping.
    
    Args:
        request: FastAPI request object
        mapping_id: ID of the mapping to delete
        service: Mapping service dependency
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_mapping(mapping_id)
        return GeneralResponse(
            message="Mapping deleted successfully",
            status_code=status.HTTP_200_OK,
            data=None
        )
    except HTTPException as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=e.status_code,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
