"""Router for master role management.

This module contains the FastAPI router for master role endpoints.
"""

from typing import List, Annotated
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from app.response_models.general_response import GeneralResponse
from app.services.master.role_service import RoleService
from app.schemas.master.role_schema import (
    MasterRoleCreate,
    MasterRoleUpdate,
    MasterRoleResponse,
    RoleWithPermissionsResponse
)
from app.core.logs import log_api_error

router = APIRouter()


@router.post(
    "/create",
    response_model=GeneralResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create new role with permissions",
    description="Create a new master role record with module permissions"
)
async def create_role(
    request: Request,
    role_data: MasterRoleCreate,
    role_service: Annotated[RoleService, Depends()]
):
    """Create a new master role record with module permissions.
    
    Args:
        role_data: Role data including module permissions to create
        role_service: Role service dependency
        
    Returns:
        Created role response
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        role_service.create_role_with_permissions(role_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Role created successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error creating role with permissions: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/get/{role_id}",
    response_model=MasterRoleResponse,
    summary="Get role by ID",
    description="Retrieve a specific role record by its ID"
)
async def get_role(
    request: Request,
    role_id: int,
    role_service: Annotated[RoleService, Depends()]
) -> MasterRoleResponse:
    """Get role by ID.
    
    Args:
        role_id: Role ID
        role_service: Role service dependency
        
    Returns:
        Role response
        
    Raises:
        HTTPException: If role not found
    """
    try:
        return role_service.get_role_by_id(role_id)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting role: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/get-with-permissions/{role_id}",
    response_model=RoleWithPermissionsResponse,
    summary="Get role with module permissions",
    description="Retrieve a specific role with its associated module permissions"
)
async def get_role_with_permissions(
    request: Request,
    role_id: int,
    role_service: Annotated[RoleService, Depends()]
) -> RoleWithPermissionsResponse:
    """Get role with module permissions.
    
    Args:
        role_id: Role ID
        role_service: Role service dependency
        
    Returns:
        Role response with module permissions
        
    Raises:
        HTTPException: If role not found or server error
    """
    try:
        role_data = role_service.get_role_with_permissions(role_id)
        return RoleWithPermissionsResponse(**role_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting role with permissions: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/list",
    response_model=List[MasterRoleResponse],
    summary="Get all roles",
    description="Retrieve all role records with pagination"
)
async def get_all_roles(
    request: Request,
    role_service: Annotated[RoleService, Depends()],
    skip: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100
) -> List[MasterRoleResponse]:
    """Get all role records with pagination.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        role_service: Role service dependency
        
    Returns:
        List of role responses
    """
    try:
        return role_service.get_all_roles(skip=skip, limit=limit)
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting role list: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put(
    "/update/{role_id}",
    response_model=MasterRoleResponse,
    summary="Update role with permissions",
    description="Update an existing role record with module permissions"
)
async def update_role(
    request: Request,
    role_id: int,
    role_data: MasterRoleUpdate,
    role_service: Annotated[RoleService, Depends()]
) -> MasterRoleResponse:
    """Update an existing role record with module permissions.
    
    Args:
        role_id: Role ID to update (can be overridden by roleId in request body)
        role_data: Updated role data including module permissions
        role_service: Role service dependency
        
    Returns:
        Updated role response
        
    Raises:
        HTTPException: If role not found or update fails
    """
    try:
        return role_service.update_role_with_permissions(role_id, role_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error updating role with permissions: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
