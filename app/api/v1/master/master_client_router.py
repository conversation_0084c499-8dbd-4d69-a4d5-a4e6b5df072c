"""Master Client API router."""
from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from app.services.master.master_client_service import MasterClientService
from app.schemas.master.master_client_schema import (
    MasterClientCreate,
    MasterClientUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[MasterClientService, Depends()]


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_master_client(
    request: Request,
    client_data: MasterClientCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new master client."""
    try:
        service.create_client(client_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Master client created successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while creating master client"
        )


@router.get("/list", response_model=GeneralResponse)
async def get_all_master_clients(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all master clients with pagination."""
    try:
        clients = service.get_all_clients(skip=skip, limit=limit)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Master clients retrieved successfully",
            data=clients
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving master clients"
        )


@router.get("/detail/{client_id}", response_model=GeneralResponse)
async def get_client_detail(
    request: Request,
    client_id: int,
    service: __service,
):
  """
  Get client detail by id
  """
  try:
    client = service.get_client_by_id(client_id)
    if not client:
      raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Client not found"
      )
    return GeneralResponse(
      status_code=status.HTTP_200_OK,
      message="Client detail retrieved successfully",
      data=client
    )
  except Exception as e:
    log_api_error(
      endpoint=request.url.path,
      method=request.method,
      status_code=500,
      error=str(e),
      user_id=str(service.current_user.id),
    )
    raise HTTPException(
      status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
      detail="Internal server error occurred while retrieving client detail"
    )

@router.get('/assigned-employees/{client_id}', response_model=GeneralResponse)
async def get_assigned_employees(
    request: Request,
    client_id: int,
    service: __service,
):
  """
  Get assigned employees by client id
  """
  try:
    employees = service.get_assigned_employees(client_id)
    return GeneralResponse(
      status_code=status.HTTP_200_OK,
      message="Assigned employees retrieved successfully",
      data=employees
    )
  except Exception as e:
    log_api_error(
      endpoint=request.url.path,
      method=request.method,
      status_code=500,
      error=str(e),
      user_id=str(service.current_user.id),
    )
    raise HTTPException(
      status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
      detail="Internal server error occurred while retrieving assigned employees"
    )

@router.put("/update/{client_id}", response_model=GeneralResponse)
async def update_master_client(
    request: Request,
    client_id: int,
    client_data: MasterClientUpdate,
    service: __service,
) -> GeneralResponse:
    """Update a master client."""
    try:
        service.update_client(client_id, client_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Master client updated successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while updating master client"
        )
