"""Health check router for monitoring application status."""

from fastapi import APIRouter, HTTPException
from typing import List
from app.core.db_monitor import get_pool_status, log_pool_status
from app.response_models.general_response import GeneralResponse

router = APIRouter()


@router.get("/", response_model=GeneralResponse)
async def health_check() -> GeneralResponse:
    """Basic health check endpoint.
    
    Returns:
        GeneralResponse: Basic health status
    """
    return GeneralResponse(
        message="Application is healthy",
        status_code=200,
        data={"status": "healthy"}
    )


@router.get("/db-pool", response_model=GeneralResponse)
async def database_pool_status() -> GeneralResponse:
    """Get database connection pool status.
    
    Returns:
        GeneralResponse: Database pool statistics
    """
    try:
        pool_status = get_pool_status()
        log_pool_status("health_check_endpoint")
        
        # Determine pool health
        utilization = pool_status['checked_out_connections'] / pool_status['total_capacity']
        health_status = "healthy"
        
        if utilization > 0.9:
            health_status = "critical"
        elif utilization > 0.7:
            health_status = "warning"
            
        return GeneralResponse(
            message=f"Database pool status: {health_status}",
            status_code=200,
            data={
                **pool_status,
                "utilization_percentage": round(utilization * 100, 2),
                "health_status": health_status
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get database pool status: {str(e)}"
        )


@router.get("/db-pool/detailed", response_model=GeneralResponse)
async def database_pool_detailed_status() -> GeneralResponse:
    """Get detailed database connection pool status with recommendations.
    
    Returns:
        GeneralResponse: Detailed database pool statistics with recommendations
    """
    try:
        pool_status = get_pool_status()
        log_pool_status("detailed_health_check_endpoint")
        
        utilization = pool_status['checked_out_connections'] / pool_status['total_capacity']
        
        # Generate recommendations based on pool status
        recommendations: List[str] = []
        
        if utilization > 0.8:
            recommendations.append("High pool utilization detected. Consider increasing pool_size or max_overflow.")
            
        if pool_status['invalid_connections'] > 0:
            recommendations.append(f"Found {pool_status['invalid_connections']} invalid connections. Check database connectivity.")
            
        if pool_status['overflow_connections'] > 0:
            recommendations.append(f"Using {pool_status['overflow_connections']} overflow connections. Monitor for sustained high load.")
            
        if utilization < 0.1:
            recommendations.append("Low pool utilization. Pool size might be over-provisioned.")
            
        return GeneralResponse(
            message="Detailed database pool analysis",
            status_code=200,
            data={
                "pool_statistics": pool_status,
                "utilization_percentage": round(utilization * 100, 2),
                "health_status": "critical" if utilization > 0.9 else "warning" if utilization > 0.7 else "healthy",
                "recommendations": recommendations,
                "pool_configuration": {
                    "base_pool_size": pool_status['pool_size'],
                    "max_overflow": pool_status['total_capacity'] - pool_status['pool_size'],
                    "total_capacity": pool_status['total_capacity']
                }
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get detailed database pool status: {str(e)}"
        )