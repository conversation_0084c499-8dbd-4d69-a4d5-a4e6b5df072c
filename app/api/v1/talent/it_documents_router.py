"""API router for talent IT documents information management."""

from typing import Annotated, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, UploadFile, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentITDocumentsResponse,
)
from app.services.talent.it_documents_service import ITDocumentsService

router = APIRouter()

service_dependency = Annotated[ITDocumentsService, Depends()]

@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_it_document(
    request: Request,
    talent_profile_id: Annotated[int, Form()],
    document_type: Annotated[str, Form()],
    service: service_dependency,
    file: Annotated[UploadFile, File()],
    notes: Annotated[Optional[str], Form()] = None,
):
    """Create a new IT document record.
    
    Args:
        talent_profile_id: The talent profile ID
        document_type: The IT document type
        service: IT documents service instance
        
    Returns:
        GeneralResponse with created IT document data
    """
    try:
        service.create_it_document(talent_profile_id, document_type, file, notes)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="IT document created successfully",
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create IT document: {str(e)}"
        )


@router.get("/get-all/{talent_id}", response_model=GeneralResponse)
async def get_it_documents_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all IT documents for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: IT documents service instance
        
    Returns:
        GeneralResponse with list of IT document data
    """
    try:
        it_documents = service.get_it_documents_by_talent_id(talent_id)
        
        if not it_documents:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="No IT documents found for this talent",
                data=[]
            )
        
        # Convert to response models
        response_data = [
            TalentITDocumentsResponse.model_validate(doc.model_dump())
            for doc in it_documents
        ]
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="IT documents retrieved successfully",
            data=response_data
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get IT documents: {str(e)}",
        )


@router.get("/get-by-id/{it_document_id}", response_model=GeneralResponse)
async def get_it_document_by_id(
    request: Request,
    it_document_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get IT document by ID.
    
    Args:
        it_document_id: The IT document ID
        service: IT documents service instance
        
    Returns:
        GeneralResponse with IT document data
    """
    try:
        it_document = service.get_it_document_by_id(it_document_id)
        
        if not it_document:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="IT document not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="IT document retrieved successfully",
            data=TalentITDocumentsResponse.model_validate(it_document.model_dump())
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get IT document: {str(e)}",
        )
