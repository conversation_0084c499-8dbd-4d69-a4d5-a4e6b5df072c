"""API router for talent allergies management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent.allergies_schema import AllergiesCreate
from app.services.talent import TalentAllergiesService

router = APIRouter()

service_dependency = Annotated[TalentAllergiesService, Depends()]

@router.get("/get/{talent_id}", response_model=GeneralResponse)
async def get_allergies_by_talent(
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all allergies for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected allergies service
        
    Returns:
        GeneralResponse with list of allergies
    """
    try:
        data = service.get_allergy_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Allergies retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=f"/allergies/list/{talent_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/create-or-update", response_model=GeneralResponse)
async def update_allergy(
    allergy_data: AllergiesCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing allergy.
    
    Args:
        allergy_data: The updated allergy data
        service: Injected allergies service
        
    Returns:
        GeneralResponse with updated allergy data
    """
    try:
        data = service.update_or_create_allergy(allergy_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Allergy updated successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/allergies/create-or-update",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
