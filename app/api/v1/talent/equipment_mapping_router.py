"""Talent Equipment Mapping API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.services.talent.equipment_mapping_service import TalentEquipmentMappingService
from app.schemas.talent.equipment_mapping_schema import (
    TalentEquipmentMappingCreate,
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[TalentEquipmentMappingService, Depends()]


@router.get('/get-available', response_model=GeneralResponse)
async def get_available_equipment(
    request: Request,
    service: __service,
) -> GeneralResponse:
    """Get list of available master equipment."""
    try:
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Available equipment retrieved successfully",
            data=service.get_available_equipment()
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise e


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_equipment_mapping(
    request: Request,
    mapping_data: TalentEquipmentMappingCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new talent equipment mapping."""
    try:
        service.create_equipment_mapping(mapping_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Equipment mapping created successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.get("/get-by-talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_equipment_mappings_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get equipment mappings for a specific talent."""
    try:
        mappings = service.get_equipment_mappings_by_talent_id(talent_profile_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mappings for talent retrieved successfully",
            data=mappings
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.patch("/update/{mapping_id}", response_model=GeneralResponse)
async def update_equipment_mapping(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Update equipment mapping information."""
    try:
        service.update_equipment_mapping(mapping_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mapping updated successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
