"""API router for talent chronic conditions management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent.chronic_conditions_schema import Chronic<PERSON>ondition<PERSON>reate
from app.services.talent import TalentChronicConditionsService

router = APIRouter()

service_dependency = Annotated[TalentChronicConditionsService, Depends()]

@router.post("/create-or-update", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_chronic_condition(
    chronic_condition: ChronicConditionCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new chronic condition for a talent.
    
    Args:
        chronic_condition: The chronic condition data to create
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with created chronic condition data
    """
    try:
        data = service.create_or_update_chronic_condition(chronic_condition)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Chronic condition created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/chronic-conditions/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_id}", response_model=GeneralResponse)
async def get_chronic_conditions_by_talent(
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all chronic conditions for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with list of chronic conditions
    """
    try:
        data = service.get_chronic_conditions_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Chronic conditions retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=f"/chronic-conditions/talent/{talent_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

