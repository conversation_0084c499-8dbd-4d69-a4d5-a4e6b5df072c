"""API router for talent client information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentClientInfoCreate
)
from app.services.talent.client_info_service import TalentClientInfoService

router = APIRouter()

service_dependency = Annotated[TalentClientInfoService, Depends()]


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_client_info(
    request: Request,
    client_info_data: TalentClientInfoCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new client information record for a talent.
    
    Args:
        request: FastAPI request object
        client_info_data: The client information data to create
        service: Injected client info service
        
    Returns:
        GeneralResponse with created client information data
    """
    try:
        service.create_client_info(client_info_data)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Client information created successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-talent-info/{talent_id}", response_model=GeneralResponse)
async def get_client_info_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get client information for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected client info service
        
    Returns:
        GeneralResponse with client information data
    """
    try:
        client_info = service.get_client_info_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information retrieved successfully",
            data=client_info
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.put("/update/{client_info_id}", response_model=GeneralResponse)
async def update_client_info(
    request: Request,
    client_info_id: int,
    client_info_data: TalentClientInfoCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Update client information for a specific talent.
    
    Args:
        request: FastAPI request object
        client_info_id: The client information ID
        client_info_data: The client information data to update
        service: Injected client info service
        
    Returns:
        GeneralResponse with updated client information data
    """
    try:
        service.update_client_info(client_info_id, client_info_data)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information updated successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
