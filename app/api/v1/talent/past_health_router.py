"""API router for talent past health issues management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent.past_health_schema import PastHealthIssueCreate
from app.services.talent import TalentPastHealthService

router = APIRouter()

service_dependency = Annotated[TalentPastHealthService, Depends()]

@router.post("/create-or-update", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_past_health_issue(
    past_health_issue: PastHealthIssueCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new past health issue for a talent.
    
    Args:
        past_health_issue: The past health issue data to create
        service: Injected past health service
        
    Returns:
        GeneralResponse with created past health issue data
    """
    try:
        data = service.create_or_update_past_health_issue(past_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Past health issue created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/past-health/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_id}", response_model=GeneralResponse)
async def get_past_health_issues_by_talent(
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all past health issues for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected past health service
        
    Returns:
        GeneralResponse with list of past health issues
    """
    try:
        data = service.get_past_health_issues_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Past health issues retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/past-health/talent/",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
