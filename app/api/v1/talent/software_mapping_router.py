"""Talent Software Mapping API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.services.talent.software_mapping_service import TalentSoftwareMappingService
from app.schemas.talent.software_mapping_schema import (
    TalentSoftwareMappingCreate,
    TalentSoftwareMappingUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[TalentSoftwareMappingService, Depends()]


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_software_mapping(
    request: Request,
    mapping_data: TalentSoftwareMappingCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new software mapping."""
    try:
        service.create_software_mapping(mapping_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Software mapping created successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_software_mappings_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get all software mappings for a specific talent profile."""
    try:
        mappings = service.get_software_mappings_by_talent(talent_profile_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings retrieved successfully",
            data=mappings
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.put("/update/{mapping_id}", response_model=GeneralResponse)
async def update_software_mapping(
    request: Request,
    mapping_id: int,
    mapping_data: TalentSoftwareMappingUpdate,
    service: __service,
) -> GeneralResponse:
    """Update software mapping information."""
    try:
        service.update_software_mapping(mapping_id, mapping_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping updated successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
