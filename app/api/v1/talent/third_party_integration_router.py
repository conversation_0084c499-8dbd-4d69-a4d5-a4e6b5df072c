"""Talent Third Party Integration API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status
from app.services.talent.third_party_integration_service import TalentThirdPartyIntegrationService
from app.schemas.talent.third_party_integration_schema import (
    TalentThirdPartyIntegrationCreate,
    TalentThirdPartyIntegrationUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[TalentThirdPartyIntegrationService, Depends()]




@router.get("/get-by-talent/{talent_profile_id}", response_model=GeneralResponse)
def get_third_party_integrations_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get all third party integrations for a specific talent profile."""
    try:
        integrations = service.get_third_party_integrations_by_talent(talent_profile_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Third party integrations retrieved successfully",
            data=integrations
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
def create_third_party_integration(
    request: Request,
    integration_data: TalentThirdPartyIntegrationCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new third party integration."""
    try:
        service.create_third_party_integration(integration_data, request)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Third party integration created successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/update/{integration_id}", response_model=GeneralResponse)
def update_third_party_integration(
    request: Request,
    integration_id: int,
    integration_data: TalentThirdPartyIntegrationUpdate,
    service: __service,
) -> GeneralResponse:
    """Update third party integration information."""
    try:
        service.update_third_party_integration(integration_id, integration_data, request)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Third party integration updated successfully",
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
