"""API router for talent ongoing health issues management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent.ongoing_health_schema import OngoingHealthIssueCreate
from app.services.talent import TalentOngoingHealthService

router = APIRouter()

service_dependency = Annotated[TalentOngoingHealthService, Depends()]

@router.post("/create-or-update", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_ongoing_health_issue(
    ongoing_health_issue: OngoingHealthIssueCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new ongoing health issue for a talent.
    
    Args:
        ongoing_health_issue: The ongoing health issue data to create
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with created ongoing health issue data
    """
    try:
        data = service.create_or_update_ongoing_health_issue(ongoing_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Ongoing health issue created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/ongoing-health/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_id}", response_model=GeneralResponse)
async def get_ongoing_health_issues_by_talent(
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all ongoing health issues for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with list of ongoing health issues
    """
    try:
        data = service.get_ongoing_health_issues_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Ongoing health issues retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/ongoing-health/talent/",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
