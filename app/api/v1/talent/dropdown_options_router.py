"""Skills router for talent management API.

This module contains the FastAPI router for skills endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.services.talent.dropdown_options_service import DropdownOptionsService

router = APIRouter()

service_dependency = Annotated[DropdownOptionsService, Depends()]


@router.get("/skills", response_model=GeneralResponse, status_code=status.HTTP_200_OK)
async def get_unique_skills(
    request: Request,
    service: service_dependency,
):
    """Get all unique skills from talent skill set mappings.
    
    This endpoint retrieves all unique skills that have been recorded
    in talent skill set mappings across all talent profiles.
    
    Args:
        request: FastAPI request object
        service: Injected skills service
        
    Returns:
        GeneralResponse with list of unique skills
    """
    try:
        unique_skills = service.get_unique_skills()

        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Unique skills retrieved successfully",
            data={"skills": unique_skills, "count": len(unique_skills)}
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=getattr(service.current_user, 'id', None) if hasattr(service, 'current_user') else None
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve unique skills"
        )
