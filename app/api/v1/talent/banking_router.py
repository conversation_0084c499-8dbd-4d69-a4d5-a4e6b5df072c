"""API router for talent banking information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
)
from app.services.talent import BankingService

router = APIRouter()

service_dependency = Annotated[BankingService, Depends()]


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_banking_information(
    request: Request,
    banking_data: TalentBankingInformationCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new banking information record for a talent.
    
    Args:
        request: FastAPI request object
        banking_data: The banking information data to create
        service: Injected banking service
        
    Returns:
        GeneralResponse with created banking information data
    """
    try:
        banking_info = service.create_banking_information(banking_data)
        
        if not banking_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create banking information. Banking information may already exist for this talent and account."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Banking information created successfully",
            data=banking_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_id}", response_model=GeneralResponse)
async def get_banking_information_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get banking information for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected banking service
        
    Returns:
        GeneralResponse with banking information data or None if not found
    """
    try:
        banking_info = service.get_banking_information_by_talent_id(talent_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Banking information retrieved successfully",
            data=banking_info.model_dump() if banking_info else None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.put("/update/{banking_id}", response_model=GeneralResponse)
async def update_banking_information(
    request: Request,
    banking_id: int,
    banking_data: TalentBankingInformationUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing banking information record.
    
    Args:
        request: FastAPI request object
        banking_id: The banking information ID to update
        banking_data: The updated banking information data
        service: Injected banking service
        
    Returns:
        GeneralResponse with updated banking information data
    """
    try:
        banking_info = service.update_banking_information(banking_id, banking_data)
        
        if not banking_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Banking information not found or update failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Banking information updated successfully",
            data=banking_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
