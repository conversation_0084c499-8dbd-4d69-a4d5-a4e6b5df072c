"""API router for talent profile management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status, UploadFile, File
from sqlmodel import Session

from app.core import log_api_error
from app.core.jwt import get_current_user
from app.db.session import get_session
from app.db.models import User
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import ProfileCreate, ProfileUpdate, DeactivateTalent
from app.services.talent import TalentService

router = APIRouter()

def get_talent_service(
    db: Annotated[Session, Depends(get_session)],
    current_user: Annotated[User, Depends(get_current_user)]
) -> TalentService:
    """Create TalentService instance with proper dependencies."""
    return TalentService(db=db, current_user=current_user)

service_dependency = Annotated[TalentService, Depends(get_talent_service)]


@router.post("/create", status_code=status.HTTP_201_CREATED, response_model=GeneralResponse)
async def create_talent_profile(
    request: Request,
    talent_profile: ProfileCreate,
    service: service_dependency,
):
    """Create a new talent profile.
    
    Args:
        talent_profile: The talent profile data to create
        service: Injected talent service
        
    Returns:
        dict: A dictionary containing status, message, and data
    """
    try:
        talent = service.create_talent_profile_from_schema(talent_profile)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Talent profile created successfully",
            data=talent
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        return GeneralResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message="Internal server error",
        )


@router.get("/all", response_model=GeneralResponse)
async def get_all_talent_profiles(
    request: Request,
    service: service_dependency,
) -> GeneralResponse:
    """Get all talent profiles.
    
    Args:
        service: Injected talent service
        
    Returns:
        GeneralResponse with list of talent profiles
    """
    try:
        talents = service.get_all_talents()
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profiles retrieved successfully",
            data=talents
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/upload-profile-picture/{talent_id}", status_code=status.HTTP_200_OK, response_model=GeneralResponse)
async def upload_profile_picture(
    request: Request,
    talent_id: int,
    service: service_dependency,
    file: UploadFile = File(...),
) -> GeneralResponse:
    """Upload a profile picture for a talent.
    
    Args:
        talent_id: The talent profile ID
        file: The uploaded image file
        service: Injected talent service
        
    Returns:
        GeneralResponse with updated talent profile data
    """
    try:
        updated_talent = service.upload_profile_picture(talent_id, file)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Profile picture uploaded successfully",
            data=updated_talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/update-profile-picture/{talent_id}", status_code=status.HTTP_200_OK, response_model=GeneralResponse)
async def update_profile_picture(
    request: Request,
    talent_id: int,
    service: service_dependency,
    file: UploadFile = File(...),
) -> GeneralResponse:
    """Update the profile picture for a talent.
    
    Args:
        talent_id: The talent profile ID
        file: The new uploaded image file
        service: Injected talent service
        
    Returns:
        GeneralResponse with updated talent profile data
    """
    try:
        updated_talent = service.update_profile_picture(talent_id, file)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Profile picture updated successfully",
            data=updated_talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/detail/{talent_id}", response_model=GeneralResponse)
async def get_talent_profile_by_id(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific talent profile by ID.
    
    Args:
        talent_id: The talent profile ID
        service: Injected talent service
        
    Returns:
        GeneralResponse with talent profile data
    """
    try:
        talent = service.get_talent_by_id(talent_id)
        if talent is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile retrieved successfully",
            data=talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/update/{talent_id}", response_model=GeneralResponse)
async def update_talent_profile(
    request: Request,
    talent_id: int,
    talent_profile: ProfileUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing talent profile.
    
    Args:
        talent_id: The talent profile ID to update
        talent_profile: The updated talent profile data
        service: Injected talent service
        
    Returns:
        GeneralResponse with updated talent profile data
    """
    try:
        updated_talent = service.update_talent_profile_from_schema(talent_id, talent_profile)
        if updated_talent is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile updated successfully",
            data=updated_talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/delete/{talent_id}", response_model=GeneralResponse)
async def delete_talent_profile(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a talent profile.
    
    Args:
        talent_id: The talent profile ID to delete
        service: Injected talent service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_talent_profile(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile deleted successfully",
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.put("/deactivate-talent/{talent_id}", response_model=GeneralResponse)
async def deactivate_talent_profile(
    request: Request,
    deactivate_talent: DeactivateTalent,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Deactivate a talent profile.
    
    Args:
        talent_id: The talent profile ID to deactivate
        service: Injected talent service
        
    Returns:
        GeneralResponse confirming deactivation
    """
    try:
        service.deactivate_talent_profile(talent_id, deactivate_talent)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile deactivated successfully",
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

