"""API router for talent documents management."""

from typing import Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.services.talent import TalentDocumentsService

router = APIRouter()


@router.get("/list/{talent_id}", response_model=GeneralResponse)
async def get_talent_documents(
    talent_id: int,
    service: Annotated[TalentDocumentsService, Depends()],
    doc_type: Optional[str] = Query(None, description="Filter by document type"),
) -> GeneralResponse:
    """Get all documents for a specific talent, optionally filtered by document type.
    
    Args:
        talent_id: The talent profile ID
        service: Injected documents service
        doc_type: Optional document type filter
        
    Returns:
        GeneralResponse with list of documents
    """
    try:
        if doc_type:
            documents = service.get_documents_by_type(talent_id, doc_type)
        else:
            documents = service.get_talent_documents(talent_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Documents retrieved successfully",
            data=documents,
        )
    except Exception as e:
        log_api_error(
            endpoint="/documents/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-details/{document_id}", response_model=GeneralResponse)
async def get_document_by_id(
    document_id: int,
    service: Annotated[TalentDocumentsService, Depends()],
) -> GeneralResponse:
    """Get a specific document by ID.
    
    Args:
        document_id: The document ID
        service: Injected documents service
        
    Returns:
        GeneralResponse with document data
    """
    try:
        document = service.get_document_by_id(document_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Document retrieved successfully",
            data=document,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/documents/talent/{document_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_talent_document(
    talent_profile_id: Annotated[int, Form()],
    doc_type: Annotated[str, Form()],
    file: Annotated[UploadFile, File()],
    service: Annotated[TalentDocumentsService, Depends()],
) -> GeneralResponse:
    """Create a new talent document with file upload.
    
    Args:
        talent_profile_id: The talent profile ID
        doc_type: The document type
        file: The uploaded file
        service: Injected documents service
        
    Returns:
        GeneralResponse with created document data
    """
    try:
        document = service.create_talent_document(
            talent_id=talent_profile_id,
            doc_type=doc_type,
            file=file
        )
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Document created successfully",
            data=document,
        )
    except Exception as e:
        log_api_error(
            endpoint=f"/documents/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
