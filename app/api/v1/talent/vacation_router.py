"""API router for talent vacation mapping management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentVacationMappingCreate,
)
from app.services.talent import VacationService

router = APIRouter()

service_dependency = Annotated[VacationService, Depends()]


@router.post("/create-or-update", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_vacation_mapping(
    request: Request,
    vacation_data: TalentVacationMappingCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new vacation mapping record for a talent.
    
    Args:
        request: FastAPI request object
        vacation_data: The vacation mapping data to create
        service: Injected vacation service
        
    Returns:
        GeneralResponse with created vacation mapping data
    """
    try:
        vacation_mapping = service.create_or_update_vacation_mapping(vacation_data)
        if not vacation_mapping:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create or update vacation mapping. Vacation mapping may already exist for this talent and year."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Vacation mapping created successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-vacation-mapping/{talent_id}", response_model=GeneralResponse)
async def get_vacation_mapping_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get vacation mappings for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected vacation service
        
    Returns:
        GeneralResponse with vacation mapping data
    """
    try:
        vacation_mapping = service.get_vacation_mapping_by_id(talent_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mapping retrieved successfully",
            data=vacation_mapping.model_dump() if vacation_mapping else None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
