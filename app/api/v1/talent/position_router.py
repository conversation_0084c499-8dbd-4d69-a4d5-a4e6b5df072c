"""Position router for talent management API.

This module contains the FastAPI router for talent position endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentPositionCreate
)
from app.services.talent.position_service import TalentPositionService


router = APIRouter()

service_dependency = Annotated[TalentPositionService, Depends()]

@router.post("/create-or-update", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_or_update_position(
    request: Request,
    position_data: TalentPositionCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new position record for a talent.
    
    Args:
        request: FastAPI request object
        position_data: The position data to create
        service: Injected position service
        
    Returns:
        GeneralResponse with created position data
    """
    try:
        service.create_position(position_data)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Position created successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_positions_by_talent(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all positions for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected position service
        
    Returns:
        GeneralResponse with list of positions for the talent
    """
    try:
        positions = service.get_positions_by_talent_id(talent_profile_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved position for talent {talent_profile_id}",
            data=positions.model_dump() if positions else None
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-id/{position_id}", response_model=GeneralResponse)
async def get_position_by_id(
    request: Request,
    position_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a talent position by ID.
    
    Args:
        request: FastAPI request object
        position_id: Position ID
        service: Injected position service
        
    Returns:
        GeneralResponse with position data
        
    Raises:
        HTTPException: If position not found
    """
    try:
        position = service.get_position_by_id(position_id)
        if not position:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent position with ID {position_id} not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent position retrieved successfully",
            data=position.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
