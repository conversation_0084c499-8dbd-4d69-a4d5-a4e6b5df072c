"""Activity router for talent management API.

This module contains the FastAPI router for talent activity endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlmodel import Session

from app.core import log_api_error
from app.core.jwt import get_current_user
from app.db.session import get_session
from app.db.models import User
from app.repositories.master.role_repository import RoleRepository
from app.repositories.talent.activity_repository import TalentActivityRepository
from app.repositories.user_repository import UserRepository
from app.response_models.general_response import GeneralResponse
from app.services.talent.activity_service import TalentActivityService

router = APIRouter()

def get_activity_service(
    db: Annotated[Session, Depends(get_session)],
    current_user: Annotated[User, Depends(get_current_user)]
) -> TalentActivityService:
    """Create TalentActivityService instance with proper dependencies."""
    repository = TalentActivityRepository(db)
    user_repository = UserRepository(db)
    role_repository = RoleRepository(db)
    return TalentActivityService(repository=repository, user_repository=user_repository, role_repository=role_repository, current_user=current_user)

service_dependency = Annotated[TalentActivityService, Depends(get_activity_service)]

@router.get("/get-by-talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_activities_by_talent(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all activities for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected activity service
        skip: Number of records to skip for pagination
        limit: Number of records to return
        
    Returns:
        GeneralResponse with list of activities for the talent
    """
    try:
        result = service.get_formatted_activities_by_talent_id(
            talent_profile_id=talent_profile_id,
            skip=skip,
            limit=limit
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-user/{user_id}", response_model=GeneralResponse)
async def get_activities_by_user(
    request: Request,
    user_id: int,
    service: service_dependency,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all activities for a specific user.
    
    Args:
        request: FastAPI request object
        user_id: User ID
        service: Injected activity service
        skip: Number of records to skip for pagination
        limit: Number of records to return
        
    Returns:
        GeneralResponse with list of activities for the user
    """
    try:
        result = service.get_activities_by_user_id(
            user_id=user_id,
            skip=skip,
            limit=limit
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get-by-type/{activity_type}", response_model=GeneralResponse)
async def get_activities_by_type(
    request: Request,
    activity_type: str,
    service: service_dependency,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all activities of a specific type.
    
    Args:
        request: FastAPI request object
        activity_type: Activity type (CREATE, UPDATE, DELETE, STATUS_CHANGE)
        service: Injected activity service
        skip: Number of records to skip for pagination
        limit: Number of records to return
        
    Returns:
        GeneralResponse with list of activities of the specified type
    """
    try:
        result = service.get_activities_by_type(
            activity_type=activity_type,
            skip=skip,
            limit=limit
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
