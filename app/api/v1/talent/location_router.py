"""API router for talent location mapping management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentLocationMappingCreate,
    TalentLocationMappingUpdate,
)
from app.services.talent.location_service import LocationService

router = APIRouter(prefix="/location", tags=["Talent Location Mapping"])

service_dependency = Annotated[LocationService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_location_mapping(
    request: Request,
    location_data: TalentLocationMappingCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new talent location mapping.
    
    Args:
        request: FastAPI request object
        location_data: Location mapping data to create
        service: Location service dependency
        
    Returns:
        GeneralResponse with created location mapping data
        
    Raises:
        HTTPException: If creation fails or validation errors occur
    """
    try:
        result = service.create_location_mapping(location_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Location mapping created successfully",
            data=result,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create location mapping"
        )


@router.get("/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_location_mappings_by_talent(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all location mappings for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: ID of the talent profile
        service: Location service dependency
        
    Returns:
        GeneralResponse with list of location mappings for the talent
        
    Raises:
        HTTPException: If retrieval fails
    """
    try:
        result = service.get_location_mappings_by_talent_id(talent_profile_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(result)} location mapping(s) for talent {talent_profile_id}",
            data=result,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving location mappings",
        )


@router.get("/{location_id}", response_model=GeneralResponse)
async def get_location_mapping_by_id(
    request: Request,
    location_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a location mapping by its ID.
    
    Args:
        request: FastAPI request object
        location_id: ID of the location mapping
        service: Location service dependency
        
    Returns:
        GeneralResponse with location mapping data
        
    Raises:
        HTTPException: If location mapping is not found or retrieval fails
    """
    try:
        result = service.get_location_mapping_by_id(location_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Location mapping retrieved successfully",
            data=result,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving location mapping",
        )


@router.put("/{location_id}", response_model=GeneralResponse)
async def update_location_mapping(
    request: Request,
    location_id: int,
    location_data: TalentLocationMappingUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing location mapping.
    
    Args:
        request: FastAPI request object
        location_id: ID of the location mapping to update
        location_data: Updated location mapping data
        service: Location service dependency
        
    Returns:
        GeneralResponse with updated location mapping data
        
    Raises:
        HTTPException: If location mapping is not found, validation fails, or update fails
    """
    try:
        result = service.update_location_mapping(location_id, location_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Location mapping updated successfully",
            data=result,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while updating location mapping",
        )


@router.delete("/{location_id}", response_model=GeneralResponse)
async def delete_location_mapping(
    request: Request,
    location_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a location mapping.
    
    Args:
        request: FastAPI request object
        location_id: ID of the location mapping to delete
        service: Location service dependency
        
    Returns:
        GeneralResponse confirming deletion
        
    Raises:
        HTTPException: If location mapping is not found or deletion fails
    """
    try:
        service.delete_location_mapping(location_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Location mapping deleted successfully",
            data=None,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while deleting location mapping",
        )
