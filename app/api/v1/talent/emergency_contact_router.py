"""API router for talent emergency contact information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentEmergencyContactCreate,
    TalentEmergencyContactUpdate,
    TalentEmergencyContactResponse,
)
from app.services.talent import EmergencyContactService

router = APIRouter()

service_dependency = Annotated[EmergencyContactService, Depends()]

@router.post("/create", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_emergency_contact(
    request: Request,
    emergency_contact_data: TalentEmergencyContactCreate,
    service: service_dependency,
):
    """Create a new emergency contact record.
    
    Args:
        emergency_contact_data: Emergency contact data to create
        service: Emergency contact service instance
        
    Returns:
        GeneralResponse with created emergency contact data
    """
    try:
        service.create_emergency_contact(emergency_contact_data)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Emergency contact created successfully"
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create emergency contact: {str(e)}"
        )


@router.get("/get-all/{talent_id}", response_model=GeneralResponse)
async def get_emergency_contacts_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all emergency contacts for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Emergency contact service instance
        
    Returns:
        GeneralResponse with list of emergency contact data
    """
    try:
        emergency_contacts = service.get_emergency_contacts_by_talent_id(talent_id)
        
        if not emergency_contacts:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="No emergency contacts found for this talent",
                data=[]
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Emergency contacts retrieved successfully",
            data=emergency_contacts
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get emergency contacts: {str(e)}",
        )


@router.get("/get-by-id/{emergency_contact_id}", response_model=GeneralResponse)
async def get_emergency_contact_by_id(
    request: Request,
    emergency_contact_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get emergency contact by ID.
    
    Args:
        emergency_contact_id: The emergency contact ID
        service: Emergency contact service instance
        
    Returns:
        GeneralResponse with emergency contact data
    """
    try:
        emergency_contact = service.get_emergency_contact_by_id(emergency_contact_id)
        
        if not emergency_contact:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="Emergency contact not found",
                data=None
            )
            
        response_data = TalentEmergencyContactResponse.model_validate(emergency_contact)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Emergency contact retrieved successfully",
            data=response_data.model_dump()
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get emergency contact: {str(e)}",
        )


@router.put("/update/{emergency_contact_id}", response_model=GeneralResponse)
async def update_emergency_contact(
    request: Request,
    emergency_contact_id: int,
    emergency_contact_data: TalentEmergencyContactUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing emergency contact record.
    
    Args:
        emergency_contact_id: The emergency contact ID to update
        emergency_contact_data: Updated emergency contact data
        service: Emergency contact service instance
        
    Returns:
        GeneralResponse with updated emergency contact data
    """
    try:
        emergency_contact = service.update_emergency_contact(emergency_contact_id, emergency_contact_data)
        
        if not emergency_contact:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="Emergency contact not found",
                data=None
            )
            
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Emergency contact updated successfully",
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update emergency contact: {str(e)}",
        )


@router.delete("/delete/{emergency_contact_id}", response_model=GeneralResponse)
async def delete_emergency_contact(
    request: Request,
    emergency_contact_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete an emergency contact record.
    
    Args:
        emergency_contact_id: The emergency contact ID to delete
        service: Emergency contact service instance
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        deleted = service.delete_emergency_contact(emergency_contact_id)
        
        if not deleted:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="Emergency contact not found",
                data=None
            )
            
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Emergency contact deleted successfully",
            data=None
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete emergency contact: {str(e)}",
        )
