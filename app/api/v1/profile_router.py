from typing import Annotated
from fastapi import APIRouter, Depends, Request, HTTPException, status

from app.schemas.profile_schema import PasswordUpdateSchema, ProfileSchema
from app.services import ProfileService
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error


router = APIRouter()

service_dependency = Annotated[ProfileService, Depends()]


@router.get("/get", response_model=GeneralResponse)
async def get_profile(
    request: Request,
    service: service_dependency,
) -> GeneralResponse:
    """Get user profile information.
    
    Args:
        request: FastAPI request object
        user_id: The user ID
        service: Injected profile service
        
    Returns:
        GeneralResponse with profile data
    """
    try:
        profile = service.get_profile()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Profile retrieved successfully",
            data=profile,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/update-password", response_model=GeneralResponse)
async def update_password(
    request: Request,
    data: PasswordUpdateSchema, 
    service: service_dependency,
) -> GeneralResponse:
    """Update user password.
    
    Args:
        request: FastAPI request object
        data: The password update data
        service: Injected profile service
        
    Returns:
        GeneralResponse with profile data
    """
    try:
        profile = service.get_profile()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        service.update_password(data.old_password, data.new_password)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Password updated successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/update", response_model=GeneralResponse)
async def update_profile(
    request: Request,
    data: ProfileSchema,
    service: service_dependency,
) -> GeneralResponse:
    """Update user profile information.
    
    Args:
        request: FastAPI request object
        data: The profile data to update
        service: Injected profile service
        
    Returns:
        GeneralResponse with profile data
    """
    try:
        profile = service.get_profile()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        service.update_profile(data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Profile updated successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.get("/get-user-permissions", response_model=GeneralResponse)
async def get_user_permissions(
    request: Request,
    service: service_dependency,
) -> GeneralResponse:
    """Get user permissions.
    
    Args:
        request: FastAPI request object
        service: Injected profile service
        
    Returns:
        GeneralResponse with user permissions data
    """
    try:
        permissions = service.get_user_permissions()
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="User permissions retrieved successfully",
            data=permissions,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
