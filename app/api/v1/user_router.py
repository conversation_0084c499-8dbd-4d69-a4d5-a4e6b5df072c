"""User management API endpoints."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.user import UserCreate, UserUpdate
from app.services.user_service import UserService

router = APIRouter()

__service = Annotated[UserService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    request: Request,
    user_data: UserCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new user."""
    try:
        user = service.create_user(user_data, request)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="User created successfully",
            data=user.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "email already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/list", response_model=GeneralResponse)
async def get_all_users(
    request: Request,
    service: __service,
) -> GeneralResponse:
    """Get all users with specific fields: name, email, is_super_user, role, and status."""
    try:
        users = service.get_all_users()
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Users retrieved successfully",
            data=users
        )
    except ValueError as e:
        error_msg = str(e)
        if "administrators" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/get/{user_id}", response_model=GeneralResponse)
async def get_user_by_id(
    request: Request,
    user_id: int,
    service: __service,
) -> GeneralResponse:
    """Get user by ID."""
    try:
        user = service.get_user_by_id(user_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="User retrieved successfully",
            data=user.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/email/{email}", response_model=GeneralResponse)
async def get_user_by_email(
    request: Request,
    email: str,
    service: __service,
) -> GeneralResponse:
    """Get user by email."""
    try:
        user = service.get_user_by_email(email)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="User retrieved successfully",
            data=user.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/update/{user_id}", response_model=GeneralResponse)
async def update_user(
    request: Request,
    user_id: int,
    user_data: UserUpdate,
    service: __service,
) -> GeneralResponse:
    """Update user information."""
    try:
        service.update_user(user_id, user_data, request)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="User updated successfully"
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "only update your own" in error_msg.lower() or "administrator" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{user_id}", response_model=GeneralResponse)
async def delete_user(
    request: Request,
    user_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete a user."""
    try:
        service.delete_user(user_id, request)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="User deleted successfully",
            data=None
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "administrators" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )
        elif "cannot delete your own" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{user_id}/toggle-status", response_model=GeneralResponse)
async def toggle_user_status(
    request: Request,
    user_id: int,
    service: __service,
) -> GeneralResponse:
    """Toggle user active status."""
    try:
        user = service.toggle_user_status(user_id, request)
        
        status_text = "activated" if user.is_active else "deactivated"
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"User {status_text} successfully",
            data=user.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "administrators" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )
        elif "cannot deactivate your own" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
