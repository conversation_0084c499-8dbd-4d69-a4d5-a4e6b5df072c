<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoho Email Creation Request</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #2c3e50;
            padding: 30px;
            text-align: left;
        }
        .logo {
            color: #ffffff;
            font-size: 32px;
            font-weight: bold;
            margin: 0;
        }
        .tagline {
            color: #e67e22;
            font-size: 14px;
            margin: 5px 0 0 0;
            font-weight: 500;
        }
        .content {
            padding: 40px 30px;
            background-color: #ffffff;
            position: relative;
        }
        .content::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background-image: radial-gradient(circle, #f8f9fa 2px, transparent 2px);
            background-size: 20px 20px;
            opacity: 0.1;
        }
        .greeting {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .subtitle {
            font-size: 18px;
            margin-bottom: 30px;
            color: #34495e;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .info-value {
            color: #555;
            margin-bottom: 15px;
        }
        .request-text {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 25px 0;
            border-left: 4px solid #e67e22;
            line-height: 1.6;
        }
        .cta-button {
            display: inline-block;
            background-color: #e67e22;
            color: #ffffff;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #d35400;
        }
        .footer {
            padding: 25px 30px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        .help-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        .help-link {
            color: #3498db;
            text-decoration: none;
        }
        .help-link:hover {
            text-decoration: underline;
        }
        .copyright {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
            line-height: 1.4;
        }
        .footer-links {
            margin-top: 10px;
        }
        .footer-links a {
            color: #666;
            text-decoration: none;
            font-size: 12px;
            margin-right: 15px;
        }
        .footer-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1 class="logo">BPO</h1>
            <p class="tagline">SOLUTIONS GROUP</p>
        </div>
        
        <div class="content">
            <h2 class="greeting">Hi {{ it_person_name }},</h2>
            <p class="subtitle">New Talent Email Creation Request</p>
            
            <div class="info-section">
                <div class="info-label">Talent Name:</div>
                <div class="info-value">{{ talent_first_name }} {{ talent_last_name }}</div>
                
                <div class="info-label">Requested Email:</div>
                <div class="info-value">{{ talent_email }}</div>
                
                <div class="info-label">Request Date:</div>
                <div class="info-value">{{ request_date }}</div>
                
                <div class="info-label">Priority:</div>
                <div class="info-value">High - New Talent Onboarding</div>
            </div>
            
            <div class="request-text">
                <strong>Action Required:</strong> Please create a new email account in Zoho Mail for the above talent. This is part of our new talent onboarding process and requires immediate attention to ensure smooth integration into our systems.
            </div>
            
            <p>Please complete the following steps:</p>
            <ul style="line-height: 1.6; color: #555;">
                <li>Create the email account: <strong>{{ talent_email }}</strong></li>
                <li>Set up appropriate access permissions and groups</li>
                <li>Configure email forwarding if required</li>
                <li>Send temporary password to HR department</li>
                <li>Confirm completion by replying to this email</li>
            </ul>
            
            <a href="https://mail.zoho.com/zm/" class="cta-button">Access Zoho Admin Panel</a>
        </div>
        
        <div class="footer">
            <p class="help-text">
                Need help? <a href="mailto:<EMAIL>" class="help-link">Contact HR Department</a>
            </p>
            
            <div class="copyright">
                Copyright © 2025 BPO Solutions Group<br>
                Blvd. Gustavo Díaz Ordaz 12415, El Paraíso, 22106 Tijuana, B.C. All rights reserved.
            </div>
            
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms & Conditions</a>
            </div>
        </div>
    </div>
</body>
</html>