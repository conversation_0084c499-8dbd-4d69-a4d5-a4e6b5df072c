"""Email service for sending Zoho email creation requests to IT department.

This module provides the main service class for sending professional HTML emails to IT personnel
requesting the creation of new talent email accounts in Zoho Mail platform.
"""

import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from pathlib import Path
from typing import Optional

from jinja2 import Environment, FileSystemLoader, Template

from app.core.config import settings
from app.core.logs import log_error_with_context
from .config import ZohoEmailConfig
from .schemas import ITEmailRequest


class ZohoEmailService:
    """Service for sending Zoho email creation requests."""
    
    def __init__(self):
        """Initialize the email service with configuration.
        
        Args:
            config: Zoho email configuration
        """
        self.config = ZohoEmailConfig(sender_email=settings.sasi_email,
                                      sender_password=settings.sasi_email_key)
        self.template_dir = Path(__file__).parent / "templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(self.template_dir),
            autoescape=True
        )
    
    def _load_template(self, template_name: str) -> Template:
        """Load and return a Jinja2 template.
        
        Args:
            template_name: Name of the template file
            
        Returns:
            Jinja2 Template object
            
        Raises:
            FileNotFoundError: If template file doesn't exist
        """
        try:
            template = self.jinja_env.get_template(template_name)
            return template
        except Exception as e:
            error_msg = f"Failed to load template '{template_name}': {str(e)}"
            log_error_with_context(
                error=e,
                context={
                    "operation": "load_template",
                    "template_name": template_name
                }
            )
            raise FileNotFoundError(error_msg) from e
    
    def _render_email_content(self, request_data: ITEmailRequest) -> str:
        """Render the HTML email content using Jinja2 template.
        
        Args:
            request_data: IT email request data
            
        Returns:
            Rendered HTML content
        """
        try:
            template = self._load_template("zoho_creation_email.html")
            
            # Prepare template variables
            template_vars = {
                "it_person_name": request_data.it_person_name,
                "talent_first_name": request_data.talent_first_name,
                "talent_last_name": request_data.talent_last_name,
                "talent_email": request_data.talent_email,
                "request_date": request_data.request_date or datetime.now().strftime("%B %d, %Y")
            }
            
            rendered_content = template.render(**template_vars)
            return rendered_content
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "render_email_content",
                    "talent_email": request_data.talent_email
                }
            )
            raise
    
    def _create_email_message(
        self, 
        to_email: str, 
        subject: str, 
        html_content: str,
        cc_emails: Optional[list[str]] = None
    ) -> MIMEMultipart:
        """Create email message with proper headers and content.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            cc_emails: Optional list of CC email addresses
            
        Returns:
            Configured MIMEMultipart message
        """
        try:
            msg = MIMEMultipart("alternative")
            msg["From"] = formataddr((self.config.sender_name, self.config.sender_email))
            msg["To"] = to_email
            msg["Subject"] = subject
            
            if cc_emails:
                msg["Cc"] = ", ".join(cc_emails)
            
            # Attach HTML content
            html_part = MIMEText(html_content, "html", "utf-8")
            msg.attach(html_part)
            
            return msg
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_email_message",
                    "to_email": to_email,
                    "subject": subject
                }
            )
            raise
    
    def _send_email_via_smtp(
        self, 
        message: MIMEMultipart, 
        recipients: list[str]
    ) -> None:
        """Send email via SMTP server.
        
        Args:
            message: Configured email message
            recipients: List of all recipient email addresses (To + CC)
            
        Raises:
            smtplib.SMTPException: If email sending fails
        """
        try:
            if self.config.use_ssl:
                server = smtplib.SMTP_SSL(self.config.smtp_server, self.config.smtp_port)
            else:
                server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
                server.starttls()
            
            with server:
                server.login(self.config.sender_email, self.config.sender_password)
                server.sendmail(
                    self.config.sender_email,
                    recipients,
                    message.as_string()
                )
            
        except smtplib.SMTPAuthenticationError as e:
            error_msg = f"SMTP authentication failed: {str(e)}"
            log_error_with_context(
                error=e,
                context={
                    "operation": "smtp_send",
                    "smtp_server": self.config.smtp_server,
                    "sender_email": self.config.sender_email
                }
            )
            raise smtplib.SMTPException(error_msg) from e
            
        except smtplib.SMTPException as e:
            error_msg = f"Failed to send email via SMTP: {str(e)}"
            log_error_with_context(
                error=e,
                context={
                    "operation": "smtp_send",
                    "recipients_count": len(recipients)
                }
            )
            raise
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "smtp_send",
                    "recipients": recipients
                }
            )
            raise
    
    def send_it_email_request(
        self,
        request_data: ITEmailRequest,
        it_email: str,
        cc_emails: Optional[list[str]] = None
    ) -> bool:
        """Send IT email creation request.
        
        Args:
            request_data: IT email request data
            it_email: IT department email address
            cc_emails: Optional list of CC email addresses
            
        Returns:
            True if email sent successfully, False otherwise
            
        Raises:
            ValueError: If request data is invalid
            smtplib.SMTPException: If email sending fails
        """
        try:
            
            # Render email content
            html_content = self._render_email_content(request_data)
            
            # Create email subject
            subject = f"[URGENT] New Talent Email Creation Request - {request_data.talent_first_name} {request_data.talent_last_name}"
            
            # Create email message
            message = self._create_email_message(
                to_email=it_email,
                subject=subject,
                html_content=html_content,
                cc_emails=cc_emails
            )
            
            # Prepare recipient list (To + CC)
            all_recipients = [it_email]
            if cc_emails:
                all_recipients.extend(cc_emails)
            
            # Send email
            self._send_email_via_smtp(message, all_recipients)
            
            return True
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "send_it_email_request",
                    "talent_email": request_data.talent_email,
                    "it_email": it_email
                }
            )
            raise
