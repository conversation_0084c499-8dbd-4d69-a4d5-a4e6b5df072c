"""Schema models for Zoho email service.

This module contains Pydantic models for email request data validation.
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class ITEmailRequest(BaseModel):
    """Schema for IT email creation request data."""
    
    it_person_name: str = Field(..., min_length=1, max_length=100, description="IT person's name")
    talent_first_name: str = Field(..., min_length=1, max_length=50, description="Talent's first name")
    talent_last_name: str = Field(..., min_length=1, max_length=50, description="Talent's last name")
    talent_email: EmailStr = Field(..., description="Requested talent email address")
    request_date: Optional[str] = Field(None, description="Request date (auto-generated if not provided)")