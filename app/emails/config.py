"""Configuration models for Zoho email service.

This module contains configuration classes and settings for the Zoho email service.
"""

from pydantic import BaseModel, Field


class ZohoEmailConfig(BaseModel):
    """Configuration for Zoho email service."""
    
    smtp_server: str = Field(default="smtp.zoho.com", description="Zoho SMTP server")
    smtp_port: int = Field(default=465, description="SMTP port (465 for SSL, 587 for TLS)")
    use_ssl: bool = Field(default=True, description="Use SSL connection")
    sender_email: str = Field(..., description="Sender email address")
    sender_password: str = Field(..., description="Sender email password")
    sender_name: str = Field(default="BPO Admin System", description="Sender display name")