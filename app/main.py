"""
Main application module for the BPO Admin backend.

This module initializes and configures the FastAPI application, sets up logging,
database connections, and starts the application server.
"""

from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI

from app.routers import register_routers
from app.core.config import settings
from app.core.logs import logger, configure_fastapi_logging
from app.db.init_db import init_db
from app.db.seeders.module_seeder import seed_modules
from app.db.seeders.user_seeder import seed_users
from app.middlewares.cors_middleware import cors_middleware
from app.scheduler.start_scheduler import setup_scheduler


@asynccontextmanager
async def lifespan(_: FastAPI):
    """
    Summary:
        Lifespan context manager for the FastAPI application.

        This async context manager handles application startup and shutdown events.
        It is called when the FastAPI application starts up and shuts down.

    Args:
        _ (FastAPI): The FastAPI application instance (unused parameter)

    Returns:
        None: This function doesn't return any value directly, but yields control
        back to the application during its lifetime.

    Raises:
        Exception: If database initialization fails during startup
    """
    logger.info("Starting application initialization")
    scheduler = None
    try:
        # Initialize the database
        init_db()
        # Run the database seeder
        logger.info("Running database seeder")
        if bool(settings.run_seeder):
            seed_users()
            seed_modules()
        else:
            logger.info("Seeder is not enabled")

        logger.info("Database initialized successfully")
        
        # Setup and start the scheduler
        if settings.env_type == "production":
            scheduler = setup_scheduler()
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {str(e)}")
        raise
    yield
    
    # Shutdown scheduler if it was started
    if scheduler:
        scheduler.shutdown()
        logger.info("Scheduler shutdown complete")
    
    logger.info("Application shutdown complete")


app = FastAPI(
    lifespan=lifespan,
    root_path="/api/v1",
    title="BPO Admin",
    version="1.0.0",
)

cors_middleware(app)

# Register all application routers
register_routers(app)


def start():
    """
    Start the FastAPI application server.

    This function configures the FastAPI logging integration and starts
    the Uvicorn server to serve the application on the configured port.
    """
    
    # Configure FastAPI logging
    configure_fastapi_logging()

    logger.info(f"Starting server on port {settings.app_port}")
    uvicorn.run(
        "app.main:app", host="0.0.0.0", port=int(settings.app_port), reload=True
    )


if __name__ == "__main__":
    start()
