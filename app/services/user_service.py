"""User service for user management business logic."""

from typing import Annotated, List, Optional, Dict, Any
from fastapi import Depends, Request
from app.core import get_hashed_password
from app.db import User
from app.repositories import UserRepository
from app.repositories.master.role_repository import RoleRepository
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync


class UserService:
    def __init__(
        self,
        user_repository: Annotated[UserRepository, Depends()],
        role_repository: Annotated[RoleRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.user_repository = user_repository
        self.role_repository = role_repository
        self.current_user = current_user
    
    def create_user(self, user_data: UserCreate, request: Optional[Request] = None) -> UserResponse:
        """Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            UserResponse: Created user data
            
        Raises:
            ValueError: If user creation fails or email already exists
        """
        # Check if user with email already exists
        existing_user: Optional[User] = self.user_repository.get_by_email(user_data.email)
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Hash the password
        hashed_password = get_hashed_password(user_data.password)
        
        # Create user with hashed password
        user_create_dict: Dict[str, Any] = {
            "name": user_data.name,
            "email": user_data.email,
            "phone": user_data.phone,
            "password": hashed_password,
            "is_superuser": user_data.is_superuser,
            "role_id": user_data.role_id,
            "department": user_data.department,
            "is_active": True,
        }
        
        created_user: User = self.user_repository.create(user_create_dict)
        
        # Track user creation activity
        track_talent_activity_sync(
            talent_profile_id=None,  # User management activities don't have talent_profile_id
            activity_type="CREATE",
            activity_description=f"Created new user: {created_user.name} ({created_user.email})",
            new_data={
                "user_id": created_user.id,
                "name": created_user.name,
                "email": created_user.email,
                "phone": created_user.phone,
                "department": created_user.department,
                "role_id": created_user.role_id,
                "is_superuser": created_user.is_superuser,
                "is_active": created_user.is_active
            },
            request=request,
            current_user=self.current_user
        )
        
        return UserResponse(
            id=created_user.id or 0,
            name=created_user.name or "",
            email=created_user.email or "",
            phone=created_user.phone or "",
            pic=created_user.pic,
            is_active=created_user.is_active or False,
            is_superuser=created_user.is_superuser or False
        )
    
    def get_user_by_id(self, user_id: int) -> UserResponse:
        """Get a user by ID.
        
        Args:
            user_id: User ID to retrieve
            
        Returns:
            UserResponse: User data
            
        Raises:
            ValueError: If user not found
        """
        user: User = self.user_repository.get_by_id(user_id)
        
        return UserResponse(
            id=user.id or 0,
            name=user.name or "",
            email=user.email or "",
            phone=user.phone or "",
            pic=user.pic,
            department=user.department,
            role_id=user.role_id,
            is_active=user.is_active or False,
            is_superuser=user.is_superuser or False
        )
    
    def get_user_by_email(self, email: str) -> UserResponse:
        """Get a user by email.
        
        Args:
            email: User email to retrieve
            
        Returns:
            UserResponse: User data
            
        Raises:
            ValueError: If user not found
        """
        user: Optional[User] = self.user_repository.get_by_email(email)
        if not user:
            raise ValueError("User not found")
        
        return UserResponse(
            id=user.id or 0,
            name=user.name or "",
            email=user.email or "",
            phone=user.phone or "",
            pic=user.pic,
            is_active=user.is_active or False,
            is_superuser=user.is_superuser or False
        )
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users (admin only).
        
        Returns:
            List[Dict[str, Any]]: List of all users with specific fields
            
        Raises:
            ValueError: If user is not admin
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can view all users")
        
        users: List[User] = self.user_repository.get_all()
        
        result: List[Dict[str, Any]] = []
        for user in users:
            # Get role name if user has a role_id
            role_name: Optional[str] = None
            if user.role_id:
                role = self.role_repository.get_by_id(user.role_id)
                role_name = role.name if role else None
            
            user_data: Dict[str, Any] = {
                "id": user.id or 0,
                "name": user.name or "",
                "email": user.email or "",
                "is_super_user": user.is_superuser or False,
                "role": role_name,
                "status": user.is_active or False,
                "department": user.department,
            }
            result.append(user_data)
        
        return result
    
    def update_user(self, user_id: int, user_data: UserUpdate, request: Optional[Request] = None):
        """Update a user.
        
        Args:
            user_id: User ID to update
            user_data: Updated user data
            
        Returns:
            UserResponse: Updated user data
            
        Raises:
            ValueError: If user not found or permission denied
        """
        # Check if user can update (self or admin)
        if user_id != self.current_user.id and not self.current_user.is_superuser:
            raise ValueError("You can only update your own profile or be an administrator")
        
        user: User = self.user_repository.get_by_id(user_id)
        
        # Store old data for activity tracking
        old_data: Dict[str, Any] = {
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "department": user.department,
            "role_id": user.role_id,
            "is_superuser": user.is_superuser,
            "is_active": user.is_active
        }
        
        # Apply updates
        updated_fields: List[str] = []
        for field, value in user_data.model_dump().items():
            if value is not None:
                if field == "password":
                    value = get_hashed_password(value)
                    updated_fields.append("password")
                else:
                    updated_fields.append(field)
                setattr(user, field, value)
        
        self.user_repository.update(user)
        
        # Store new data for activity tracking
        new_data: Dict[str, Any] = {
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "department": user.department,
            "role_id": user.role_id,
            "is_superuser": user.is_superuser,
            "is_active": user.is_active
        }
        
        # Track user update activity
        track_talent_activity_sync(
            talent_profile_id=None,  # User management activities don't have talent_profile_id
            activity_type="UPDATE",
            activity_description=f"Updated user: {user.name} ({user.email}) - Fields: {', '.join(updated_fields)}",
            old_data=old_data,
            new_data=new_data,
            request=request,
            current_user=self.current_user
        )
    
    def delete_user(self, user_id: int, request: Optional[Request] = None) -> bool:
        """Delete a user (admin only).
        
        Args:
            user_id: User ID to delete
            
        Returns:
            bool: True if deletion successful
            
        Raises:
            ValueError: If user not found, not admin, or deletion fails
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can delete users")
        
        # Prevent self-deletion
        if user_id == self.current_user.id:
            raise ValueError("You cannot delete your own account")
        
        user: User = self.user_repository.get_by_id(user_id)
        
        # Store user data for activity tracking before deletion
        user_data: Dict[str, Any] = {
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            "phone": user.phone,
            "department": user.department,
            "role_id": user.role_id,
            "is_superuser": user.is_superuser,
            "is_active": user.is_active
        }
        
        self.user_repository.delete(user)
        
        # Track user deletion activity
        track_talent_activity_sync(
            talent_profile_id=None,  # User management activities don't have talent_profile_id
            activity_type="DELETE",
            activity_description=f"Deleted user: {user_data['name']} ({user_data['email']})",
            old_data=user_data,
            request=request,
            current_user=self.current_user
        )
        
        return True
    
    def toggle_user_status(self, user_id: int, request: Optional[Request] = None) -> UserResponse:
        """Toggle user active status (admin only).
        
        Args:
            user_id: User ID to toggle status
            
        Returns:
            UserResponse: Updated user data
            
        Raises:
            ValueError: If user not found, not admin, or operation fails
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can toggle user status")
        
        # Prevent self-deactivation
        if user_id == self.current_user.id:
            raise ValueError("You cannot deactivate your own account")
        
        user: User = self.user_repository.get_by_id(user_id)
        
        # Store old status for activity tracking
        old_data: Dict[str, Any] = {
            "user_id": user.id,
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active
        }
        
        updated_user: User = self.user_repository.toggle_status(user)
        
        # Store new status for activity tracking
        new_data: Dict[str, Any] = {
            "user_id": updated_user.id,
            "name": updated_user.name,
            "email": updated_user.email,
            "is_active": updated_user.is_active
        }
        
        # Track user status toggle activity
        status_action = "activated" if updated_user.is_active else "deactivated"
        track_talent_activity_sync(
            talent_profile_id=None,  # User management activities don't have talent_profile_id
            activity_type="UPDATE",
            activity_description=f"User status {status_action}: {updated_user.name} ({updated_user.email})",
            old_data=old_data,
            new_data=new_data,
            request=request,
            current_user=self.current_user
        )
        
        return UserResponse(
            id=updated_user.id or 0,
            name=updated_user.name or "",
            email=updated_user.email or "",
            phone=updated_user.phone or "",
            pic=updated_user.pic,
            is_active=updated_user.is_active or False,
            is_superuser=updated_user.is_superuser or False
        )
