"""Service layer for talent location mapping business logic."""

from typing import Annotated, List, Optional

from fastapi import Depends, HTTPException, status

from app.core import logger
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import User
from app.repositories.talent.location_repository import LocationRepository
from app.schemas.talent.location_schema import (
    TalentLocationMappingCreate,
    TalentLocationMappingUpdate,
    TalentLocationMappingResponse,
)
from app.core.jwt import get_current_user


class LocationService:
    """Service class for talent location mapping business logic."""

    def __init__(
        self,
        repository: Annotated[LocationRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ):
        """Initialize the service with repository and current user.
        
        Args:
            repository: Location repository for data access
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_location_mapping(
        self, location_data: TalentLocationMappingCreate
    ) -> Optional[TalentLocationMappingResponse]:
        """Create a new talent location mapping.
        
        Args:
            location_data: Location mapping data to create
            
        Returns:
            Created location mapping response or None if creation failed
            
        Raises:
            HTTPException: If validation fails or creation fails
        """
        try:
            # Validate input data
            if not location_data.location.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Location cannot be empty"
                )
            
            if not location_data.duration_of_stay.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Duration of stay cannot be empty"
                )

            # Check if location mapping already exists for this talent and location
            existing_mapping = self.repository.get_location_mapping_by_talent_and_location(
                location_data.talent_profile_id, location_data.location
            )
            if existing_mapping:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Location mapping already exists for talent {location_data.talent_profile_id} at location {location_data.location}"
                )

            # Create the location mapping
            location_mapping = self.repository.create_location_mapping(location_data)
            if not location_mapping:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create location mapping"
                )

            # Track activity for location mapping creation
            new_data = location_mapping.model_dump() if hasattr(location_mapping, 'model_dump') else location_data.model_dump()
            track_talent_activity_sync(
                talent_profile_id=location_data.talent_profile_id,
                activity_type="CREATE",
                activity_description="Location mapping created",
                new_data=new_data,
                current_user=self.current_user
            )

            logger.info(
                f"User {self.current_user.id} created location mapping for talent {location_data.talent_profile_id}"
            )
            return TalentLocationMappingResponse.model_validate(location_mapping)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in create_location_mapping: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while creating location mapping"
            )

    def get_location_mapping_by_id(self, location_id: int) -> Optional[TalentLocationMappingResponse]:
        """Get a location mapping by its ID.
        
        Args:
            location_id: ID of the location mapping
            
        Returns:
            Location mapping response if found, None otherwise
            
        Raises:
            HTTPException: If location mapping is not found
        """
        try:
            location_mapping = self.repository.get_location_mapping_by_id(location_id)
            if not location_mapping:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Location mapping with ID {location_id} not found"
                )
            
            return TalentLocationMappingResponse.model_validate(location_mapping)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in get_location_mapping_by_id: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while retrieving location mapping"
            )

    def get_location_mappings_by_talent_id(
        self, talent_profile_id: int
    ) -> List[TalentLocationMappingResponse]:
        """Get all location mappings for a specific talent.
        
        Args:
            talent_profile_id: ID of the talent profile
            
        Returns:
            List of location mapping responses for the talent
        """
        try:
            location_mappings = self.repository.get_location_mappings_by_talent_id(
                talent_profile_id
            )
            return [
                TalentLocationMappingResponse.model_validate(mapping)
                for mapping in location_mappings
            ]
        except Exception as e:
            logger.error(f"Error in get_location_mappings_by_talent_id: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while retrieving location mappings"
            )

    def update_location_mapping(
        self, location_id: int, location_data: TalentLocationMappingUpdate
    ) -> Optional[TalentLocationMappingResponse]:
        """Update an existing location mapping.
        
        Args:
            location_id: ID of the location mapping to update
            location_data: Updated location mapping data
            
        Returns:
            Updated location mapping response or None if update failed
            
        Raises:
            HTTPException: If validation fails or update fails
        """
        try:
            # Check if location mapping exists
            if not self.repository.location_mapping_exists(location_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Location mapping with ID {location_id} not found"
                )

            # Validate input data
            if location_data.location is not None and not location_data.location.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Location cannot be empty"
                )
            
            if location_data.duration_of_stay is not None and not location_data.duration_of_stay.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Duration of stay cannot be empty"
                )

            # Get old data for activity tracking
            old_mapping = self.repository.get_location_mapping_by_id(location_id)
            old_data = old_mapping.model_dump() if old_mapping and hasattr(old_mapping, 'model_dump') else {}
            
            # Update the location mapping
            updated_mapping = self.repository.update_location_mapping(
                location_id, location_data
            )
            if not updated_mapping:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update location mapping"
                )

            # Track activity for location mapping update
            new_data = updated_mapping.model_dump() if hasattr(updated_mapping, 'model_dump') else {}
            changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
            
            if changed_old_data or changed_new_data:
                track_talent_activity_sync(
                    talent_profile_id=updated_mapping.talent_profile_id,
                    activity_type="UPDATE",
                    activity_description="Location mapping updated",
                    old_data=changed_old_data,
                    new_data=changed_new_data,
                    current_user=self.current_user
                )

            logger.info(
                f"User {self.current_user.id} updated location mapping {location_id}"
            )
            return TalentLocationMappingResponse.model_validate(updated_mapping)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in update_location_mapping: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while updating location mapping"
            )

    def delete_location_mapping(self, location_id: int) -> bool:
        """Delete a location mapping.
        
        Args:
            location_id: ID of the location mapping to delete
            
        Returns:
            True if deletion was successful
            
        Raises:
            HTTPException: If location mapping is not found or deletion fails
        """
        try:
            # Check if location mapping exists
            if not self.repository.location_mapping_exists(location_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Location mapping with ID {location_id} not found"
                )

            # Delete the location mapping
            success = self.repository.delete_location_mapping(location_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete location mapping"
                )

            logger.info(
                f"User {self.current_user.id} deleted location mapping {location_id}"
            )
            return True
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in delete_location_mapping: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while deleting location mapping"
            )