"""Skills service for talent management.

This module contains the service class for skills business logic operations.
"""

from typing import Annotated, List
from fastapi import Depends

from app.repositories.talent.dropdown_options_repository import DropdownOptionsRepository
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context
from app.db import User


class DropdownOptionsService:
    """Service class for dropdown options business logic.
    
    Handles all business operations related to dropdown options management.
    """
    
    def __init__(
        self, 
        repository: Annotated[DropdownOptionsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a repository and current user.
        
        Args:
            repository: Injected dropdown options repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def get_unique_skills(self) -> List[str]:
        """Get all unique skills from talent skill set mappings.
        
        This method retrieves skills arrays from the repository and processes them
        to extract, clean, and deduplicate skills.
        
        Returns:
            List of unique skill names
            
        Raises:
            Exception: If operation fails
        """
        try:
            # Get raw skills arrays from repository
            skills_arrays = self.repository.get_skills_arrays()
            
            # Process and flatten skills arrays
            unique_skills: set[str] = set()
            for skills_array in skills_arrays:
                if skills_array and isinstance(skills_array, list):
                    for skill in skills_array:
                        if skill and isinstance(skill, str):
                            # Clean and normalize the skill name
                            cleaned_skill = skill.strip()
                            if cleaned_skill:
                                unique_skills.add(cleaned_skill)
            
            # Return sorted list of unique skills
            return sorted(list(unique_skills))
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "service": "SkillsService",
                    "method": "get_unique_skills",
                    "user_id": self.current_user.id if self.current_user else None
                }
            )
            raise
