"""Service layer for talent third party integration operations."""
 
from datetime import datetime
import json
from typing import List
from fastapi import Depends, Request

from app.core.config import settings
from app.core.zoho_util import delete_user, toggle_user
from app.emails.schemas import ITEmailRequest
from app.emails.zoho_creation_email import ZohoEmailService
from app.repositories.talent.candidate_repository import TalentRepository
from app.repositories.talent.position_repository import TalentPositionRepository
from app.repositories.talent.third_party_integration_repository import TalentThirdPartyIntegrationRepository
from app.schemas.talent.third_party_integration_schema import (
    TalentThirdPartyIntegrationCreate,
    TalentThirdPartyIntegrationUpdate,
    TalentThirdPartyIntegrationResponse
)
from app.core.activity_tracker import track_talent_activity_sync
from app.core.logs import log_error_with_context
from app.core.utils import get_changed_data
from app.core.jwt import get_current_user
from app.db.models import User


class TalentThirdPartyIntegrationService:
    """Service class for talent third party integration operations."""
    
    def __init__(
        self,
        repository: TalentThirdPartyIntegrationRepository = Depends(),
        position_repository: TalentPositionRepository = Depends(),
        candidate_repository: TalentRepository = Depends(),
        zoho_email_service: ZohoEmailService = Depends(),
        current_user: User = Depends(get_current_user),
    ):
        """Initialize the service with dependencies."""
        self.repository = repository
        self.zoho_email_service = zoho_email_service
        self.position_repository = position_repository
        self.candidate_repository = candidate_repository
        self.current_user = current_user
    
    def get_third_party_integrations_by_talent(self, talent_profile_id: int) -> List[TalentThirdPartyIntegrationResponse]:
        """Get all third party integrations for a specific talent profile."""
        try:
            integrations = self.repository.get_by_talent_profile_id(talent_profile_id)
            return [
                TalentThirdPartyIntegrationResponse.model_validate(integration)
                for integration in integrations
            ]
        except Exception as e:
          log_error_with_context(
                    error=e,
                    context={
                    "talent_profile_id": talent_profile_id,
                    "user_id": self.current_user.id
                })
          raise
    
    def create_third_party_integration(self, integration_data: TalentThirdPartyIntegrationCreate, request: Request) -> None:
        """Create a new third party integration."""
        try:
            # Check if integration already exists
            existing_integrations = self.repository.get_by_talent_profile_id(
                integration_data.talent_profile_id
            )
            
            for existing in existing_integrations:
                if existing.third_party == integration_data.third_party and existing.status == "active":
                    raise ValueError(
                        f"Third party integration for {integration_data.third_party} already exists for this talent"
                    )
              
            talent_position = self.position_repository.get_by_talent_id(integration_data.talent_profile_id)
            if not talent_position:
                raise ValueError("Talent position not found")
            
            integration_data.data = talent_position.bpo_email

            if settings.env_type != "production" and talent_position.bpo_email:
              if integration_data.third_party == "Zoho Mail":
                self.__create_zoho_mail_box(talent_position.bpo_email, integration_data, request)
            
            # Create the integration
            integration_data.email_to = settings.it_email
            integration_data.email_count = 1
            integration_data.status = 'initiated'
            created_integration = self.repository.create(integration_data)
            
            # Track activity
            track_talent_activity_sync(
                talent_profile_id=created_integration.talent_profile_id,
                activity_type="CREATE",
                activity_description=f"Created third party integration: {created_integration.third_party}",
                request=request,
                old_data=None,
                new_data={"integration": "Zoho Mail", "primaryEmailAddress": talent_position.bpo_email},
                current_user=self.current_user
            )
            
        except ValueError:
            raise
        except Exception as e:
            log_error_with_context(
                    error=e,
                    context={
                    "integration_data": integration_data.model_dump(),
                    "user_id": self.current_user.id
                })
            raise ValueError("Failed to create third party integration")
    
    def update_third_party_integration(self, integration_id: int, update_data: TalentThirdPartyIntegrationUpdate, request: Request) -> None:
        """Update an existing third party integration."""
        try:
            # Get existing integration
            existing_integration = self.repository.get_by_id(integration_id)
            if not existing_integration:
                raise ValueError(f"Third party integration with ID {integration_id} not found")
            
            # Get changed data for activity tracking
            old_data = existing_integration.model_dump()
            changed_data = get_changed_data(old_data, update_data.model_dump(exclude_unset=True))
            
            if not changed_data:
                return

            if settings.env_type == "production":
              if existing_integration.third_party == "Zoho Mail" and existing_integration.json_data:
                if update_data.status in ['active', 'inactive']:
                  response  = toggle_user({
                      "zuid": json.loads(existing_integration.json_data)['zuid'],
                      "accountId": existing_integration.data,
                      "status": "Active" if update_data.status == 'active' else "Disabled",
                  })
                else:
                  response  = delete_user([json.loads(existing_integration.json_data)['primaryEmailAddress']])
                  if 'error' in response:
                    raise ValueError(response['error'])
                  track_talent_activity_sync(
                  talent_profile_id=existing_integration.talent_profile_id,
                  activity_type="DELETE",
                  activity_description=f"Deleted third party integration: {existing_integration.third_party}",
                  old_data={"status": existing_integration.status.title()},
                  new_data={"status": "Deleted"},
                  request=request,
                  current_user=self.current_user
                )
                self.repository.update(integration_id, update_data)
                return

            # Update the integration
            self.repository.update(integration_id, update_data)
            
            # Track activity
            if changed_data:
                track_talent_activity_sync(
                talent_profile_id=existing_integration.talent_profile_id,
                activity_type="UPDATE",
                activity_description=f"Updated third party integration: {existing_integration.third_party}",
                request=request,
                old_data={"status": existing_integration.status.title()},
                new_data={"status": update_data.status.title()},
                current_user=self.current_user
        )
        
        except ValueError:
            raise
        except Exception as e:
            log_error_with_context(
                    error=e,
                    context={
                    "integration_id": integration_id,
                    "update_data": update_data.model_dump(),
                    "user_id": self.current_user.id
                })
            raise


    def __create_zoho_mail_box(self, bpo_email: str, integration_data: TalentThirdPartyIntegrationCreate, request: Request) -> None:
          candidate = self.candidate_repository.get_talent_by_id(integration_data.talent_profile_id)
          data: dict[str, str] = {
            "it_person_name": settings.it_user_name,
            "talent_email": bpo_email + "@bposolutionsgroup.com",
            "talent_first_name": candidate.first_name,
            "talent_last_name": candidate.last_name,
            "request_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
          }
          self.zoho_email_service.send_it_email_request(request_data=ITEmailRequest(**data), it_email=settings.it_email)
          
