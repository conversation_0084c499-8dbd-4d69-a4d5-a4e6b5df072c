"""Banking information service for talent management.

This module contains the service class for banking information business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.banking_repository import BankingRepository
from app.schemas.talent.banking_schema import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
    TalentBankingInformationResponse
)
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data



class BankingService:
    """Service class for banking information business logic.
    
    Handles all business operations related to banking information.
    """
    
    def __init__(
        self, 
        repository: Annotated[BankingRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_banking_information(
        self, 
        banking_data: TalentBankingInformationCreate
    ) -> Optional[TalentBankingInformationResponse]:
        """Create new banking information for a talent.
        
        Args:
            banking_data: Banking information data to create
            
        Returns:
            Created banking information response or None if creation fails
        """
        try:
            # Check if banking information already exists for this talent and account
            if self.repository.exists_by_talent_and_account(
                banking_data.talent_profile_id, 
                banking_data.account_number
            ):
                log_error_with_context(
                    error=ValueError("Banking information already exists for this talent and account"),
                    context={
                        "talent_profile_id": banking_data.talent_profile_id,
                        "account_number": banking_data.account_number,
                        "operation": "create_banking_information"
                    }
                )
                raise ValueError("Banking information already exists for this talent and account")
            
            db_banking = self.repository.create(banking_data)
            if db_banking:
                # Track activity for banking information creation
                new_data = db_banking.model_dump() if hasattr(db_banking, 'model_dump') else {}
                track_talent_activity_sync(
                    talent_profile_id=banking_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Banking information created",
                    new_data=new_data,
                    current_user=self.current_user
                )
                return TalentBankingInformationResponse.model_validate(db_banking.model_dump())
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_banking_information",
                    "talent_profile_id": banking_data.talent_profile_id,
                    "account_number": banking_data.account_number,
                    "clabe": banking_data.clabe,
                    "swift_code": banking_data.swift_code,
                    "bank_name": banking_data.bank_name,
                    "error": str(e),
                    "user_id": str(self.current_user.id)
                }
            )
            raise e

    def get_banking_information_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> Optional[TalentBankingInformationResponse]:
        """Get all banking information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of banking information responses
        """
        try:
            db_banking = self.repository.get_by_talent_id(talent_profile_id)
            if db_banking:
                return TalentBankingInformationResponse.model_validate(db_banking.model_dump())
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_banking_information_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def update_banking_information(
        self, 
        banking_id: int, 
        banking_data: TalentBankingInformationUpdate
    ) -> Optional[TalentBankingInformationResponse]:
        """Update banking information.
        
        Args:
            banking_id: Banking information ID
            banking_data: Updated banking information data
            
        Returns:
            Updated banking information response or None if update fails
        """
        try:
            # Check if banking information exists
            existing_banking = self.repository.get_by_id(banking_id)

            if not existing_banking:
                log_error_with_context(
                    error=ValueError("Banking information not found"),
                    context={
                        "banking_id": banking_id,
                        "operation": "update_banking_information"
                    }
                )
                raise ValueError("Banking information not found")
            
            # Capture old data for activity tracking
            old_data = existing_banking.model_dump() if hasattr(existing_banking, 'model_dump') else {}
            
            # If account number is being updated, check for duplicates
            if banking_data.account_number and banking_data.account_number != existing_banking.account_number:
                if self.repository.exists_by_talent_and_account(
                    existing_banking.talent_profile_id, 
                    banking_data.account_number
                ):
                    log_error_with_context(
                        error=ValueError("Banking information already exists for this talent and account"),
                        context={
                            "talent_profile_id": existing_banking.talent_profile_id,
                            "account_number": banking_data.account_number,
                            "operation": "update_banking_information"
                        }
                    )
                    return None

            # Update fields
            update_data = banking_data.model_dump()
            for field, value in update_data.items():
                if value is not None:
                    setattr(existing_banking, field, value)
            
            db_banking = self.repository.update(banking_id, existing_banking)
            if db_banking:
                # Track activity for banking information update
                new_data = db_banking.model_dump() if hasattr(db_banking, 'model_dump') else {}
                
                # Get changed data using utility function
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                # Only track if there are actual changes
                if changed_old_data and changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=existing_banking.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Banking information updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
                
                return TalentBankingInformationResponse.model_validate(db_banking.model_dump())
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_banking_information",
                    "banking_id": banking_id
                }
            )
            raise e
    
    def validate_banking_information(self, banking_data: TalentBankingInformationCreate) -> List[str]:
        """Validate banking information data.
        
        Args:
            banking_data: Banking information data to validate
            
        Returns:
            List of validation error messages
        """
        errors: List[str] = []
        
        try:
            # Validate account number format (basic validation)
            if not banking_data.account_number.isdigit():
                errors.append("Account number must contain only digits")
            
            # Validate CLABE format (18 digits for Mexican CLABE)
            if banking_data.clabe and len(banking_data.clabe) != 18:
                errors.append("CLABE must be exactly 18 digits")
            
            if banking_data.clabe and not banking_data.clabe.isdigit():
                errors.append("CLABE must contain only digits")
            
            # Validate SWIFT code format (8 or 11 characters)
            if banking_data.swift_code:
                swift_len = len(banking_data.swift_code)
                if swift_len not in [8, 11]:
                    errors.append("SWIFT code must be 8 or 11 characters long")
                
                if not banking_data.swift_code.isalnum():
                    errors.append("SWIFT code must contain only alphanumeric characters")
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "validate_banking_information",
                    "talent_profile_id": banking_data.talent_profile_id
                }
            )
            errors.append("Validation error occurred")
        
        return errors
