"""Service for talent emergency contact information business logic."""

from typing import Annotated, List, Optional

from fastapi import Depends

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db.models import User, TalentEmergencyContactMapping
from app.repositories.talent import EmergencyContactRepository
from app.schemas.talent import (
    TalentEmergencyContactCreate,
    TalentEmergencyContactUpdate,
)


class EmergencyContactService:
    """Service for talent emergency contact information business logic."""

    def __init__(
        self,
        repository: Annotated[EmergencyContactRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with database session.
        
        Args:
            repository: Emergency contact repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_emergency_contact(self, emergency_contact_data: TalentEmergencyContactCreate) -> TalentEmergencyContactMapping:
        """Create a new emergency contact record.
        
        Args:
            emergency_contact_data: Emergency contact data to create
            
        Returns:
            Created emergency contact record
            
        Raises:
            Exception: If creation fails
        """
        result = self.repository.create_emergency_contact(emergency_contact_data)
        
        # Track the activity
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            track_talent_activity_sync(
                talent_profile_id=result.talent_profile_id,
                activity_type="CREATE",
                activity_description="Emergency contact created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result

    def get_emergency_contact_by_id(self, emergency_contact_id: int) -> Optional[TalentEmergencyContactMapping]:
        """Get emergency contact by ID.
        
        Args:
            emergency_contact_id: Emergency contact ID
            
        Returns:
            Emergency contact record or None if not found
        """
        return self.repository.get_emergency_contact_by_id(emergency_contact_id)

    def get_emergency_contacts_by_talent_id(self, talent_id: int) -> List[TalentEmergencyContactMapping]:
        """Get all emergency contacts for a specific talent.
        
        Args:
            talent_id: Talent profile ID
            
        Returns:
            List of emergency contact records
        """
        return self.repository.get_emergency_contacts_by_talent_id(talent_id)

    def update_emergency_contact(
        self, 
        emergency_contact_id: int, 
        emergency_contact_data: TalentEmergencyContactUpdate
    ) -> Optional[TalentEmergencyContactMapping]:
        """Update an existing emergency contact record.
        
        Args:
            emergency_contact_id: Emergency contact ID to update
            emergency_contact_data: Updated emergency contact data
            
        Returns:
            Updated emergency contact record or None if not found
            
        Raises:
            Exception: If update fails
        """
        # Get existing data before update
        existing_emergency_contact = self.repository.get_emergency_contact_by_id(emergency_contact_id)
        if not existing_emergency_contact:
            return None
            
        old_data = existing_emergency_contact.model_dump() if hasattr(existing_emergency_contact, 'model_dump') else {}
        
        result = self.repository.update_emergency_contact(
            emergency_contact_id, emergency_contact_data
        )
        
        # Track the activity if update was successful
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            
            # Get changed data using utility function
            changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
            
            # Only track if there are actual changes
            if changed_old_data or changed_new_data:
                track_talent_activity_sync(
                    talent_profile_id=result.talent_profile_id,
                    activity_type="UPDATE",
                    activity_description="Emergency contact updated",
                    old_data=changed_old_data,
                    new_data=changed_new_data,
                    current_user=self.current_user
                )
        
        return result

    def delete_emergency_contact(self, emergency_contact_id: int) -> bool:
        """Delete an emergency contact record.
        
        Args:
            emergency_contact_id: Emergency contact ID to delete
            
        Returns:
            True if deleted successfully, False if not found
            
        Raises:
            Exception: If deletion fails
        """
        return self.repository.delete_emergency_contact(emergency_contact_id)
