"""Talent Software Mapping service for business logic."""

from typing import Annotated, List
from fastapi import Depends
from app.db.models import User
from app.repositories.talent.software_mapping_repository import TalentSoftwareMappingRepository
from app.schemas.talent.software_mapping_schema import (
    TalentSoftwareMappingCreate,
    TalentSoftwareMappingUpdate,
    TalentSoftwareMappingResponse
)
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data


class TalentSoftwareMappingService:
    """Service for talent software mapping business logic."""

    def __init__(
        self,
        repository: Annotated[TalentSoftwareMappingRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.repository = repository
        self.current_user = current_user
    
    def create_software_mapping(self, mapping_data: TalentSoftwareMappingCreate):
        """Create a new software mapping."""
        try:
            # Check if mapping with same talent and software already exists
            mapping_dict = mapping_data.model_dump()
            created_mapping = self.repository.create(mapping_dict)
            
            # Track activity for software mapping creation
            if created_mapping:
                new_data = created_mapping.model_dump() if hasattr(created_mapping, 'model_dump') else mapping_data.model_dump()
                track_talent_activity_sync(
                    talent_profile_id=mapping_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Software mapping created",
                    new_data=new_data,
                    current_user=self.current_user
                )
            
            return created_mapping
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_software_mapping",
                    "talent_profile_id": mapping_data.talent_profile_id,
                    "software": mapping_data.software
                }
            )
            raise e
    
    def get_software_mapping_by_id(self, mapping_id: int) -> TalentSoftwareMappingResponse:
        """Get software mapping by ID."""
        try:
            mapping = self.repository.get_by_id(mapping_id)
            if not mapping:
                error = ValueError(f"Software mapping with ID {mapping_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_software_mapping_by_id",
                        "mapping_id": mapping_id
                    }
                )
                raise error
            return TalentSoftwareMappingResponse.model_validate(mapping)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mapping_by_id",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_software_mappings_by_talent(self, talent_profile_id: int) -> List[TalentSoftwareMappingResponse]:
        """Get all software mappings for a specific talent profile."""
        try:
            mappings = self.repository.get_by_talent_profile_id(talent_profile_id)
            return [TalentSoftwareMappingResponse.model_validate(mapping.model_dump()) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mappings_by_talent",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def update_software_mapping(self, mapping_id: int, mapping_data: TalentSoftwareMappingUpdate):
        """Update software mapping information."""
        try:
            # Get existing mapping
            mapping = self.repository.get_by_id(mapping_id)
            if not mapping:
                error = ValueError(f"Software mapping with ID {mapping_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "update_software_mapping",
                        "mapping_id": mapping_id
                    }
                )
                raise error
            
            # Capture old data for activity tracking
            old_data = mapping.model_dump()
            
            # Update mapping
            update_dict = mapping_data.model_dump()
            updated_mapping = self.repository.update(mapping, update_dict)
            
            # Track activity for software mapping update
            if updated_mapping:
                new_data = updated_mapping.model_dump()
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=mapping.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Software mapping updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
            
            return updated_mapping
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_software_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_software_mapping_count_by_talent(self, talent_profile_id: int) -> int:
        """Get count of software mappings for a specific talent profile."""
        try:
            return self.repository.count_by_talent_profile(talent_profile_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mapping_count_by_talent",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
