"""Service layer for talent profile management."""

import os
import uuid
from typing import List

from fastapi import HTTPException, status, UploadFile
from sqlmodel import Session

from app.core.activity_tracker import track_talent_activity_sync
from app.core.file_util import get_document_base64
from app.core.utils import get_changed_data
from app.db import TalentProfile, User
from app.repositories.talent import TalentRepository, TalentChronicConditionsRepository
from app.repositories.talent.position_repository import TalentPositionRepository
from app.schemas.talent import ProfileCreate, ProfileUpdate
from app.schemas.talent.talent_profile_schema import DeactivateTalent, TalentSummaryResponse



class TalentService:
    """Service class for talent profile business logic."""
    
    def __init__(
        self,
        db: Session,
        current_user: User,
    ) -> None:
        """Initialize the talent service.
        
        Args:
            db: Database session
            current_user: Current authenticated user
        """
        self.talent_repository = TalentRepository(db)
        self.chronic_conditions_repository = TalentChronicConditionsRepository(db)
        self.position_repository = TalentPositionRepository(db)
        self.current_user = current_user

    def get_talent_by_id(self, talent_id: int) -> TalentProfile:
        """Get a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            The talent profile
            
        Raises:
            HTTPException: If talent profile not found
        """
        talent = self.talent_repository.get_talent_by_id(talent_id)
        pic = ''
        if talent.pic:
          pic = get_document_base64(talent.pic)
          talent.pic = pic

        if not talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return talent
    
    def get_all_talents(self) -> List[TalentSummaryResponse]:
        """Get all talent profiles with summary information.
        
        Returns:
            List of talent summary responses with essential information
        """
        talents = self.talent_repository.get_all_talents()
        talent_summaries: List[TalentSummaryResponse] = []
        
        for talent in talents:
            # Skip talents without valid ID
            if not talent.id:
                continue
                
            # Get position information for the talent
            position = self.position_repository.get_by_talent_id(talent.id)
            
            # Construct full name
            talent_name = f"{talent.first_name}"
            if talent.middle_name:
                talent_name += f" {talent.middle_name}"
            talent_name += f" {talent.last_name}"

            pic = ''
            if talent.pic:
              pic = "data:image/jpeg;base64," + get_document_base64(talent.pic)
            
            # Create summary response
            summary = TalentSummaryResponse(
                id=talent.id,
                talent_name=talent_name,
                phone=talent.phone,
                start_date=position.start_date if position else None,
                job_position=position.position if position else None,
                supervisor=position.reporting_manager if position else None,
                status=talent.is_active,
                pic=pic
            )
            talent_summaries.append(summary)
            
        return talent_summaries

    def create_talent_profile(self, talent_profile: TalentProfile) -> TalentProfile:
        """Create a new talent profile.
        
        Args:
            talent_profile: The talent profile data to create
            
        Returns:
            The created talent profile
        """
        # Create the talent profile
        result = self.talent_repository.create_talent_profile(talent_profile)
        
        # Track the activity
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            track_talent_activity_sync(
                talent_profile_id=result.id,
                activity_type="CREATE",
                activity_description="Talent profile created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result
    
    def update_talent_profile(self, talent_profile: TalentProfile, existing_talent: ProfileUpdate) -> TalentProfile:
        """Update an existing talent profile.
        
        Args:
            talent_profile: The talent profile data to update
            existing_talent: The existing talent profile to update

        Returns:
            The updated talent profile
            
        Raises:
            HTTPException: If talent profile not found
        """
        # Get old data before update
        old_data = talent_profile.model_dump() if hasattr(talent_profile, 'model_dump') else {}

        for field, value in existing_talent.model_dump().items():
            if value is not None:
                setattr(talent_profile, field, value)
        
        # Update the talent profile
        result = self.talent_repository.update_talent_profile(talent_profile)
        
        # Track the activity with only changed fields
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            
            # Get changed data using utility function
            changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
            
            # Only track activity if there are actual changes
            if changed_old_data or changed_new_data:
                track_talent_activity_sync(
                    talent_profile_id=result.id,
                    activity_type="UPDATE",
                    activity_description="Talent profile updated",
                    new_data=changed_new_data,
                    old_data=changed_old_data,
                    current_user=self.current_user
                )
        
        return result
    
    def delete_talent_profile(self, talent_id: int) -> bool:
        """Delete a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If talent profile not found
        """
        # Get old data before deletion
        old_talent = self.talent_repository.get_talent_by_id(talent_id)
        old_data = old_talent.model_dump() if old_talent and hasattr(old_talent, 'model_dump') else {}
        
        # Delete the talent profile
        success = self.talent_repository.delete_talent_profile(talent_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        
        # Track the activity
        track_talent_activity_sync(
            talent_profile_id=talent_id,
            activity_type="DELETE",
            activity_description="Talent profile deleted",
            old_data=old_data,
            current_user=self.current_user
        )
        
        return success
    
    def create_talent_profile_from_schema(self, talent_profile: ProfileCreate) -> TalentProfile:
        """Create a new talent profile from schema.
        
        Args:
            talent_profile: The talent profile data to create
            
        Returns:
            The created talent profile
        """
        # Use schema's built-in conversion method
        mapped_data = talent_profile.model_dump()
        talent_model = TalentProfile(**mapped_data)
        return self.create_talent_profile(talent_model)
    
    def update_talent_profile_from_schema(self, talent_id: int, talent_profile: ProfileUpdate) -> TalentProfile:
        """Update an existing talent profile from schema.
        
        Args:
            talent_id: The talent profile ID to update
            talent_profile: The updated talent profile data
            
        Returns:
            The updated talent profile
        """
        # Get existing talent profile
        existing_talent = self.talent_repository.get_talent_by_id(talent_id)
        if not existing_talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        
        return self.update_talent_profile(existing_talent, talent_profile)
    
    def _create_profile_picture_directory(self, talent_id: int) -> str:
        """Create profile picture directory structure if it doesn't exist."""
        upload_dir = f"uploads/talent_profiles/{talent_id}/profile_pictures"
        os.makedirs(upload_dir, exist_ok=True)
        return upload_dir
    
    def _save_profile_picture(self, file: UploadFile, talent_id: int) -> str:
        """Save uploaded profile picture and return the file path."""
        # Create directory structure
        upload_dir = self._create_profile_picture_directory(talent_id)
        
        # Get file extension
        file_extension = ""
        if file.filename and "." in file.filename:
            file_extension = "." + file.filename.split(".")[-1]
        
        # Generate unique filename to avoid overwriting
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        return file_path
    
    def _validate_image_file(self, file: UploadFile) -> None:
        """Validate uploaded image file."""
        # Check file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only JPEG, PNG, and GIF images are allowed."
            )
        
        # Check file size (5MB limit)
        file.file.seek(0, 2)  # Seek to end of file
        file_size = file.file.tell()
        file.file.seek(0)  # Reset file pointer
        
        max_size = 5 * 1024 * 1024  # 5MB in bytes
        if file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File size too large. Maximum allowed size is 5MB."
            )
    
    def _delete_old_profile_pictures(self, old_pic_path: str) -> None:
        """Delete all old profile pictures for a talent."""
        if os.path.exists(old_pic_path):
            os.remove(old_pic_path)
    
    def upload_profile_picture(self, talent_id: int, file: UploadFile) -> TalentProfile:
        """Upload a profile picture for a talent.
        
        Args:
            talent_id: The talent profile ID
            file: The uploaded image file
            
        Returns:
            The updated talent profile
            
        Raises:
            HTTPException: If talent profile not found or file validation fails
        """
        # Validate the image file
        self._validate_image_file(file)
        
        # Check if talent exists
        existing_talent = self.talent_repository.get_talent_by_id(talent_id)
        if not existing_talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        
        # Capture old data before update
        old_data = existing_talent.model_dump() if hasattr(existing_talent, 'model_dump') else {}
        
        # Save the uploaded file
        file_path = self._save_profile_picture(file, talent_id)
        
        # Update talent profile with new picture path
        existing_talent.pic = file_path
        result = self.talent_repository.update_talent_profile(existing_talent)
        
        # Track the activity
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            
            # Get changed data using utility function
            changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
            
            # Only track if there are actual changes
            if changed_old_data or changed_new_data:
                track_talent_activity_sync(
                    talent_profile_id=result.id,
                    activity_type="UPDATE",
                    activity_description="Profile picture uploaded",
                    old_data=changed_old_data,
                    new_data=changed_new_data,
                    current_user=self.current_user
                )
        
        return result
    
    def update_profile_picture(self, talent_id: int, file: UploadFile) -> TalentProfile:
        """Update the profile picture for a talent.
        
        Args:
            talent_id: The talent profile ID
            file: The new uploaded image file
            
        Returns:
            The updated talent profile
            
        Raises:
            HTTPException: If talent profile not found or file validation fails
        """
        # Validate the image file
        self._validate_image_file(file)
        
        # Check if talent exists
        existing_talent = self.talent_repository.get_talent_by_id(talent_id)
        if not existing_talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        
        # Save the new uploaded file
        file_path = self._save_profile_picture(file, talent_id)

        # delete all old profile pictures except the new one
        if existing_talent.pic:
            self._delete_old_profile_pictures(existing_talent.pic)
        
        # Update talent profile with new picture path
        existing_talent.pic = file_path
        self.talent_repository.update_talent_profile(existing_talent)
        
        track_talent_activity_sync(
            talent_profile_id=talent_id,
            activity_type="UPDATE",
            activity_description="Profile picture updated",
            current_user=self.current_user
        )
        
        return existing_talent


    def deactivate_talent_profile(self, talent_id: int, deactivate_talent: DeactivateTalent):
        """Deactivate a talent profile.
        
        Args:
            talent_id: The talent profile ID
            deactivate_talent: The deactivation data
            
        Returns:
            The updated talent profile
            
        Raises:
            HTTPException: If talent profile not found
        """
        # Check if talent exists
        existing_talent = self.talent_repository.get_talent_by_id(talent_id)
        if not existing_talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
            
        # Capture old data before update
        old_data = existing_talent
        
        # Update talent profile with deactivation data
        existing_talent.is_active = deactivate_talent.is_active
        existing_talent.deactivation_reason = deactivate_talent.reason
        self.talent_repository.update_talent_profile(existing_talent)
        
        # Only track if there are actual changes
        if old_data.is_active != existing_talent.is_active:
            track_talent_activity_sync(
                talent_profile_id=talent_id,
                activity_type="UPDATE",
                activity_description="Profile deactivated" if existing_talent.is_active else "Profile reactivated",
                old_data={"status": old_data.is_active},
                new_data={"status": existing_talent.is_active, "reason": existing_talent.deactivation_reason},
                current_user=self.current_user
            )
        
        return existing_talent
