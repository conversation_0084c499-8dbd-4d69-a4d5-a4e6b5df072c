"""Service layer for managing talent chronic conditions."""

from typing import Annotated, Optional

from fastapi import Depends

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import TalentCronicConditions, User
from app.repositories.talent import TalentChronicConditionsRepository
from app.schemas.talent.chronic_conditions_schema import ChronicConditionCreate


class TalentChronicConditionsService:
    """Service class for talent chronic conditions business logic."""
    
    def __init__(
        self,
        chronic_conditions_repository: Annotated[TalentChronicConditionsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            chronic_conditions_repository: Repository for chronic conditions
            current_user: Currently authenticated user
        """
        self.chronic_conditions_repository = chronic_conditions_repository
        self.current_user = current_user

    def create_or_update_chronic_condition(
        self, chronic_condition_req: ChronicConditionCreate
    ) -> TalentCronicConditions:
        """Create a new chronic condition.
        
        Args:
            chronic_condition: The chronic condition data to create
            
        Returns:
            The created chronic condition
        """
        # Check if condition already exists
        existing = self.chronic_conditions_repository.get_chronic_conditions_by_talent_id(chronic_condition_req.talent_profile_id)
        
        if existing:
            # Capture old data for activity tracking
            old_data = existing.model_dump() if hasattr(existing, 'model_dump') else {}
            
            # Update existing condition
            existing.cronic_condition = chronic_condition_req.chronic_condition or ""
            existing.medication = chronic_condition_req.medication
            result = self.chronic_conditions_repository.create_or_update_chronic_condition(existing)
            
            # Track activity for chronic condition update
            if result:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=chronic_condition_req.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Chronic condition updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
            
            return result

        # Create new chronic condition
        data = TalentCronicConditions(
            talent_profile_id=chronic_condition_req.talent_profile_id,
            cronic_condition=chronic_condition_req.chronic_condition or "",
            medication=chronic_condition_req.medication or "",
        )

        result = self.chronic_conditions_repository.create_or_update_chronic_condition(data)
        
        # Track activity for chronic condition creation
        if result:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else chronic_condition_req.model_dump()
            track_talent_activity_sync(
                talent_profile_id=chronic_condition_req.talent_profile_id,
                activity_type="CREATE",
                activity_description="Chronic condition created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result

    def get_chronic_conditions_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentCronicConditions]:
        """Get all chronic conditions for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of chronic conditions
        """
        return self.chronic_conditions_repository.get_chronic_conditions_by_talent_id(talent_id)
    