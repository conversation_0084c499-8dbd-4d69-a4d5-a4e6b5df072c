"""Vacation mapping service for talent management.

This module contains the service class for vacation mapping business logic.
"""

from typing import Annotated, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import User
from app.repositories.talent.vacation_repository import VacationRepository
from app.schemas.talent.vacation_schema import (
    TalentVacationMappingCreate,
    TalentVacationMappingResponse
)
from app.core.logs import log_error_with_context


class VacationService:
    """Service class for vacation mapping business logic.
    
    Handles all business operations related to vacation mapping.
    """
    
    def __init__(
        self, 
        repository: Annotated[VacationRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a repository and current user.
        
        Args:
            repository: Injected vacation repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_or_update_vacation_mapping(
        self, 
        vacation_data: TalentVacationMappingCreate
    ):
        """Create new vacation mapping for a talent.
        
        Args:
            vacation_data: Vacation mapping data to create
            
        Returns:
            Created vacation mapping response or None if creation fails
        """
        try:
            # Check if vacation mapping already exists for this talent and year
            
            db_vacation = self.repository.get_by_talent_id(vacation_data.talent_profile_id)

            if db_vacation:
                # Capture old data for activity tracking
                old_data = db_vacation.model_dump() if hasattr(db_vacation, 'model_dump') else {}
                
                for field in db_vacation.model_fields:
                    if hasattr(vacation_data, field) and field != "id":
                        setattr(db_vacation, field, getattr(vacation_data, field))
                result = self.repository.create(db_vacation)
                
                # Track activity for vacation mapping update
                if result:
                    new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                    changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                    
                    if changed_old_data or changed_new_data:
                        track_talent_activity_sync(
                            talent_profile_id=vacation_data.talent_profile_id,
                            activity_type="UPDATE",
                            activity_description="Vacation mapping updated",
                            old_data=changed_old_data,
                            new_data=changed_new_data,
                            current_user=self.current_user
                        )
                
                return result
            else:
                result = self.repository.create(vacation_data)
                
                # Track activity for vacation mapping creation
                if result:
                    new_data = result.model_dump() if hasattr(result, 'model_dump') else vacation_data.model_dump()
                    track_talent_activity_sync(
                        talent_profile_id=vacation_data.talent_profile_id,
                        activity_type="CREATE",
                        activity_description="Vacation mapping created",
                        new_data=new_data,
                        current_user=self.current_user
                    )
                
                return result
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_or_update_vacation_mapping",
                    "talent_profile_id": vacation_data.talent_profile_id
                }
            )
            raise e
    
    def get_vacation_mapping_by_id(self, talent_profile_id: int) -> Optional[TalentVacationMappingResponse]:
        """Get vacation mapping by ID.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            Vacation mapping response or None if not found
        """
        try:
            db_vacation = self.repository.get_by_talent_id(talent_profile_id)
            if db_vacation:
                return TalentVacationMappingResponse.model_validate(db_vacation.model_dump())
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_vacation_mapping_by_id",
                    "vacation_id": talent_profile_id
                }
            )
            raise e
    
    