"""Payroll information service for talent management.

This module contains the service class for payroll information business logic.
"""

from typing import Annotated, List, Optional, Dict, Any
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db.models import User
from app.repositories.talent import PayrollRepository
from app.schemas.talent.payroll_schema import (
    TalentPayrollInformationCreate,
    TalentPayrollInformationUpdate,
    TalentPayrollInformationResponse
)
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync


class PayrollService:
    """Service class for payroll information business logic.
    
    Handles all business operations related to payroll information.
    """
    
    def __init__(
        self, 
        repository: Annotated[PayrollRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            session: SQLModel database session
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_payroll_information(
        self, 
        payroll_data: TalentPayrollInformationCreate
    ):
        """Create new payroll information for a talent.
        
        Args:
            payroll_data: Payroll information data to create
            
        Returns:
            Created payroll information response or None if creation fails
        """
        try:
            # Check if payroll information already exists for this talent
            if self.repository.exists_by_talent_id(payroll_data.talent_profile_id):
                log_error_with_context(
                    error=ValueError("Payroll information already exists for this talent"),
                    context={
                        "talent_profile_id": payroll_data.talent_profile_id,
                        "operation": "create_payroll_information"
                    }
                )
            
            result = self.repository.create(payroll_data)
            
            # Track the activity
            if result and hasattr(result, 'id') and result.id:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                track_talent_activity_sync(
                    talent_profile_id=payroll_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Payroll information created",
                    new_data=new_data,
                    current_user=self.current_user
                )
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_payroll_information",
                    "talent_profile_id": payroll_data.talent_profile_id
                }
            )
            raise e
    
    def get_payroll_information_by_id(self, payroll_id: int) -> Optional[TalentPayrollInformationResponse]:
        """Get payroll information by ID.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            Payroll information response or None if not found
        """
        try:
            db_payroll = self.repository.get_by_id(payroll_id)
            if db_payroll:
                return TalentPayrollInformationResponse.model_validate(db_payroll)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_payroll_information_by_id",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def get_payroll_information_by_talent_id(
        self, 
        talent_profile_id: int
    ):
        """Get payroll information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Payroll information response or None if not found
        """
        try:
            return self.repository.get_by_talent_id(talent_profile_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_payroll_information_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def update_payroll_information(
        self, 
        payroll_id: int, 
        payroll_data: TalentPayrollInformationUpdate
    ):
        """Update payroll information.
        
        Args:
            payroll_id: Payroll information ID
            payroll_data: Updated payroll information data
            
        Returns:
            Updated payroll information response or None if update fails
        """
        try:
            # Check if payroll information exists
            existing_payroll = self.repository.get_by_id(payroll_id)

            if not existing_payroll:
                log_error_with_context(
                    error=ValueError("Payroll information not found"),
                    context={
                        "payroll_id": payroll_id,
                        "operation": "update_payroll_information"
                    }
                )
                raise ValueError("Payroll information not found")

            # Get old data before update
            old_data = existing_payroll.model_dump() if hasattr(existing_payroll, 'model_dump') else {}

            # Update fields
            update_data = payroll_data.model_dump()
            for field, value in update_data.items():
                if value is not None:
                    setattr(existing_payroll, field, value)
            
            result = self.repository.update(payroll_id, existing_payroll)
            
            # Track the activity with only changed fields
            if result and hasattr(result, 'id') and result.id:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                
                # Compare old and new data to get only changed fields
                changed_old_data: Dict[str, Any] = {}
                changed_new_data: Dict[str, Any] = {}
                
                # Find fields that have changed
                all_keys = set(old_data.keys()) | set(new_data.keys())
                
                for key in all_keys:
                    if key == 'updated_at':
                        continue
                    old_value = old_data.get(key) if key in old_data else None
                    new_value = new_data.get(key) if key in new_data else None
                    
                    # Include field if values are different
                    if old_value is not None and new_value is not None and old_value != new_value:
                        changed_old_data[key] = old_value
                        changed_new_data[key] = new_value
                
                # Only track activity if there are actual changes
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=result.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Payroll information updated",
                        new_data=changed_new_data,
                        old_data=changed_old_data,
                        current_user=self.current_user
                    )
            
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_payroll_information",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def delete_payroll_information(self, payroll_id: int) -> bool:
        """Delete payroll information.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Check if payroll information exists
            existing_payroll = self.repository.get_by_id(payroll_id)
            if not existing_payroll:
                log_error_with_context(
                    error=ValueError("Payroll information not found"),
                    context={
                        "payroll_id": payroll_id,
                        "operation": "delete_payroll_information"
                    }
                )
                return False
            
            return self.repository.delete(payroll_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_payroll_information",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def validate_payroll_information(self, payroll_data: TalentPayrollInformationCreate) -> List[str]:
        """Validate payroll information data.
        
        Args:
            payroll_data: Payroll information data to validate
            
        Returns:
            List of validation error messages
        """
        errors: List[str] = []
        
        try:
            # Validate IMMS format (basic validation - should be numeric)
            if payroll_data.imms and not payroll_data.imms.isdigit():
                errors.append("IMMS number must contain only digits")
            
            # Validate CURP format (18 characters for Mexican CURP)
            if payroll_data.curp:
                if len(payroll_data.curp) != 18:
                    errors.append("CURP must be exactly 18 characters")
                
                if not payroll_data.curp.isalnum():
                    errors.append("CURP must contain only alphanumeric characters")
            
            # Validate RFC format (12 or 13 characters for Mexican RFC)
            if payroll_data.rfc:
                rfc_len = len(payroll_data.rfc)
                if rfc_len not in [12, 13]:
                    errors.append("RFC must be 12 or 13 characters long")
                
                if not payroll_data.rfc.isalnum():
                    errors.append("RFC must contain only alphanumeric characters")
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "validate_payroll_information",
                    "talent_profile_id": payroll_data.talent_profile_id
                }
            )
            errors.append("Validation error occurred")
        
        return errors
