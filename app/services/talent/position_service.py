"""Position service for talent management.

This module contains the service class for talent position business logic operations."""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.position_repository import TalentPositionRepository
from app.schemas.talent import (
    TalentPositionCreate,
    TalentPositionResponse
)
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data


class TalentPositionService:
    """Service class for talent position business logic.
    
    Handles all business operations related to talent positions.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentPositionRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_position(self, position_data: TalentPositionCreate):
        """Create a new talent position.
        
        Args:
            position_data: Position data to create
            
        Returns:
            Created position response or None if creation fails
        """
        try:
            exists = self.repository.get_by_talent_id(position_data.talent_profile_id)

            if not position_data.location_duration:
                position_data.location_duration = None
            
            if exists:
                # Capture old data for activity tracking
                old_data = exists.model_dump()
                
                for field, value in position_data.model_dump(exclude_unset=True).items():
                    if hasattr(exists, field) and field != "id":
                        if value:
                            setattr(exists, field, value)
                        else:
                            setattr(exists, field, None)
                
                updated_position = self.repository.create_or_update(exists)
                if updated_position:
                    # Get changed data and track activity
                    new_data = updated_position.model_dump()
                    changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                    
                    if changed_old_data or changed_new_data:
                        track_talent_activity_sync(
                            talent_profile_id=position_data.talent_profile_id,
                            activity_type="UPDATE",
                            activity_description="Position information updated",
                            old_data=changed_old_data,
                            new_data=changed_new_data,
                            current_user=self.current_user
                        )
            else:
                for field, value in position_data.model_dump(exclude_unset=True).items():
                    if hasattr(position_data, field) and field != "id":
                        if value:
                            setattr(position_data, field, value)
                        else:
                            setattr(position_data, field, None)
                
                created_position = self.repository.create_or_update(position_data)
                if created_position:
                    # Track creation activity
                    track_talent_activity_sync(
                        talent_profile_id=position_data.talent_profile_id,
                        activity_type="CREATE",
                        activity_description="Position information created",
                        new_data=created_position.model_dump(),
                        current_user=self.current_user
                    )
            
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_position",
                    "service": "TalentPositionService",
                    "data": position_data.model_dump()
                }
            )
            return None
    
    def get_position_by_id(self, position_id: int) -> Optional[TalentPositionResponse]:
        """Get a talent position by ID.
        
        Args:
            position_id: Position ID
            
        Returns:
            Position response or None if not found
        """
        try:
            db_position = self.repository.get_by_id(position_id)
            if db_position:
                return TalentPositionResponse.model_validate(db_position)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_position_by_id",
                    "service": "TalentPositionService",
                    "data": {"position_id": position_id}
                }
            )
            return None
    
    def get_positions_by_talent_id(self, talent_profile_id: int) -> TalentPositionResponse | None:
        """Get all positions for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of position responses for the talent
        """
        try:
            db_position = self.repository.get_by_talent_id(talent_profile_id)
            if db_position:
                return TalentPositionResponse.model_validate(db_position.model_dump())
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_positions_by_talent_id",
                    "service": "TalentPositionService",
                    "data": {"talent_profile_id": talent_profile_id}
                }
            )
            return None
    
    def get_all_positions(self, skip: int = 0, limit: int = 100) -> List[TalentPositionResponse]:
        """Get all talent positions with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of position responses
        """
        try:
            db_positions = self.repository.get_all(skip=skip, limit=limit)
            return [TalentPositionResponse.model_validate(position) for position in db_positions]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_positions",
                    "service": "TalentPositionService",
                    "data": {"skip": skip, "limit": limit}
                }
            )
            return []
