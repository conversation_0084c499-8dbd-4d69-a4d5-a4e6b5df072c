import os
import uuid
import base64
from typing import Annotated, List, Optional
from fastapi import Depends, HTTPException, status, UploadFile

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.db import TalentDocumentsCollected, User
from app.repositories.talent import TalentDocumentsRepository
from app.schemas.talent.documents_schema import TalentDocumentResponse


class TalentDocumentsService:
    def __init__(
        self,
        documents_repository: Annotated[TalentDocumentsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.documents_repository = documents_repository
        self.current_user = current_user

    def _convert_to_response_objects(self, documents: List[TalentDocumentsCollected], include_content: bool = False) -> List[TalentDocumentResponse]:
        """Convert TalentDocumentsCollected objects to TalentDocumentResponse objects with computed size and optionally base64 content."""
        response_documents: List[TalentDocumentResponse] = []
        
        for document in documents:
            # Calculate file size in MB
            size_mb = None
            base64_content = None
            
            try:
                if document.url and os.path.exists(document.url):
                    size_mb = os.path.getsize(document.url)
                    
                    # Include base64 content if requested
                    if include_content:
                        with open(document.url, "rb") as file:
                            file_content = file.read()
                            base64_content = base64.b64encode(file_content).decode('utf-8')
            except (OSError, FileNotFoundError):
                # If file doesn't exist or can't be accessed, size and content remain None
                pass
            
            # Create response object with computed size and optional content
            response_document = TalentDocumentResponse(
                talent_profile_id=document.talent_profile_id,
                doc_type=document.doc_type,
                url=document.url,
                size=size_mb,
                base64_content=base64_content
            )
            response_documents.append(response_document)
        
        return response_documents

    def get_talent_documents(self, talent_id: int) -> List[TalentDocumentResponse]:
        """Get all documents for a specific talent."""
        documents = self.documents_repository.get_documents_by_talent_id(talent_id)
        return self._convert_to_response_objects(documents, include_content=True)

    def get_document_by_id(self, document_id: int) -> Optional[TalentDocumentsCollected]:
        """Get a specific document by ID."""
        document = self.documents_repository.get_document_by_id(document_id)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        return document

    def _create_upload_directory(self, talent_id: int, doc_type: str) -> str:
        """Create upload directory structure if it doesn't exist."""
        upload_dir = f"uploads/talent_docs/{talent_id}/{doc_type}"
        os.makedirs(upload_dir, exist_ok=True)
        return upload_dir

    def _save_uploaded_file(self, file: UploadFile, talent_id: int, doc_type: str) -> str:
        """Save uploaded file and return the file path."""
        # Create directory structure
        upload_dir = self._create_upload_directory(talent_id, doc_type)
        
        # Get file extension
        file_extension = ""
        if file.filename and "." in file.filename:
            file_extension = "." + file.filename.split(".")[-1]
        
        # Generate unique filename to avoid overwriting
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        return file_path

    def create_talent_document(
        self, 
        talent_id: int, 
        doc_type: str, 
        file: UploadFile
    ) -> TalentDocumentsCollected:
        """Create a new talent document with file upload."""
        # Save the uploaded file
        file_path = self._save_uploaded_file(file, talent_id, doc_type)
        
        document = TalentDocumentsCollected(
            talent_profile_id=talent_id,
            doc_type=doc_type,
            url=file_path
        )
        created_document = self.documents_repository.create_document(document)
        
        # Track activity for document creation
        new_data = created_document.model_dump() if hasattr(created_document, 'model_dump') else None
        track_talent_activity_sync(
            talent_profile_id=talent_id,
            activity_type="CREATE",
            activity_description="Document uploaded",
            new_data=new_data,
            current_user=self.current_user
        )
        
        return created_document

    def get_documents_by_type(self, talent_id: int, doc_type: str) -> List[TalentDocumentResponse]:
        """Get documents by talent ID and document type."""
        documents = self.documents_repository.get_documents_by_type(talent_id, doc_type)
        return self._convert_to_response_objects(documents)
