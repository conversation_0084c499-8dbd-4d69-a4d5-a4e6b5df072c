"""Service layer for managing talent ongoing health issues."""

from typing import Annotated, Optional

from fastapi import Depends

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import TalentOngoingHealthIssues, User
from app.repositories.talent import Talent<PERSON>ngoingHealthRepository
from app.schemas.talent.ongoing_health_schema import OngoingHealthIssueCreate


class TalentOngoingHealthService:
    """Service class for talent ongoing health issues business logic."""
    
    def __init__(
        self,
        repository: Annotated[TalentOngoingHealthRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            repository: The repository for ongoing health issues
            current_user: Currently authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_or_update_ongoing_health_issue(
        self, ongoing_health_issue_req: OngoingHealthIssueCreate
    ) -> TalentOngoingHealthIssues:
        """Create a new ongoing health issue.
        
        Args:
            ongoing_health_issue: The ongoing health issue data to create
            
        Returns:
            The created ongoing health issue
        """
        existing = self.repository.get_ongoing_health_issues_by_talent_id(ongoing_health_issue_req.talent_profile_id)

        if existing:
            # Capture old data for activity tracking
            old_data = existing.model_dump() if hasattr(existing, 'model_dump') else {}
            
            existing.medication = ongoing_health_issue_req.medication
            existing.ongoing_health_issues = ongoing_health_issue_req.ongoing_health_issues
            result = self.repository.create_or_update_ongoing_health_issue(existing)
            
            # Track activity for ongoing health issue update
            if result:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=ongoing_health_issue_req.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Ongoing health issues updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
            
            return result

        data = TalentOngoingHealthIssues(
            talent_profile_id=ongoing_health_issue_req.talent_profile_id,
            medication=ongoing_health_issue_req.medication,
            ongoing_health_issues=ongoing_health_issue_req.ongoing_health_issues,
        )
        result = self.repository.create_or_update_ongoing_health_issue(data)
        
        # Track activity for ongoing health issue creation
        if result:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else ongoing_health_issue_req.model_dump()
            track_talent_activity_sync(
                talent_profile_id=ongoing_health_issue_req.talent_profile_id,
                activity_type="CREATE",
                activity_description="Ongoing health issues created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result

    def get_ongoing_health_issues_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentOngoingHealthIssues]:
        """Get all ongoing health issues for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of ongoing health issues
        """
        return self.repository.get_ongoing_health_issues_by_talent_id(talent_id)
