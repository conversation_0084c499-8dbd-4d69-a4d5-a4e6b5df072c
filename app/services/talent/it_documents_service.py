"""Service for talent IT documents information business logic."""

import base64
import os
from typing import Annotated, List, Optional
import uuid

from fastapi import Depends, UploadFile

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db.models import User, TalentITDocumentsMapping
from app.repositories.talent.it_documents_repository import ITDocumentsRepository
from app.schemas.talent import (
    TalentITDocumentsUpdate,
)
from app.schemas.talent.it_documents_schema import TalentITDocumentsResponse


class ITDocumentsService:
    """Service for talent IT documents information business logic."""

    def __init__(
        self,
        repository: Annotated[ITDocumentsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            repository: IT documents repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user


    def _create_profile_picture_directory(self, talent_id: int, document_type: str) -> str:
        """Create profile picture directory structure if it doesn't exist."""
        upload_dir = f"uploads/talent_it_docs/{talent_id}/{document_type}"
        os.makedirs(upload_dir, exist_ok=True)
        return upload_dir
    
    def _save_profile_picture(self, file: UploadFile, talent_id: int, document_type: str) -> str:
        """Save uploaded profile picture and return the file path."""
        # Create directory structure
        upload_dir = self._create_profile_picture_directory(talent_id, document_type)
        
        # Get file extension
        file_extension = ""
        if file.filename and "." in file.filename:
            file_extension = "." + file.filename.split(".")[-1]
        
        # Generate unique filename to avoid overwriting
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        return file_path

    def create_it_document(self, talent_profile_id: int, document_type: str, file: UploadFile, notes: Optional[str] = None) -> TalentITDocumentsMapping:
        """Create a new IT document record.
        
        Args:
            talent_profile_id: The talent profile ID
            document_type: The IT document type
            file: Uploaded file

        Returns:
            Created IT document record
            
        Raises:
            Exception: If creation fails
        """
        url = ''
        if file:
            url = self._save_profile_picture(file, talent_profile_id, document_type)
            
        result = self.repository.create_it_document(talent_profile_id, document_type, url, notes)
        
        # Track the activity
        if result and hasattr(result, 'id') and result.id:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            track_talent_activity_sync(
                talent_profile_id=result.talent_profile_id,
                activity_type="CREATE",
                activity_description="IT document created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result

    def get_it_document_by_id(self, it_document_id: int) -> Optional[TalentITDocumentsMapping]:
        """Get IT document by ID.
        
        Args:
            it_document_id: IT document ID
            
        Returns:
            IT document record or None if not found
        """
        return self.repository.get_it_document_by_id(it_document_id)

    def get_it_documents_by_talent_id(self, talent_id: int) -> List[TalentITDocumentsResponse]:
        """Get all IT documents for a specific talent.
        
        Args:
            talent_id: Talent profile ID
            
        Returns:
            List of IT document records
        """
        data: List[TalentITDocumentsMapping] = self.repository.get_it_documents_by_talent_id(talent_id)
        response_data: List[TalentITDocumentsResponse] = []
        for item in data:
            size_mb = None
            base64_content = ''
            try:
                if item.url and os.path.exists(item.url):
                    size_mb = os.path.getsize(item.url)
                    with open(item.url, "rb") as file:
                        file_content = file.read()
                        base64_content = base64.b64encode(file_content).decode('utf-8')
            except (OSError, FileNotFoundError):
                # If file doesn't exist or can't be accessed, size and content remain None
                pass
            response_data.append(TalentITDocumentsResponse(
                id=item.id,
                created_at=item.created_at,
                updated_at=item.updated_at,
                talent_profile_id=item.talent_profile_id,
                document_type=item.document_type,
                url='data:image/png;base64,'+base64_content,
                size=size_mb,
                notes=item.notes,
            ))
        return response_data

    def update_it_document(
        self, 
        it_document_id: int, 
        it_document_data: TalentITDocumentsUpdate
    ) -> Optional[TalentITDocumentsMapping]:
        """Update an existing IT document record.
        
        Args:
            it_document_id: IT document ID to update
            it_document_data: Updated IT document data
            
        Returns:
            Updated IT document record or None if not found
            
        Raises:
            Exception: If update fails
        """
        # Get the original record for activity tracking
        original_record = self.repository.get_it_document_by_id(it_document_id)
        if not original_record:
            return None
            
        # Update the record
        result = self.repository.update_it_document(it_document_id, it_document_data)
        
        # Track the activity if update was successful
        if result:
            old_data = original_record.model_dump() if hasattr(original_record, 'model_dump') else {}
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            changed_data = get_changed_data(old_data, new_data)
            
            if changed_data:
                track_talent_activity_sync(
                    talent_profile_id=result.talent_profile_id,
                    activity_type="UPDATE",
                    activity_description="IT document updated",
                    old_data=changed_data[0],
                    new_data=changed_data[1],
                    current_user=self.current_user
                )
        
        return result

    def delete_it_document(self, it_document_id: int) -> bool:
        """Delete an IT document record.
        
        Args:
            it_document_id: IT document ID to delete
            
        Returns:
            True if deleted successfully, False if not found
            
        Raises:
            Exception: If deletion fails
        """
        # Get the original record for activity tracking
        original_record = self.repository.get_it_document_by_id(it_document_id)
        if not original_record:
            return False
            
        # Delete the record
        result = self.repository.delete_it_document(it_document_id)
        
        # Track the activity if deletion was successful
        if result:
            old_data = original_record.model_dump() if hasattr(original_record, 'model_dump') else {}
            track_talent_activity_sync(
                talent_profile_id=original_record.talent_profile_id,
                activity_type="DELETE",
                activity_description="IT document deleted",
                old_data=old_data,
                current_user=self.current_user
            )
        
        return result
