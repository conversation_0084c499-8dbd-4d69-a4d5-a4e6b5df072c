"""Client information service for talent management.

This module contains the service class for talent client information business logic.
"""

from typing import Annotated
from fastapi import Depends, HTTPException, status
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.client_info_repository import TalentClientInfoRepository
from app.schemas.talent.client_info_schema import (
    TalentClientInfoCreate,
    TalentClientInfoResponse
)
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data


class TalentClientInfoService:
    """Service class for talent client information business logic.
    
    Handles all business operations related to talent client information.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentClientInfoRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_client_info(
        self, 
        client_info_data: TalentClientInfoCreate
    ):
        """Create new client information for a talent.
        
        Args:
            client_info_data: Client information data to create
            
        Returns:
            Created client information response
        """
        try:
            # Check if client information already exists for this talent
            for field, value in client_info_data.model_dump().items():
              if not value:
                setattr(client_info_data, field, None)
            # Create new client information
            new_client_info = self.repository.create(client_info_data)
            
            # Track activity for client info creation
            if new_client_info:
                track_talent_activity_sync(
                    talent_profile_id=client_info_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Client information created",
                    new_data=new_client_info.model_dump() if hasattr(new_client_info, 'model_dump') else None,
                    current_user=self.current_user
                )
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_client_info",
                    "talent_profile_id": client_info_data.talent_profile_id
                }
            )
            raise e
    
    def get_client_info_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> list[TalentClientInfoResponse]:
        """Get client information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Client information response or None if not found
        """
        try:
            db_client_info = self.repository.get_by_talent_id(talent_profile_id)
            if db_client_info:
                return [TalentClientInfoResponse.model_validate(item.model_dump()) for item in db_client_info]  
            return []
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_info_by_talent_id",
                    "talent_profile_id": talent_profile_id,
                    "user_id": self.current_user.id
                }
            )
            raise e

    def update_client_info(
        self,
        client_info_id: int,
        client_info_data: TalentClientInfoCreate
    ):
        """Update client information for a specific talent.
        
        Args:
            client_info_id: The client information ID
            client_info_data: The client information data to update
        """
        try:
            db_client_info = self.repository.get_by_id(client_info_id)
            if not db_client_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Client information not found",
                )
            # Capture old data for activity tracking
            old_data = db_client_info.model_dump() if hasattr(db_client_info, 'model_dump') else None
            
            for field, value in client_info_data.model_dump().items():
              if hasattr(db_client_info, field) and value:
                setattr(db_client_info, field, value)
            
            updated_client_info = self.repository.update(client_info_id, db_client_info)
            
            # Track activity for client info update
            if updated_client_info:
                new_data = updated_client_info.model_dump() if hasattr(updated_client_info, 'model_dump') else None
                
                if old_data and new_data:
                    changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                    
                    if changed_old_data or changed_new_data:
                        track_talent_activity_sync(
                            talent_profile_id=db_client_info.talent_profile_id,
                            activity_type="UPDATE",
                            activity_description="Client information updated",
                            old_data=changed_old_data,
                            new_data=changed_new_data,
                            current_user=self.current_user
                        )
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_client_info",
                    "client_info_id": client_info_id,
                    "user_id": self.current_user.id
                }
            )
            raise e
