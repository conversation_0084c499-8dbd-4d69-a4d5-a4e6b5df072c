"""Service layer for managing talent allergies."""

from typing import Annotated, Optional

from fastapi import Depends

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import TalentAllergies, User
from app.repositories.talent import TalentAllergiesRepository
from app.schemas.talent.allergies_schema import AllergiesCreate


class TalentAllergiesService:
    """Service class for talent allergies business logic."""
    
    def __init__(
        self,
        allergies_repository: Annotated[TalentAllergiesRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            allergies_repository: Repository for allergies
            current_user: Currently authenticated user
        """
        self.allergies_repository = allergies_repository
        self.current_user = current_user

    def get_allergy_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentAllergies]:
        """Get an allergy for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            Allergy record
        """
        return self.allergies_repository.get_allergy_by_talent_id(talent_id)

    def update_or_create_allergy(
        self, allergy_data: AllergiesCreate
    ) -> TalentAllergies:
        """Update an existing allergy.
        
        Args:
            allergy_id: The allergy ID to update
            allergy_data: The updated allergy data
            
        Returns:
            The updated allergy
            
        Raises:
            HTTPException: If allergy not found
        """
        # First get the existing allergy
        existing_allergy = self.get_allergy_by_talent_id(allergy_data.talent_profile_id)
        
        if not existing_allergy:
            # Create a new allergy if none exists
            allergy = TalentAllergies(
                talent_profile_id=allergy_data.talent_profile_id,
                allergy=allergy_data.allergy or "",
                medication=allergy_data.medication or "",
            )
            result = self.allergies_repository.create_allergy(allergy)
            
            # Track activity for allergy creation
            if result:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else allergy_data.model_dump()
                track_talent_activity_sync(
                    talent_profile_id=allergy_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Allergy information created",
                    new_data=new_data,
                    current_user=self.current_user
                )
            
            return result
        
        # Capture old data for activity tracking
        old_data = existing_allergy.model_dump() if hasattr(existing_allergy, 'model_dump') else {}
        
        # Update only the provided fields
        if allergy_data.allergy is not None:
            existing_allergy.allergy = allergy_data.allergy
        if allergy_data.medication is not None:
            existing_allergy.medication = allergy_data.medication
            
        result = self.allergies_repository.update_allergy(existing_allergy)
        
        # Track activity for allergy update
        if result:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
            changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
            
            if changed_old_data or changed_new_data:
                track_talent_activity_sync(
                    talent_profile_id=allergy_data.talent_profile_id,
                    activity_type="UPDATE",
                    activity_description="Allergy information updated",
                    old_data=changed_old_data,
                    new_data=changed_new_data,
                    current_user=self.current_user
                )
        
        return result
