"""Talent equipment mapping service for talent management.

This module contains the service class for talent equipment mapping business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.master.equipment_repository import EquipmentRepository
from app.repositories.talent.equipment_mapping_repository import TalentEquipmentMappingRepository
from app.schemas.master.equipment_schema import MasterEquipmentResponse
from app.schemas.talent.equipment_mapping_schema import (
    TalentEquipmentMappingCreate,
    TalentEquipmentMappingResponse
)
from app.core.logs import log_error_with_context
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data


class TalentEquipmentMappingService:
    """Service class for talent equipment mapping business logic.
    
    Handles all business operations related to talent equipment mapping.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentEquipmentMappingRepository, Depends()],
        equipment_repository: Annotated[EquipmentRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            equipment_repository: Injected equipment repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.equipment_repository = equipment_repository
        self.current_user = current_user

    def get_available_equipment(self) -> List[MasterEquipmentResponse]:
        """Get list of available master equipment.
        
        Returns:
            List of available master equipment
        """
        try:
            return [
                MasterEquipmentResponse.model_validate(equipment.model_dump()) 
                for equipment in self.repository.get_available_equipment()
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_available_equipment"
                }
            )
            raise e
    
    def create_equipment_mapping(
        self, 
        mapping_data: TalentEquipmentMappingCreate
    ):
        """Create new equipment mapping for a talent.
        
        Args:
            mapping_data: Equipment mapping data to create
            
        Returns:
            Created equipment mapping response or None if creation fails
        """
        try:
            # Check if equipment mapping already exists for this talent and equipment
            created_mapping = self.repository.create(mapping_data)
            
            # Track activity for equipment mapping creation
            if created_mapping:
                new_data = created_mapping.model_dump() if hasattr(created_mapping, 'model_dump') else mapping_data.model_dump()
                track_talent_activity_sync(
                    talent_profile_id=mapping_data.talent_profile_id,
                    activity_type="CREATE",
                    activity_description="Equipment mapping created",
                    new_data=new_data,
                    current_user=self.current_user
                )
            
            return created_mapping
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_equipment_mapping",
                    "talent_profile_id": mapping_data.talent_profile_id,
                    "master_equipment_id": mapping_data.master_equipment_id
                }
            )
            raise e
    
    def get_equipment_mapping_by_id(self, mapping_id: int) -> Optional[TalentEquipmentMappingResponse]:
        """Get equipment mapping by ID.
        
        Args:
            mapping_id: Equipment mapping ID
            
        Returns:
            Equipment mapping response or None if not found
        """
        try:
            db_mapping = self.repository.get_by_id(mapping_id)
            if db_mapping:
                return TalentEquipmentMappingResponse.model_validate(db_mapping)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_mapping_by_id",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_equipment_mappings_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> List[TalentEquipmentMappingResponse]:
        """Get equipment mappings for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of equipment mapping responses
        """
        try:
            db_mappings = self.repository.get_by_talent_id(talent_profile_id)
            final_mappings: List[TalentEquipmentMappingResponse] = []
            for db_mapping in db_mappings:
              equipment = self.equipment_repository.get_by_id(db_mapping.master_equipment_id)
              equipment.is_active = db_mapping.is_active
              final_mappings.append(TalentEquipmentMappingResponse(**equipment.model_dump(), master_equipment_id=db_mapping.master_equipment_id, talent_profile_id=db_mapping.talent_profile_id))
            return final_mappings
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_mappings_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def update_equipment_mapping(
        self, 
        mapping_id: int
    ):
        """Update equipment mapping.
        
        Args:
            mapping_id: Equipment mapping ID
            mapping_data: Updated equipment mapping data
            
        Returns:
            Updated equipment mapping response or None if update fails
        """
        try:
            # Check if equipment mapping exists
            existing_mapping = self.repository.get_by_id(mapping_id)

            if not existing_mapping:
                log_error_with_context(
                    error=ValueError("Equipment mapping not found"),
                    context={
                        "mapping_id": mapping_id,
                        "operation": "update_equipment_mapping"
                    }
                )
                raise ValueError("Equipment mapping not found")

            # Capture old data for activity tracking
            old_data = existing_mapping.model_dump() if hasattr(existing_mapping, 'model_dump') else {}
            
            existing_mapping.is_active = not existing_mapping.is_active
            updated_mapping = self.repository.update(existing_mapping)
            
            # Track activity for equipment mapping update
            if updated_mapping:
                new_data = updated_mapping.model_dump() if hasattr(updated_mapping, 'model_dump') else {}
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=existing_mapping.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Equipment mapping updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
            
            existing_equipment = self.equipment_repository.get_by_equipment_id(existing_mapping.master_equipment_id)
            if existing_equipment:
                existing_equipment.is_occupied = not existing_equipment.is_occupied
                self.equipment_repository.update(existing_mapping.master_equipment_id, existing_equipment)
                
            return updated_mapping
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_equipment_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    