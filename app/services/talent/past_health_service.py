"""Service layer for managing talent past health issues."""

from typing import Annotated, Optional

from fastapi import Depends

from app.core.jwt import get_current_user
from app.core.activity_tracker import track_talent_activity_sync
from app.core.utils import get_changed_data
from app.db import Talent<PERSON>astHealthIssues, User
from app.repositories.talent import Talent<PERSON>astHealthRepository
from app.schemas.talent.past_health_schema import PastHealthIssueCreate


class TalentPastHealthService:
    """Service class for talent past health issues business logic."""
    
    def __init__(
        self,
        repository: Annotated[TalentPastHealthRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            repository: Injected repository
            current_user: Currently authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_or_update_past_health_issue(
        self, past_health_issue_req: PastHealthIssueCreate
    ) -> TalentPastHealthIssues:
        """Create a new past health issue.
        
        Args:
            past_health_issue: The past health issue data to create
            
        Returns:
            The created past health issue
        """
        existing = self.repository.get_past_health_issues_by_talent_id(
            past_health_issue_req.talent_profile_id
        )
        if existing:
            # Capture old data for activity tracking
            old_data = existing.model_dump() if hasattr(existing, 'model_dump') else {}
            
            existing.past_health_issues = past_health_issue_req.past_health_issues
            result = self.repository.create_or_update_past_health_issue(existing)
            
            # Track activity for past health issue update
            if result:
                new_data = result.model_dump() if hasattr(result, 'model_dump') else {}
                changed_old_data, changed_new_data = get_changed_data(old_data, new_data)
                
                if changed_old_data or changed_new_data:
                    track_talent_activity_sync(
                        talent_profile_id=past_health_issue_req.talent_profile_id,
                        activity_type="UPDATE",
                        activity_description="Past health issues updated",
                        old_data=changed_old_data,
                        new_data=changed_new_data,
                        current_user=self.current_user
                    )
            
            return result

        data = TalentPastHealthIssues(
            talent_profile_id=past_health_issue_req.talent_profile_id,
            past_health_issues=past_health_issue_req.past_health_issues,
        )
        result = self.repository.create_or_update_past_health_issue(data)
        
        # Track activity for past health issue creation
        if result:
            new_data = result.model_dump() if hasattr(result, 'model_dump') else past_health_issue_req.model_dump()
            track_talent_activity_sync(
                talent_profile_id=past_health_issue_req.talent_profile_id,
                activity_type="CREATE",
                activity_description="Past health issues created",
                new_data=new_data,
                current_user=self.current_user
            )
        
        return result

    def get_past_health_issues_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentPastHealthIssues]:
        """Get all past health issues for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of past health issues
        """
        return self.repository.get_past_health_issues_by_talent_id(talent_id)
