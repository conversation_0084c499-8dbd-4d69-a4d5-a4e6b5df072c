"""Master Client service for business logic."""

from typing import Annotated, List, Sequence
from fastapi import Depends
from app.db.models import User
from app.repositories.master.master_client_repository import MasterClientRepository
from app.repositories.talent import TalentRepository
from app.schemas.master.master_client_schema import (
    MasterClientCreate,
    MasterClientUpdate,
    MasterClientResponse
)
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context
from app.schemas.master.master_client_schema import TalentClientInfoResponse


class MasterClientService:
    """Service for master client business logic."""

    def __init__(
        self,
        repository: Annotated[MasterClientRepository, Depends()],
        talent_repository: Annotated[TalentRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.repository = repository
        self.talent_repository = talent_repository
        self.current_user = current_user
    
    def create_client(self, client_data: Master<PERSON><PERSON>C<PERSON>):
        """Create a new master client."""
        try:
            # Check if client with same name already exists
            existing_client = self.repository.get_by_name(client_data.name)
            if existing_client:
                error = ValueError(f"Master client with name '{client_data.name}' already exists")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "create_client",
                        "name": client_data.name
                    }
                )
                raise error
            
            # Check if client with same client_id already exists
            existing_client_id = self.repository.get_by_client_id(client_data.client_id)
            if existing_client_id:
                error = ValueError(f"Master client with client ID '{client_data.client_id}' already exists")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "create_client",
                        "client_id": client_data.client_id
                    }
                )
                raise error
            
            # Create client
            client_dict = client_data.model_dump()
            for field, value in client_dict.items():
                if not value:
                    client_dict[field] = None
            self.repository.create(client_dict)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_client",
                    "name": client_data.name
                }
            )
            raise e
    
    def get_client_by_id(self, client_id: int) -> MasterClientResponse:
        """Get master client by ID."""
        try:
            client = self.repository.get_by_id(client_id)
            if not client:
                error = ValueError(f"Master client with ID {client_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_client_by_id",
                        "client_id": client_id
                    }
                )
                raise error
            return MasterClientResponse.model_validate(client.model_dump())
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_by_id",
                    "client_id": client_id
                }
            )
            raise e
    
    def get_client_by_name(self, name: str) -> MasterClientResponse:
        """Get master client by name."""
        try:
            client = self.repository.get_by_name(name)
            if not client:
                error = ValueError(f"Master client with name '{name}' not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_client_by_name",
                        "name": name
                    }
                )
                raise error
            return MasterClientResponse.model_validate(client)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_by_name",
                    "name": name
                }
            )
            raise e
    
    def get_client_by_client_id(self, client_id: str) -> MasterClientResponse:
        """Get master client by client_id field."""
        try:
            client = self.repository.get_by_client_id(client_id)
            if not client:
                error = ValueError(f"Master client with client ID '{client_id}' not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_client_by_client_id",
                        "client_id": client_id
                    }
                )
                raise error
            return MasterClientResponse.model_validate(client)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_by_client_id",
                    "client_id": client_id
                }
            )
            raise e
    
    def get_all_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientResponse]:
        """Get all master clients with pagination."""
        try:
            clients = self.repository.get_all(skip=skip, limit=limit)
            final_clients: List[MasterClientResponse] = []
            for client in clients:
                emp_count = self.repository.get_emp_count_by_client_id(client_id=client.id or 0)
                client_dict = client.model_dump()
                client_dict["emp_count"] = emp_count or 0
                final_clients.append(MasterClientResponse(**client_dict))
            return final_clients
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_clients",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def get_active_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientResponse]:
        """Get all active master clients with pagination."""
        try:
            clients = self.repository.get_active_clients(skip=skip, limit=limit)
            return [MasterClientResponse.model_validate(client) for client in clients]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_active_clients",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def get_inactive_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientResponse]:
        """Get all inactive master clients with pagination."""
        try:
            clients = self.repository.get_inactive_clients(skip=skip, limit=limit)
            return [MasterClientResponse.model_validate(client) for client in clients]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_inactive_clients",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def search_clients_by_name(self, name_pattern: str, skip: int = 0, limit: int = 100) -> List[MasterClientResponse]:
        """Search master clients by name pattern."""
        try:
            clients = self.repository.search_by_name(name_pattern, skip=skip, limit=limit)
            return [MasterClientResponse.model_validate(client) for client in clients]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "search_clients_by_name",
                    "name_pattern": name_pattern,
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def update_client(self, client_id: int, client_data: MasterClientUpdate) -> MasterClientResponse:
        """Update master client information."""
        try:
            # Get existing client
            client = self.repository.get_by_id(client_id)
            if not client:
                error = ValueError(f"Master client with ID {client_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "update_client",
                        "client_id": client_id
                    }
                )
                raise error
            
            # Check if new name conflicts with existing client
            if client_data.name and client_data.name != client.name:
                existing_client = self.repository.get_by_name(client_data.name)
                if existing_client:
                    error = ValueError(f"Master client with name '{client_data.name}' already exists")
                    log_error_with_context(
                        error=error,
                        context={
                            "operation": "update_client",
                            "client_id": client_id,
                            "name": client_data.name
                        }
                    )
                    raise error
            
            # Check if new client_id conflicts with existing client
            if client_data.client_id and client_data.client_id != client.client_id:
                existing_client_id = self.repository.get_by_client_id(client_data.client_id)
                if existing_client_id:
                    error = ValueError(f"Master client with client ID '{client_data.client_id}' already exists")
                    log_error_with_context(
                        error=error,
                        context={
                            "operation": "update_client",
                            "client_id": client_id,
                            "new_client_id": client_data.client_id
                        }
                    )
                    raise error
            
            # Update client
            update_dict = client_data.model_dump()
            updated_client = self.repository.update(client, update_dict)
            return MasterClientResponse.model_validate(updated_client)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_client",
                    "client_id": client_id
                }
            )
            raise e
    
    def toggle_client_status(self, client_id: int) -> MasterClientResponse:
        """Toggle master client active status."""
        try:
            client = self.repository.get_by_id(client_id)
            if not client:
                error = ValueError(f"Master client with ID {client_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "toggle_client_status",
                        "client_id": client_id
                    }
                )
                raise error
            
            updated_client = self.repository.toggle_status(client)
            return MasterClientResponse.model_validate(updated_client)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "toggle_client_status",
                    "client_id": client_id
                }
            )
            raise e


    def get_assigned_employees(self, client_id: int) -> List[TalentClientInfoResponse]:
        """Get assigned employees by client id."""
        try:
            employees = self.repository.get_emp_count_by_client_id(client_id, is_count=False)
            filtered_employees: List[TalentClientInfoResponse] = []
            if isinstance(employees, Sequence):
                for emp in employees:
                    if emp.is_active:
                        talent = self.talent_repository.get_talent_by_id(emp.talent_profile_id)
                        if talent:
                            filtered_employees.append(TalentClientInfoResponse(
                                id=emp.talent_profile_id,
                                first_name=talent.first_name,
                                last_name=talent.last_name,
                                position=emp.title or "",
                                start_date=emp.start_date,
                            ))
            return filtered_employees
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_assigned_employees",
                    "client_id": client_id
                }
            )
            raise e
