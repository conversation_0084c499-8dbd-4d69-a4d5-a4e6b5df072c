"""Service for master module management.

This module contains the service class for master module business logic.
"""

from typing import List, Optional, Annotated
from fastapi import Depends, HTTPException, status
from app.repositories.master.module_repository import ModuleRepository
from app.schemas.master.module_schema import MasterModuleResponse
from app.core.jwt import get_current_user
from app.db import User, MasterModule
from app.core.logs import log_error_with_context


class ModuleService:
    """Service class for master module business logic."""
    
    def __init__(
        self,
        module_repository: Annotated[ModuleRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            module_repository: Module repository dependency
            current_user: Current authenticated user
        """
        self.module_repository = module_repository
        self.current_user = current_user
    
    def get_module_by_id(self, module_id: int) -> MasterModuleResponse:
        """Get module by ID.
        
        Args:
            module_id: Module ID
            
        Returns:
            Module response
            
        Raises:
            HTTPException: If module not found
        """
        try:
            module: Optional[MasterModule] = self.module_repository.get_by_id(module_id)
            if not module:
                error = ValueError(f"Module with ID {module_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_module_by_id",
                        "module_id": module_id,
                        "user_id": self.current_user.id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Module with ID {module_id} not found"
                )
            
            return MasterModuleResponse.model_validate(module)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_module_by_id",
                    "module_id": module_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while retrieving module"
            )
    
    def get_all_modules(self, skip: int = 0, limit: int = 100) -> List[MasterModuleResponse]:
        """Get all module records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of module responses
        """
        try:
            module_list: List[MasterModule] = self.module_repository.get_all(skip=skip, limit=limit)
            return [MasterModuleResponse.model_validate(module) for module in module_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_modules",
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while retrieving modules"
            )
    
    def get_active_modules(self, skip: int = 0, limit: int = 100) -> List[MasterModuleResponse]:
        """Get all active module records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of active module responses
        """
        try:
            module_list: List[MasterModule] = self.module_repository.get_active_modules(skip=skip, limit=limit)
            return [MasterModuleResponse.model_validate(module) for module in module_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_active_modules",
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error occurred while retrieving active modules"
            )