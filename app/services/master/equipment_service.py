"""Service for master equipment management.

This module contains the service class for master equipment business logic.
"""

from typing import List, Optional, Annotated
from fastapi import Depends, HTTPException, status
from app.repositories.master.equipment_repository import EquipmentRepository
from app.schemas.master.equipment_schema import (
    MasterEquipmentCreate,
    MasterEquipmentUpdate,
    MasterEquipmentResponse
)
from app.core.jwt import get_current_user
from app.db import User, MasterEquipment
from app.core.logs import log_error_with_context


class EquipmentService:
    """Service class for master equipment business logic."""
    
    def __init__(
        self,
        equipment_repository: Annotated[EquipmentRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            equipment_repository: Equipment repository dependency
            current_user: Current authenticated user
        """
        self.equipment_repository = equipment_repository
        self.current_user = current_user
    
    def create_equipment(self, equipment_data: MasterEquipmentCreate):
        """Create a new master equipment record.
        
        Args:
            equipment_data: Equipment data to create
            
        Returns:
            Created equipment response
            
        Raises:
            HTTPException: If creation fails or equipment name already exists
        """
        try:
            # Check if equipment name already exists
            if equipment_data.equipment_id:
                existing_equipment: Optional[MasterEquipment] = self.equipment_repository.get_by_equipment_id(equipment_data.equipment_id)
                if existing_equipment:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Equipment with ID '{equipment_data.equipment_id}' already exists"
                    )
            
            # Create new equipment
            self.equipment_repository.create(equipment_data)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_equipment",
                    "equipment_data": equipment_data.model_dump(),
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create equipment"
            )
    
    def get_equipment_by_id(self, equipment_id: int) -> MasterEquipmentResponse:
        """Get master equipment record by ID.
        
        Args:
            equipment_id: Equipment ID to retrieve
            
        Returns:
            Equipment response
            
        Raises:
            HTTPException: If equipment not found or retrieval fails
        """
        try:
            equipment: Optional[MasterEquipment] = self.equipment_repository.get_by_id(equipment_id)
            if not equipment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Equipment with ID {equipment_id} not found"
                )
            
            return MasterEquipmentResponse.model_validate(equipment)
        
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_by_id",
                    "equipment_id": equipment_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve equipment"
            )
    
    def get_all_equipment(self, skip: int = 0, limit: int = 100) -> List[MasterEquipmentResponse]:
        """Get all master equipment records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment responses
            
        Raises:
            HTTPException: If retrieval fails
        """
        try:
            equipment_list: List[MasterEquipment] = self.equipment_repository.get_all(skip=skip, limit=limit)
            final_list: List[MasterEquipmentResponse] = []
            for equipment in equipment_list:
                talent_name = "N/A"
                if equipment.id:
                    talent_name = self.equipment_repository.get_talent_by_equipment_id(equipment.id)
                    if talent_name:
                        talent_name = talent_name
                    else:
                        talent_name = "N/A"
                    
                final_list.append(MasterEquipmentResponse(**equipment.model_dump(), assigned_to=talent_name))
            return final_list
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_equipment",
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve equipment list"
            )
    
    def update_equipment(self, equipment_id: int, equipment_data: MasterEquipmentUpdate) -> MasterEquipmentResponse:
        """Update master equipment record.
        
        Args:
            equipment_id: Equipment ID to update
            equipment_data: Equipment data to update
            
        Returns:
            Updated equipment response
            
        Raises:
            HTTPException: If equipment not found, name conflict, or update fails
        """
        try:
            # Check if equipment exists
            existing_equipment: Optional[MasterEquipment] = self.equipment_repository.get_by_id(equipment_id)
            if not existing_equipment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Equipment with ID {equipment_id} not found"
                )
            
            # Check for name conflict if name is being updated
            if equipment_data.equipment_id and equipment_data.equipment_id != existing_equipment.equipment_id:
                name_conflict: Optional[MasterEquipment] = self.equipment_repository.get_by_equipment_id(equipment_data.equipment_id)
                if name_conflict:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Equipment with name '{equipment_data.equipment_id}' already exists"
                    )
            
            # Update equipment
            updated_equipment: Optional[MasterEquipment] = self.equipment_repository.update(equipment_id, equipment_data)
            if not updated_equipment:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update equipment"
                )
            
            return MasterEquipmentResponse.model_validate(updated_equipment)
        
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_equipment",
                    "equipment_id": equipment_id,
                    "equipment_data": equipment_data.model_dump(exclude_unset=True),
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update equipment"
            )
    
    def delete_equipment(self, equipment_id: int) -> bool:
        """Delete master equipment record.
        
        Args:
            equipment_id: Equipment ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If equipment not found or deletion fails
        """
        try:
            # Check if equipment exists
            existing_equipment: Optional[MasterEquipment] = self.equipment_repository.get_by_id(equipment_id)
            if not existing_equipment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Equipment with ID {equipment_id} not found"
                )
            
            # Delete equipment
            deleted: bool = self.equipment_repository.delete(equipment_id)
            if not deleted:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete equipment"
                )
            
            return True
        
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_equipment",
                    "equipment_id": equipment_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete equipment"
            )
    
    def get_equipment_count(self) -> int:
        """Get total count of master equipment records.
        
        Returns:
            Total count of equipment records
            
        Raises:
            HTTPException: If count fails
        """
        try:
            return self.equipment_repository.count()
        
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_count",
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get equipment count"
            )
