"""Service for role module permission mapping management.

This module contains the service class for role module permission mapping business logic.
"""

from typing import List, Optional, Annotated
from fastapi import Depends, HTTPException, status
from app.repositories.master.role_module_permission_mapping_repository import RoleModulePermissionMappingRepository
from app.schemas.master.role_module_permission_mapping_schema import (
    RoleModulePermissionMappingCreate,
    RoleModulePermissionMappingUpdate,
    RoleModulePermissionMappingResponse
)
from app.db import User
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context


class RoleModulePermissionMappingService:
    """Service class for role module permission mapping business logic."""
    
    def __init__(
        self,
        mapping_repository: Annotated[RoleModulePermissionMappingRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            mapping_repository: Mapping repository dependency
            current_user: Current authenticated user
        """
        self.mapping_repository = mapping_repository
        self.current_user = current_user
    
    def create_mapping(self, mapping_data: RoleModulePermissionMappingCreate) -> RoleModulePermissionMappingResponse:
        """Create a new role module permission mapping.
        
        Args:
            mapping_data: Mapping data to create
            
        Returns:
            Created mapping response
            
        Raises:
            HTTPException: If mapping creation fails or duplicate exists
        """
        try:
            # Check if mapping already exists for this role and module
            existing_mapping = self.mapping_repository.get_by_role_and_module(
                mapping_data.role_id, mapping_data.module_id
            )
            if existing_mapping:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Mapping already exists for this role and module"
                )
            
            created_mapping = self.mapping_repository.create(mapping_data)
            return RoleModulePermissionMappingResponse.model_validate(created_mapping)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_mapping",
                    "mapping_data": mapping_data.model_dump(),
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create mapping"
            )
    
    def get_mapping_by_id(self, mapping_id: int) -> RoleModulePermissionMappingResponse:
        """Get mapping by ID.
        
        Args:
            mapping_id: Mapping ID
            
        Returns:
            Mapping response
            
        Raises:
            HTTPException: If mapping not found or retrieval fails
        """
        try:
            mapping = self.mapping_repository.get_by_id(mapping_id)
            if not mapping:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Mapping not found"
                )
            return RoleModulePermissionMappingResponse.model_validate(mapping)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_mapping_by_id",
                    "mapping_id": mapping_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve mapping"
            )
    
    def get_all_mappings(self, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMappingResponse]:
        """Get all mappings with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping responses
        """
        try:
            mapping_list = self.mapping_repository.get_all(skip=skip, limit=limit)
            return [RoleModulePermissionMappingResponse.model_validate(mapping) for mapping in mapping_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_mappings",
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve mappings"
            )
    
    def get_mappings_by_role_id(self, role_id: int, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMappingResponse]:
        """Get mappings by role ID.
        
        Args:
            role_id: Role ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping responses for the role
        """
        try:
            mapping_list = self.mapping_repository.get_by_role_id(role_id, skip=skip, limit=limit)
            return [RoleModulePermissionMappingResponse.model_validate(mapping) for mapping in mapping_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_mappings_by_role_id",
                    "role_id": role_id,
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve mappings by role"
            )
    
    def get_mappings_by_module_id(self, module_id: int, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMappingResponse]:
        """Get mappings by module ID.
        
        Args:
            module_id: Module ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping responses for the module
        """
        try:
            mapping_list = self.mapping_repository.get_by_module_id(module_id, skip=skip, limit=limit)
            return [RoleModulePermissionMappingResponse.model_validate(mapping) for mapping in mapping_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_mappings_by_module_id",
                    "module_id": module_id,
                    "skip": skip,
                    "limit": limit,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve mappings by module"
            )
    
    def get_mapping_by_role_and_module(self, role_id: int, module_id: int) -> Optional[RoleModulePermissionMappingResponse]:
        """Get mapping by role ID and module ID.
        
        Args:
            role_id: Role ID
            module_id: Module ID
            
        Returns:
            Mapping response if found, None otherwise
        """
        try:
            mapping = self.mapping_repository.get_by_role_and_module(role_id, module_id)
            if mapping:
                return RoleModulePermissionMappingResponse.model_validate(mapping)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_mapping_by_role_and_module",
                    "role_id": role_id,
                    "module_id": module_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve mapping"
            )
    
    def update_mapping(self, mapping_id: int, mapping_data: RoleModulePermissionMappingUpdate) -> RoleModulePermissionMappingResponse:
        """Update an existing mapping record.
        
        Args:
            mapping_id: Mapping ID to update
            mapping_data: Updated mapping data
            
        Returns:
            Updated mapping response
            
        Raises:
            HTTPException: If mapping not found or update fails
        """
        try:
            # Check if mapping exists
            if not self.mapping_repository.check_if_exists(mapping_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Mapping not found"
                )
            
            updated_mapping = self.mapping_repository.update(mapping_id, mapping_data)
            if not updated_mapping:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Mapping not found"
                )
            
            return RoleModulePermissionMappingResponse.model_validate(updated_mapping)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_mapping",
                    "mapping_id": mapping_id,
                    "mapping_data": mapping_data.model_dump(),
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update mapping"
            )
    
    def delete_mapping(self, mapping_id: int) -> bool:
        """Delete a mapping record.
        
        Args:
            mapping_id: Mapping ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If mapping not found or deletion fails
        """
        try:
            # Check if mapping exists
            if not self.mapping_repository.check_if_exists(mapping_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Mapping not found"
                )
            
            deleted = self.mapping_repository.delete(mapping_id)
            if not deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Mapping not found"
                )
            
            return True
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_mapping",
                    "mapping_id": mapping_id,
                    "user_id": self.current_user.id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete mapping"
            )