"""Service for master role management.

This module contains the service class for master role business logic.
"""

from typing import List, Optional, Dict, Any, Annotated
from fastapi import Depends, HTTPException, status
from app.repositories.master.role_repository import RoleRepository
from app.schemas.master.role_schema import (
    MasterRoleCreate,
    MasterRoleUpdate,
    MasterRoleResponse
)
from app.core.jwt import get_current_user
from app.db import User, MasterR<PERSON>
from app.core.logs import log_error_with_context


class RoleService:
    """Service class for master role business logic."""
    
    def __init__(
        self,
        role_repository: Annotated[RoleRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            role_repository: Role repository dependency
            current_user: Current authenticated user
        """
        self.role_repository = role_repository
        self.current_user = current_user
    
    def create_role(self, role_data: MasterRoleCreate):
        """Create a new master role record.
        
        Args:
            role_data: Role data to create
            
        Returns:
            Created role response
            
        Raises:
            HTTPException: If creation fails or role name already exists
        """
        try:
            # Check if role name already exists
            existing_role: Optional[MasterRole] = self.role_repository.get_by_name(role_data.name)
            if existing_role:
                error = ValueError(f"Role with name '{role_data.name}' already exists")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "create_role",
                        "role_name": role_data.name,
                        "existing_role_id": existing_role.id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Role with name '{role_data.name}' already exists"
                )
            
            self.role_repository.create(role_data)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_role",
                    "role_data": role_data.model_dump()
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create role"
            )
    
    def create_role_with_permissions(self, role_data: MasterRoleCreate):
        """Create a new master role record with module permissions.
        
        Args:
            role_data: Role data including module permissions to create
            
        Returns:
            Created role response
            
        Raises:
            HTTPException: If creation fails or role name already exists
        """
        try:
            # Check if role name already exists
            existing_role: Optional[MasterRole] = self.role_repository.get_by_name(role_data.name)
            if existing_role:
                error = ValueError(f"Role with name '{role_data.name}' already exists")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "create_role_with_permissions",
                        "role_name": role_data.name,
                        "existing_role_id": existing_role.id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Role with name '{role_data.name}' already exists"
                )
            
            self.role_repository.create_role_with_permissions(role_data)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_role_with_permissions",
                    "role_data": role_data.model_dump()
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create role with permissions"
            )
    
    def get_role_with_permissions(self, role_id: int) -> Dict[str, Any]:
        """Get role with its module permissions.
        
        Args:
            role_id: Role ID
            
        Returns:
            Role data with module permissions
            
        Raises:
            HTTPException: If role not found or database error
        """
        try:
            role_data = self.role_repository.get_role_with_permissions(role_id)
            if not role_data:
                error = ValueError(f"Role with ID {role_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_role_with_permissions",
                        "role_id": role_id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )
            
            return role_data
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_role_with_permissions",
                    "role_id": role_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve role with permissions"
            )
    
    def get_role_by_id(self, role_id: int) -> MasterRoleResponse:
        """Get role by ID.
        
        Args:
            role_id: Role ID
            
        Returns:
            Role response
            
        Raises:
            HTTPException: If role not found
        """
        try:
            role: Optional[MasterRole] = self.role_repository.get_by_id(role_id)
            if not role:
                error = ValueError(f"Role with ID {role_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_role_by_id",
                        "role_id": role_id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )
            
            return MasterRoleResponse.model_validate(role)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_role_by_id",
                    "role_id": role_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve role"
            )
    
    def get_all_roles(self, skip: int = 0, limit: int = 100) -> List[MasterRoleResponse]:
        """Get all role records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of role responses
        """
        try:
            role_list: List[MasterRole] = self.role_repository.get_all(skip=skip, limit=limit)
            return [MasterRoleResponse.model_validate(role) for role in role_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_roles",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve roles"
            )
    
    def update_role(self, role_id: int, role_data: MasterRoleUpdate) -> MasterRoleResponse:
        """Update an existing role record.
        
        Args:
            role_id: Role ID to update
            role_data: Updated role data
            
        Returns:
            Updated role response
            
        Raises:
            HTTPException: If role not found, update fails, or name conflict
        """
        try:
            if not self.role_repository.check_if_exists(role_id):
                error = ValueError(f"Role with ID {role_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "update_role",
                        "role_id": role_id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )
            
            # Check if new name conflicts with existing role (if name is being updated)
            if role_data.name:
                existing_role: Optional[MasterRole] = self.role_repository.get_by_name(role_data.name)
                if existing_role and existing_role.id != role_id:
                    error = ValueError(f"Role with name '{role_data.name}' already exists")
                    log_error_with_context(
                        error=error,
                        context={
                            "operation": "update_role",
                            "role_id": role_id,
                            "role_name": role_data.name,
                            "existing_role_id": existing_role.id
                        }
                    )
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"Role with name '{role_data.name}' already exists"
                    )
            
            role: Optional[MasterRole] = self.role_repository.update(role_id, role_data)
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update role"
                )
            
            return MasterRoleResponse.model_validate(role)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_role",
                    "role_id": role_id,
                    "role_data": role_data.model_dump()
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update role"
            )
    
    def delete_role(self, role_id: int) -> dict[str, str]:
        """Delete a role record.
        
        Args:
            role_id: Role ID to delete
            
        Returns:
            Success message
            
        Raises:
            HTTPException: If role not found or deletion fails
        """
        try:
            if not self.role_repository.check_if_exists(role_id):
                error = ValueError(f"Role with ID {role_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "delete_role",
                        "role_id": role_id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )
            
            success = self.role_repository.delete(role_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete role"
                )
            
            return {"message": "Role deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_role",
                    "role_id": role_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete role"
            )
    
    def update_role_with_permissions(self, role_id: int, role_data: MasterRoleUpdate) -> MasterRoleResponse:
        """Update an existing role record with module permissions.
        
        Args:
            role_id: Role ID to update
            role_data: Updated role data including module permissions
            
        Returns:
            Updated role response
            
        Raises:
            HTTPException: If role not found, update fails, or name conflict
        """
        try:
            # Use role_id from the data if provided, otherwise use the path parameter
            target_role_id = role_data.role_id if role_data.role_id is not None else role_id
            
            if not self.role_repository.check_if_exists(target_role_id):
                error = ValueError(f"Role with ID {target_role_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "update_role_with_permissions",
                        "role_id": target_role_id
                    }
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Role not found"
                )
            
            # Check if new name conflicts with existing role (if name is being updated)
            if role_data.name:
                existing_role: Optional[MasterRole] = self.role_repository.get_by_name(role_data.name)
                if existing_role and existing_role.id != target_role_id:
                    error = ValueError(f"Role with name '{role_data.name}' already exists")
                    log_error_with_context(
                        error=error,
                        context={
                            "operation": "update_role_with_permissions",
                            "role_id": target_role_id,
                            "role_name": role_data.name,
                            "existing_role_id": existing_role.id
                        }
                    )
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"Role with name '{role_data.name}' already exists"
                    )
            
            role: Optional[MasterRole] = self.role_repository.update_role_with_permissions(target_role_id, role_data)
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update role with permissions"
                )
            
            return MasterRoleResponse.model_validate(role)
        except HTTPException:
            raise
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_role_with_permissions",
                    "role_id": role_id,
                    "role_data": role_data.model_dump()
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update role with permissions"
            )
