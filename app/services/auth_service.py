from typing import Annotated, Dict, Any
from fastapi import Depends, HTTPException, status
from app.repositories import UserRepository
from app.schemas.login_schema import LoginSchema
from app.core import verify_password, log_error_with_context
from app.core.jwt import create_access_token


class AuthService:
    def __init__(self, user_repository: Annotated[UserRepository, Depends()]):
        self.user_repository = user_repository

    async def login(self, req: LoginSchema) -> Dict[str, Any]:
        """Authenticate user and return user data."""
        try:
            # Find user by email
            user = self.user_repository.get_by_email(req.email)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password",
                )

            # Verify password
            if not user.password or not await verify_password(
                req.password, user.password
            ):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password",
                )

            # Check if user is active
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Account is deactivated",
                )

            return {
                "name": user.name,
                "email": user.email,
                "phone": user.phone,
                "pic": user.pic,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "status": "success",
                "message": "Login successful",
                "access_token": await create_access_token(
                    data={"sub": user.email, "user_id": user.id}
                ),
            }
        except Exception as e:
            log_error_with_context(
                e, {"service": "AuthService", "method": "login", "email": req.email}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login failed",
            )
