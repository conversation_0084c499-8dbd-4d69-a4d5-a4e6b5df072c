from typing import Annotated, Any
from fastapi import Depends
from app.core.jwt import get_current_user
from app.core.password import get_hashed_password, verify_password
from app.db import User
from app.repositories import UserRepository
from app.repositories.master.role_repository import RoleRepository
from app.schemas.profile_schema import ProfileSchema


class ProfileService:
    def __init__(
        self, 
        user_repository: Annotated[UserRepository, Depends()],
        role_repository: Annotated[RoleRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.user_repository = user_repository
        self.role_repository = role_repository
        self.current_user = current_user

    def get_profile(self) -> dict[str, str]:
        if not self.current_user.id:
            raise ValueError("User ID is None")
        user = self.user_repository.get_by_id(self.current_user.id)
        if not user:
            raise ValueError("User not found")
        return {
            "email": user.email or "",
            "name": user.name or "",
            "phone": user.phone or "",
        }

    def update_profile(self, profile_data: ProfileSchema) -> None:
        if not self.current_user.id:
            raise ValueError("User ID is None")
        user = self.user_repository.get_by_id(self.current_user.id)
        if not user:
            raise ValueError("User not found")
        user.name = profile_data.name
        user.phone = profile_data.phone
        self.user_repository.update(user)


    def update_password(self, old_password: str, new_password: str) -> None:
        if not self.current_user.id:
            raise ValueError("User ID is None")

        user = self.user_repository.get_by_id(self.current_user.id)

        if not user:
            raise ValueError("User not found")

        if not user.password:
            raise ValueError("User password is None")

        if not verify_password(old_password, user.password):
            raise ValueError("Old password is incorrect")
        user.password = get_hashed_password(new_password)

        self.user_repository.update(user)

    def get_user_permissions(self) -> dict[str, Any]:
        if not self.current_user.id:
            raise ValueError("User ID is None")
        user = self.user_repository.get_by_id(self.current_user.id)
        if not user:
            raise ValueError("User not found")
        
        permissions: dict[str, Any] = {}
        
        if user.role_id and not user.is_superuser:
          role = self.role_repository.get_role_with_permissions(user.role_id or 0)
          if not role:
            raise ValueError("Role not found")
          permissions = role


        return {
            "is_superuser": user.is_superuser,
            "permissions": permissions,
            "is_active": user.is_active,
        }

