"""Database session management module.

This module provides functionality for creating and managing database sessions
using SQLModel ORM.
"""

from sqlmodel import Session

from app.db.init_db import engine


def get_session():
    """Create and yield a new database session.

    Returns:
        Session: A SQLModel database session object that can be used
                for database operations.
    """
    with Session(engine) as session:

        yield session
