from sqlmodel import select, Session
from app.db.models import User
from app.db.init_db import engine
from app.core.password import get_hashed_password


def seed_users():
    with Session(engine) as session:
        user = session.exec(
            select(User).where(User.email == "<EMAIL>")
        ).first()

        if not user:

            user = User(
                name="Admin",
                email="<EMAIL>",
                password=get_hashed_password("Asd@1234"),
                phone=str("8500035146"),
                is_active=True,
                is_superuser=True,
            )

            session.add(user)
        else:

            user.password = get_hashed_password("Asd@1234")
            user.is_active = True
            user.is_superuser = True
            user.phone = str("8500035146")

            session.add(user)
        session.commit()
