from app.db.init_db import engine
from sqlmodel import Session, select

from app.db.models import MasterModule


def seed_modules():
    modules = [
        "Talent Profile",
        "Talent Banking Info",
        "Talent Documents",
        "Talent Work History",
        "Talent BPO Related Info",
        "Talent Equipment & Software",
        "Talent Health",
        "Talent 3rd Party Integrations",
        "Talent History Logs",
        "Users",
        "Roles",
        "Clients",
        "Equipment",
        "System History Logs",
    ]
    with Session(engine) as session:
        for module in modules:
            statement = session.exec(
                select(MasterModule).where(MasterModule.name == module)
            ).first()

            if not statement:
                module = MasterModule(name=module)
                session.add(module)
        session.commit()
