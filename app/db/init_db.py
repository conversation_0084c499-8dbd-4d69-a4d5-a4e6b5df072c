"""Database initialization module.

This module provides functionality to initialize and setup the database
using SQLModel ORM. It creates the database engine and provides methods
to create all database tables.

Attributes:
    engine: The SQLModel database engine.
"""

from sqlmodel import SQLModel, create_engine

from app.core import settings
from app.db.models import (
    User,
    TalentProfile,
    TalentCronicConditions,
    TalentPastHealthIssues,
    TalentOngoingHealthIssues,
    TalentAllergies,
    TalentDocumentsCollected,
    TalentWageHistory,
    MasterClient,
    TalentClientInfo,
    TalentSkillSetMapping,
    TalentPayrollInformationMapping,
    TalentBankingInformationMapping,
    TalentEmergencyContactMapping,
    TalentPositionMapping,
    TalentVacationMapping,
    TalentLocationMapping,
    TalentEquipmentMapping,
    TalentSoftwareMapping,
    MasterRole,
    MasterModule,
    RoleModulePermissionMapping,
)


# Re-export models for backward compatibility
__all__ = [
    "User",
    "TalentProfile",
    "TalentCronicConditions",
    "TalentPastHealthIssues",
    "TalentOngoingHealthIssues",
    "TalentAllergies",
    "TalentDocumentsCollected",
    "TalentWageHistory",
    "MasterClient",
    "TalentClientInfo",
    "TalentSkillSetMapping",
    "TalentPayrollInformationMapping",
    "TalentBankingInformationMapping",
    "TalentEmergencyContactMapping",
    "TalentPositionMapping",
    "TalentVacationMapping",
    "TalentLocationMapping",
    "TalentEquipmentMapping",
    "TalentSoftwareMapping",
    "MasterRole",
    "MasterModule",
    "RoleModulePermissionMapping",
    "engine",
    "init_db",
]


# In app/db/init_db.py
engine = create_engine(
    settings.database_url, 
    echo=False if settings.env_type == "local" else False,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
    pool_size=20,        # Increase base pool size from default 5 to 20
    max_overflow=10,     # Allow up to 10 additional connections during peak load
    pool_timeout=60,     # Increase timeout from default 30 to 60 seconds
)


def init_db():
    """Initialize the database.

    This function creates all database tables defined in SQLModel models.
    """

    SQLModel.metadata.create_all(engine)
