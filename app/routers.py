"""Router configuration module for the BPO Admin backend.

This module centralizes all router imports and provides a function to register
all routers with the FastAPI application instance.
"""

from fastapi import FastAPI

from app.api.v1 import (
    auth_router,
    profile_router,
    user_router,
    talent_client_info_router,
    role_router,
    module_router,
    role_module_permission_mapping_router,
    health_router,
    equipment_router,
)
from app.api.v1.master import master_client_router
from app.api.v1.talent import (
    candidate_router,
    chronic_conditions_router,
    documents_router,
    ongoing_health_router,
    past_health_router,
    banking_router,
    payroll_router,
    emergency_contact_router,
    skill_set_router,
    position_router,
    vacation_router,
    location_router,
    equipment_mapping_router,
    software_mapping_router,
    it_documents_router,
    allergies_router,
    activity_router,
    third_party_integration_router,
)


def register_routers(app: FastAPI) -> None:
    """
    Register all application routers with the FastAPI app instance.
    
    This function includes all routers with their respective prefixes and tags,
    organizing them by functional area for better maintainability.
    
    Args:
        app (FastAPI): The FastAPI application instance to register routers with
    """
    # Core application routers
    app.include_router(router=health_router, prefix="/health", tags=["Health Check"])
    app.include_router(router=auth_router, prefix="/auth", tags=["Auth"])
    app.include_router(router=profile_router, prefix="/profile", tags=["Profile"])
    app.include_router(router=user_router, prefix="/users", tags=["User Management"])

    # Talent profile and personal information
    app.include_router(router=candidate_router, prefix="/talent/profile", tags=["Talent Profile"])
    app.include_router(router=payroll_router, prefix="/talent/payroll", tags=["Talent Payroll Information"])
    app.include_router(router=skill_set_router, prefix="/talent/skill-sets", tags=["Talent Skill Sets"])
    app.include_router(router=emergency_contact_router, prefix="/talent/emergency-contact", tags=["Talent Emergency Contact"])

    # Talent health information
    app.include_router(router=chronic_conditions_router, prefix="/talent/chronic-conditions", tags=["Talent Chronic Conditions"])
    app.include_router(router=ongoing_health_router, prefix="/talent/ongoing-health", tags=["Talent Ongoing Health"])
    app.include_router(router=past_health_router, prefix="/talent/past-health", tags=["Talent Past Health"])
    app.include_router(router=allergies_router, prefix="/talent/allergies", tags=["Talent Allergies"])

    # Talent documents and banking
    app.include_router(router=documents_router, prefix="/talent/documents", tags=["Talent Documents"])
    app.include_router(router=banking_router, prefix="/talent/banking", tags=["Talent Banking Information"])

    # Talent positions and mappings
    app.include_router(router=position_router, prefix="/talent/positions", tags=["Talent Positions"])
    app.include_router(router=location_router, prefix="/talent/location", tags=["Talent Location Mapping"])
    app.include_router(router=vacation_router, prefix="/talent/vacation", tags=["Talent Vacation Mapping"])

    # Client information
    app.include_router(router=master_client_router, prefix="/master/clients", tags=["Master Client"])
    app.include_router(router=talent_client_info_router, prefix="/talent/client-info", tags=["Talent Client Information"])

    # IT equipment and software
    app.include_router(router=equipment_mapping_router, prefix="/talent/equipment", tags=["Talent Equipment Mapping"])
    app.include_router(router=software_mapping_router, prefix="/talent/software", tags=["Talent Software Mapping"])
    app.include_router(router=it_documents_router, prefix="/talent/it-documents", tags=["Talent IT Documents"])

    # Master data management
    app.include_router(router=role_router, prefix="/master/role", tags=["Master Role"])
    app.include_router(router=module_router, prefix="/master/module", tags=["Master Module"])
    app.include_router(router=role_module_permission_mapping_router, prefix="/master/role-module-permission", tags=["Role Module Permission Mapping"])
    app.include_router(router=equipment_router, prefix="/master/equipment", tags=["Master Equipment"])

    # Activity and integrations
    app.include_router(router=activity_router, prefix="/talent/activity", tags=["Talent Activity"])
    app.include_router(router=third_party_integration_router, prefix="/talent/third-party-integration", tags=["Talent Third Party Integration"])