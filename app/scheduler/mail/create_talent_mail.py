"""
Talent Email Creation Scheduler Module

This module contains the scheduled task for checking and managing talent email creation
in third-party integrations (primarily Zoho). It runs hourly during business hours
to monitor email creation status and handle retry logic.

Author: BPO Admin Backend Team
Created: 2025
"""

from datetime import datetime
from datetime import timed<PERSON><PERSON>
import json
from sqlmodel import Session, select
from app.core.activity_tracker import track_talent_activity_sync
from app.core.config import settings
from app.core.logs import logger
from app.core.zoho_util import get_all_org_users
from app.db.init_db import engine
from app.db.models import TalentPositionMapping, TalentProfile, TalentThirdPartyIntegration
from app.emails.schemas import ITEmailRequest
from app.emails.zoho_creation_email import ZohoEmailService


async def check_new_talent_email_creation():
    """
    Scheduled task that runs every hour between 6 AM and 6 PM (Monday-Friday).
    
    This function monitors talent third-party integrations and manages email creation
    status. It performs the following operations:
    
    1. Fetches all talent integrations with status "initiated" or "retry"
    2. Retrieves current Zoho organization users
    3. Checks if talent emails have been created in Zoho
    4. Updates integration status to "created" if email exists
    5. Sends retry emails to IT if email creation is pending
    
    The function implements a retry mechanism with hourly intervals to ensure
    email creation requests are properly handled.
    """
    logger.info("Running scheduled hourly task for talent email creation check")
    
    # Create database session for all operations
    with Session(engine) as session: 
        # Query for talent integrations that need processing
        # Status "initiated" = first time request, "retry" = subsequent attempts
        query = select(TalentThirdPartyIntegration).where(
            TalentThirdPartyIntegration.status.in_(["initiated", "retry"])
        )
        talent_integrations = session.exec(query).all()
        
        # Early return if no integrations need processing
        if not talent_integrations:
            logger.info("No talent integrations found with status 'initiated' or 'retry'")
            return
        
        # Fetch all current Zoho organization users to check email creation status
        users = get_all_org_users(settings.zoho_user_refresh_token)
        if 'error' in users:
            logger.error(f"Error fetching Zoho org users: {users}")
            return
        
        # Extract email addresses from Zoho users for quick lookup
        emails = []
        for user in users:
            emails.append(user['primaryEmailAddress'])

        # Process each talent integration
        for talent_integration in talent_integrations:
            talent_email = talent_integration.data + "@bposolutionsgroup.com"
            logger.info(f"Processing talent integration: {talent_integration.id} - Status: {talent_integration.status}")
            
            # Check if the talent's email has been created in Zoho
            if talent_email and talent_email in emails:
                # Email found in Zoho - update integration status to "created"
                old_status = talent_integration.status
                talent_integration.status = "active"
                
                # Get the user data from Zoho and store it in the integration record
                index = emails.index(talent_email)
                # Store the user data in json_data
                talent_integration.json_data = json.dumps(users[index])
                talent_integration.accountId = users[index]['accountId']
                talent_integration.zuid = users[index]['zuid']
                talent_integration.zoid = users[index]['policyId']['zoid']
                
                # Save changes to database
                session.add(talent_integration)
                session.commit()
                
                # Track the status change in activity log
                track_talent_activity_sync(
                    talent_profile_id=talent_integration.talent_profile_id,
                    activity_type="UPDATE",
                    activity_description=f"Email Created: {talent_integration.third_party}",
                    old_data={"status": old_status},
                    new_data={"status": talent_integration.status},
                    current_user=0
                )
                logger.info(f"Email creation confirmed for talent integration {talent_integration.id}")
                
            else:
                # Email not found in Zoho - handle retry logic
                # Only send retry email if more than 1 hour has passed since last update
                if talent_integration.updated_at < datetime.now() - timedelta(hours=0):
                    try:
                        logger.info(f"Sending retry email for talent integration {talent_integration.id}")
                        
                        # Initialize email service
                        service = ZohoEmailService()
                        
                        # Fetch talent profile and position information
                        talent = session.exec(
                            select(TalentProfile).where(TalentProfile.id == talent_integration.talent_profile_id)
                        ).first()
                        talent_position = session.exec(
                            select(TalentPositionMapping).where(TalentPositionMapping.talent_profile_id == talent_integration.talent_profile_id)
                        ).first()
                        
                        # Prepare email request data
                        request_data = ITEmailRequest(
                            it_person_name=settings.it_user_name,
                            talent_first_name=talent.first_name,
                            talent_last_name=talent.last_name,
                            talent_email=talent_position.bpo_email + "@bposolutionsgroup.com",
                            request_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        )
                        
                        # Send email request to IT department
                        service.send_it_email_request(
                            request_data=request_data,
                            it_email=settings.it_email,
                            # cc_emails=['<EMAIL>']  # Optional CC emails
                        )
                        
                        # Update integration status and increment retry count
                        talent_integration.status = "retry"
                        talent_integration.email_count = talent_integration.email_count + 1 if talent_integration.email_count else 1
                        session.add(talent_integration)
                        session.commit()
                        
                        # Track the retry attempt in activity log
                        track_talent_activity_sync(
                            talent_profile_id=talent_integration.talent_profile_id,
                            activity_type="UPDATE",
                            activity_description=f"Email Not Created Yet - Retry Count: {talent_integration.email_count}",
                            old_data={"status": talent_integration.status},
                            new_data={"status": talent_integration.status}
                        )
                        
                        logger.info(f"Retry email sent for talent integration {talent_integration.id} - Retry count: {talent_integration.email_count}")
                        
                    except Exception as e:
                        logger.error(f"Error sending IT email for talent integration {talent_integration.id}: {e}")
                else:
                    logger.info(f"Skipping retry for talent integration {talent_integration.id} - Less than 1 hour since last update")
    
    logger.info("Completed scheduled hourly task for talent email creation check")
    

