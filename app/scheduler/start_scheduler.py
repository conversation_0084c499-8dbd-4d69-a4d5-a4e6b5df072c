from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from app.core.logs import logger

from app.scheduler.mail.create_talent_mail import check_new_talent_email_creation


def setup_scheduler() -> AsyncIOScheduler:
    """
    Set up the scheduler to run tasks every hour between 6 AM and 6 PM.
    
    This function initializes the AsyncIOScheduler and adds the scheduled task
    with a cron trigger that runs every hour from 6 AM to 6 PM.
    
    Returns:
        AsyncIOScheduler: The configured and started scheduler instance
    """
    scheduler = AsyncIOScheduler()
    
    # Add job to run every hour between 6 AM (06:00) and 6 PM (18:00)
    scheduler.add_job(
        check_new_talent_email_creation,
        CronTrigger(hour="6-18", minute=0, day_of_week="mon-fri"),  # Run at the top of every hour from 6 AM to 6 PM, Monday to Friday
        id="hourly_task",
        name="Hourly Task (6 AM - 6 PM, Mon-Fri)",
        replace_existing=True
    )
    
    scheduler.start()
    logger.info("Scheduler started - will run hourly between 6 AM and 6 PM")
    
    return scheduler
