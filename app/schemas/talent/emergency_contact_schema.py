"""Schemas for talent emergency contact information."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field



class TalentEmergencyContactCreate(BaseModel):
    """Schema for creating talent emergency contact information."""
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    first_name: str = Field(..., description="First name of the emergency contact")
    last_name: Optional[str] = Field(None, description="Last name of the emergency contact")
    middle_name: Optional[str] = Field(None, description="Middle name of the emergency contact")
    relationship: str = Field(..., description="Relationship of the emergency contact")
    country_code: str = Field(..., description="Country code of the emergency contact")
    phone: str = Field(..., description="Phone number of the emergency contact")
    email: Optional[str] = Field(None, description="Email of the emergency contact")


class TalentEmergencyContactUpdate(BaseModel):
    """Schema for updating talent emergency contact information."""
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    first_name: str = Field(..., description="First name of the emergency contact")
    last_name: Optional[str] = Field(None, description="Last name of the emergency contact")
    middle_name: Optional[str] = Field(None, description="Middle name of the emergency contact")
    relationship: str = Field(..., description="Relationship of the emergency contact")
    country_code: str = Field(..., description="Country code of the emergency contact")
    phone: str = Field(..., description="Phone number of the emergency contact")
    email: Optional[str] = Field(None, description="Email of the emergency contact")


class TalentEmergencyContactResponse(BaseModel):
    """Schema for talent emergency contact information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    first_name: str = Field(..., description="First name of the emergency contact")
    last_name: Optional[str] = Field(None, description="Last name of the emergency contact")
    middle_name: Optional[str] = Field(None, description="Middle name of the emergency contact")
    relationship: str = Field(..., description="Relationship of the emergency contact")
    country_code: str = Field(..., description="Country code of the emergency contact")
    phone: str = Field(..., description="Phone number of the emergency contact")
    email: Optional[str] = Field(None, description="Email of the emergency contact")
