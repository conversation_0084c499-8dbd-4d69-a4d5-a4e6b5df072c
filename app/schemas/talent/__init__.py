"""Talent schemas package."""

from .chronic_conditions_schema import (
    ChronicConditionCreate,
    ChronicConditionResponse,
)
from .ongoing_health_schema import (
    OngoingHealthIssueCreate,
)
from .past_health_schema import (
    PastHealthIssueCreate,
)
from .talent_profile_schema import (
    ProfileCreate,
    ProfileResponse,
    ProfileUpdate,
    DeactivateTalent
)
from .banking_schema import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
    TalentBankingInformationResponse,
)
from .payroll_schema import (
    TalentPayrollInformationCreate,
    TalentPayrollInformationUpdate,
    TalentPayrollInformationResponse,
)
from .emergency_contact_schema import (
    TalentEmergencyContactCreate,
    TalentEmergencyContactUpdate,
    TalentEmergencyContactResponse,
)
from .documents_schema import (
    TalentDocumentCreate,
    TalentDocumentUpdate,
    TalentDocumentResponse,
)
from .client_info_schema import (
    TalentClientInfoCreate,
    TalentClientInfoResponse,
)
from .skill_set_schema import (
    TalentSkillSetCreate,
    TalentSkillSetUpdate,
    TalentSkillSetResponse,
)
from .position_schema import (
    TalentPositionCreate,
    TalentPositionResponse,
)
from .vacation_schema import (
    TalentVacationMappingCreate,
    TalentVacationMappingResponse,
)
from .location_schema import (
    TalentLocationMappingCreate,
    TalentLocationMappingUpdate,
    TalentLocationMappingResponse,
)
from .activity_schema import (
    TalentActivityCreate,
    TalentActivityUpdate,
    TalentActivityResponse,
    ActivityTrackingData,
)
from .it_documents_schema import (
    TalentITDocumentsCreate,
    TalentITDocumentsUpdate,
    TalentITDocumentsResponse,
)
from .third_party_integration_schema import (
    TalentThirdPartyIntegrationBase,
    TalentThirdPartyIntegrationCreate,
    TalentThirdPartyIntegrationResponse,
)

__all__ = [
    "ChronicConditionCreate",
    "ChronicConditionResponse",
    "OngoingHealthIssueCreate",
    "PastHealthIssueCreate",
    "ProfileCreate",
    "ProfileResponse",
    "ProfileUpdate",
    "TalentBankingInformationCreate",
    "TalentBankingInformationUpdate",
    "TalentBankingInformationResponse",
    "TalentPayrollInformationCreate",
    "TalentPayrollInformationUpdate",
    "TalentPayrollInformationResponse",
    "TalentEmergencyContactCreate",
    "TalentEmergencyContactUpdate",
    "TalentEmergencyContactResponse",
    "TalentDocumentCreate",
    "TalentDocumentUpdate",
    "TalentDocumentResponse",
    "TalentClientInfoCreate",
    "TalentClientInfoResponse",
    "TalentSkillSetCreate",
    "TalentSkillSetUpdate",
    "TalentSkillSetResponse",
    "TalentPositionCreate",
    "TalentPositionResponse",
    "TalentVacationMappingCreate",
    "TalentVacationMappingResponse",
    "TalentLocationMappingCreate",
    "TalentLocationMappingUpdate",
    "TalentLocationMappingResponse",
    "TalentActivityCreate",
    "TalentActivityUpdate",
    "TalentActivityResponse",
    "ActivityTrackingData",
    "TalentITDocumentsCreate",
    "TalentITDocumentsUpdate",
    "TalentITDocumentsResponse",
    "TalentThirdPartyIntegrationBase",
    "TalentThirdPartyIntegrationCreate",
    "TalentThirdPartyIntegrationResponse",
    "DeactivateTalent"
]
