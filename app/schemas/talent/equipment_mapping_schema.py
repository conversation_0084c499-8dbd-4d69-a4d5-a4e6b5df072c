"""Schemas for talent equipment mapping management.

This module contains Pydantic schemas for talent equipment mapping CRUD operations.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class TalentEquipmentMappingCreate(BaseModel):
    """Schema for creating new talent equipment mapping.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        equipment_id: Foreign key reference to the associated equipment
        duration_of_use: Duration of equipment usage
        is_replaced: Whether the equipment has been replaced
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    master_equipment_id: int = Field(..., description="Foreign key reference to master equipment")
    is_active: bool = Field(..., description="Whether the equipment mapping is active")



class TalentEquipmentMappingUpdate(TalentEquipmentMappingCreate):
    """Schema for updating existing talent equipment mapping.
    
    All fields are optional to allow partial updates.
    """
    
    id: Optional[int] = Field(default=None, description="Equipment serial number")


class TalentEquipmentMappingResponse(BaseModel):
    """Schema for talent equipment mapping API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    serial_number: Optional[str] = Field(None, description="Equipment serial number")
    model: Optional[str] = Field(None, description="Equipment model")
    name: Optional[str] = Field(None, description="Equipment name")
    is_active: Optional[bool] = Field(None, description="Whether the equipment mapping is active")
    category: Optional[str] = Field(None, description="Equipment category")
    master_equipment_id: Optional[int] = Field(None, description="Foreign key reference to master equipment")
