"""Schema definitions for talent documents management."""

from typing import Optional
from pydantic import Field

from app.schemas.base import CamelCaseModel


class TalentDocumentCreate(CamelCaseModel):
    """Schema for creating talent document information."""
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    doc_type: str = Field(..., max_length=50, description="Type of document")
    url: str = Field(..., max_length=500, description="Document URL or file path")


class TalentDocumentUpdate(CamelCaseModel):
    """Schema for updating talent document information."""
    
    doc_type: Optional[str] = Field(None, max_length=50, description="Type of document")
    url: Optional[str] = Field(None, max_length=500, description="Document URL or file path")


class TalentDocumentResponse(CamelCaseModel):
    """Schema for talent document information API responses.
    
    Includes all fields from the database model plus computed fields.
    """

    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    doc_type: str = Field(..., description="Type of document")
    url: str = Field(..., description="Document URL or file path")
    size: Optional[float] = Field(None, description="Document size in MB")
    base64_content: Optional[str] = Field(None, description="Base64 encoded file content")
