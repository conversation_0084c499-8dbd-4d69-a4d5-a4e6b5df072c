"""IT documents schema definitions for talent management.

This module contains Pydantic models for talent IT documents validation,
including create, update, and response schemas.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class TalentITDocumentsCreate(BaseModel):
    """Schema for creating new talent IT documents information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        document_type: IT document type
        notes: IT document description
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    document_type: str = Field(..., max_length=200, description="IT document type")
    url: Optional[str] = Field(None, description="IT document url")
    notes: Optional[str] = Field(None, max_length=200, description="IT document description")


class TalentITDocumentsUpdate(BaseModel):
    """Schema for updating talent IT documents information.
    
    All fields are optional for partial updates.
    """
    
    document_type: Optional[str] = Field(None, max_length=200, description="IT document type")
    url: Optional[str] = Field(None, description="IT document url")
    notes: Optional[str] = Field(None, max_length=200, description="IT document description")


class TalentITDocumentsResponse(BaseModel):
    """Schema for talent IT documents API responses.
    
    Includes all fields from the database model.
    """
    
    id: Optional[int] = Field(None, description="Primary key for the talent IT documents")
    created_at: Optional[datetime] = Field(None, description="Timestamp when IT documents was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when IT documents was last updated")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    document_type: str = Field(..., description="IT document type")
    url: str = Field(..., description="IT document url")
    size: Optional[int] = Field(None, description="IT document size in MB")
    notes: Optional[str] = Field(None, description="IT document description")
