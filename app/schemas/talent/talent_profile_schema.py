"""Schemas for talent profile API."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class ProfileBase(BaseModel):
    """Base schema for profiles."""

    first_name: str = Field(
        ..., min_length=1, max_length=100, description="First name of the profile"
    )
    middle_name: Optional[str] = Field(
        None, max_length=100, description="Middle name of the profile"
    )
    last_name: str = Field(
        ..., min_length=1, max_length=100, description="Last name of the profile"
    )
    personal_email: str = Field(..., description="Email address of the profile")
    country_code: Optional[str] = Field(
        None, max_length=20, description="Country code of the primary phone number"
    )
    phone: Optional[str] = Field(
        None, max_length=20, description="Primary phone number of the profile"
    )
    country_code2: Optional[str] = Field(
        None, max_length=20, description="Country code of the secondary phone number"
    )
    phone2: Optional[str] = Field(
        None, max_length=20, description="Secondary phone number of the profile"
    )
    date_of_birth: Optional[str] = Field(None, description="Date of birth")
    gender: Optional[str] = Field(None, max_length=10, description="Gender")
    nationality: Optional[str] = Field(None, max_length=50, description="Nationality")
    address_1: Optional[str] = Field(None, max_length=500, description="Primary address")
    apt_unit: Optional[str] = Field(
        None, max_length=50, description="Apartment or unit number"
    )
    address_2: Optional[str] = Field(
        None, max_length=500, description="Secondary address"
    )
    city: Optional[str] = Field(None, max_length=100, description="City")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    zip_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    state: Optional[str] = Field(None, max_length=100, description="State")
    pic: Optional[str] = Field(None, max_length=200, description="Profile picture file path")
    is_active: bool = Field(True, description="Whether the talent profile is active")


class ProfileCreate(ProfileBase):
    """Schema for creating a new talent profile."""
    pass


class ProfileUpdate(BaseModel):
    """Schema for updating an existing profile."""

    first_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="First name of the profile"
    )
    middle_name: Optional[str] = Field(
        None, max_length=100, description="Middle name of the profile"
    )
    last_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Last name of the profile"
    )
    personal_email: Optional[str] = Field(None, description="Email address of the profile")
    country_code: Optional[str] = Field(
        None, max_length=20, description="Country code of the primary phone number"
    )
    phone: Optional[str] = Field(
        None, max_length=20, description="Primary phone number"
    )
    country_code2: Optional[str] = Field(
        None, max_length=20, description="Country code of the secondary phone number"
    )
    phone2: Optional[str] = Field(
        None, max_length=20, description="Secondary phone number"
    )
    date_of_birth: Optional[datetime] = Field(None, description="Date of birth")
    gender: Optional[str] = Field(None, max_length=10, description="Gender")
    nationality: Optional[str] = Field(None, max_length=50, description="Nationality")
    address_1: Optional[str] = Field(None, max_length=500, description="Primary address")
    apt_unit: Optional[str] = Field(
        None, max_length=50, description="Apartment or unit number"
    )
    address_2: Optional[str] = Field(
        None, max_length=500, description="Secondary address"
    )
    city: Optional[str] = Field(None, max_length=100, description="City")
    state: Optional[str] = Field(None, max_length=100, description="State")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    zip_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    pic: Optional[str] = Field(None, max_length=200, description="Profile picture file path")
    is_active: Optional[bool] = Field(
        None, description="Whether the talent profile is active"
    )


class ProfileResponse(ProfileBase):
    """Schema for profile response."""

    id: int = Field(..., description="Unique identifier for the profile")
    created_at: datetime = Field(
        ..., description="Timestamp when the record was created"
    )
    updated_at: Optional[datetime] = Field(
        None, description="Timestamp when the record was last updated"
    )
    pic: Optional[str] = Field(None, max_length=200, description="Profile picture file path")


class TalentSummaryResponse(BaseModel):
    """Custom response schema for talent summary with essential information."""
    
    id: int = Field(..., description="Unique identifier for the talent profile")
    talent_name: str = Field(..., description="Full name of the talent")
    phone: Optional[str] = Field(None, description="Primary phone number of the talent")
    start_date: Optional[datetime] = Field(None, description="Position start date")
    job_position: Optional[str] = Field(None, description="Current job position")
    supervisor: Optional[str] = Field(None, description="Reporting manager/supervisor")
    pic: Optional[str] = Field(None, description="Profile picture file path")
    status: Optional[bool] = Field(None, description="Current status of the talent")

class DeactivateTalent(BaseModel):
    """Schema for deactivating a talent profile."""
    is_active: bool = Field(False, description="Set to False to deactivate the profile")
    reason: Optional[str] = Field(None, description="Reason for deactivation")
