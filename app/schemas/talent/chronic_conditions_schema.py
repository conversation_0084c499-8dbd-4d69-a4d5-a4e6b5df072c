"""Schemas for talent chronic conditions API."""

from typing import Optional
from pydantic import Field
from app.schemas.base import CamelCaseModel


class ChronicConditionBase(CamelCaseModel):
    """Base schema for chronic condition data."""
    
    talent_profile_id: int = Field(..., description="The talent profile ID")
    chronic_condition: Optional[str] = Field(None, max_length=255, description="Name of the chronic condition")
    medication: Optional[str] = Field(None, max_length=500, description="Current medication for the condition")


class ChronicConditionCreate(ChronicConditionBase):
    """Schema for creating a new chronic condition."""
    pass


class ChronicConditionResponse(ChronicConditionBase):
    """Schema for chronic condition response data."""
    
    id: int = Field(..., description="Unique identifier for the chronic condition")
