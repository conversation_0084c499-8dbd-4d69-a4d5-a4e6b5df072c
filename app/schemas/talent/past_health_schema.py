"""Schemas for talent past health issues API."""

from pydantic import Field
from app.schemas.base import CamelCaseModel


class PastHealthIssueBase(CamelCaseModel):
    """Base schema for past health issue data."""
    talent_profile_id: int = Field(..., description="The talent profile ID")
    past_health_issues: str = Field(..., description="The past health issues")


class PastHealthIssueCreate(PastHealthIssueBase):
    """Schema for creating a new past health issue."""
    pass
