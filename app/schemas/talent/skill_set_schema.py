"""Skill set schema definitions for talent management.

This module contains Pydantic models for talent skill set validation,
including create, update, and response schemas.
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field


class EnglishLevel(BaseModel):
    """Schema for English level of the talent (Write, Speak, Type)."""
    write: str = Field(..., description="Write level of the talent")
    speak: str = Field(..., description="Speak level of the talent")
    type: str = Field(..., description="Type level of the talent")


class TalentSkillSetCreate(BaseModel):  
    """Schema for creating new talent skill set information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        skills: Array of skills talent has
        years_of_experience: Years of experience in the skill
        notes_from_interview: Notes from the talent interview
        english_level: English level of the talent (Write, Speak, Type)
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    skills: List[str] = Field(..., description="Array of skills talent has")
    years_of_experience: int = Field(..., ge=0, description="Years of experience in the skill")
    notes_from_interview: Optional[str] = Field(None, description="Notes from the talent interview")
    english_level: Optional[EnglishLevel] = Field(None, description="English level of the talent (Write, Speak, Type)")


class TalentSkillSetUpdate(BaseModel):
    """Schema for updating talent skill set information.
    
    All fields are optional for partial updates.
    """
    
    skills: Optional[List[str]] = Field(None, description="Array of skills talent has")
    years_of_experience: Optional[int] = Field(None, ge=0, description="Years of experience in the skill")
    notes_from_interview: Optional[str] = Field(None, description="Notes from the talent interview")
    english_level: Optional[EnglishLevel] = Field(None, description="English level of the talent (Write, Speak, Type)")


class TalentSkillSetResponse(BaseModel):
    """Schema for talent skill set API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key for the talent skill set")
    created_at: Optional[datetime] = Field(None, description="Timestamp when skill set was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when skill set was last updated")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    skills: List[str] = Field(..., description="Array of skills talent has")
    years_of_experience: int = Field(..., description="Years of experience in the skill")
    notes_from_interview: Optional[str] = Field(None, description="Notes from the talent interview")
    english_level: Optional[EnglishLevel] = Field(None, description="English level of the talent (Write, Speak, Type)")
