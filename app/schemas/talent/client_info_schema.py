"""Client information schema definitions for talent management.

This module contains Pydantic models for talent client information validation,
including create, update, and response schemas.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class TalentClientInfoCreate(BaseModel):
    """Schema for creating new talent client information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        campaign: Campaign of the talent
        bpo_manager: BPO manager of the talent
        client_id: Client id of the talent
        reporting_department: Reporting department of the talent
    """
    
    talent_profile_id: int = Field(..., description="talent profile id", title="talent profile id")
    role: Optional[str] = Field(None, description="role", title="role")
    title: Optional[str] = Field(None, description="title", title="title")
    client_id: Optional[int] = Field(None, description="client id", title="client id")
    location: Optional[str] = Field(None, description="location", title="location")
    start_date: Optional[str] = Field(None, description="start date", title="start date")
    end_date: Optional[str] = Field(None, description="end date", title="end date")
    work_days: Optional[list[str]] = Field(None, description="work days", title="work days")
    is_fte: Optional[bool] = Field(None, description="is fte", title="is fte")
    is_remote: Optional[bool] = Field(None, description="is remote", title="is remote")
    is_contract: Optional[bool] = Field(None, description="is contract", title="is contract")
    shift_start_time: Optional[str] = Field(None, description="shift start time", title="shift start time")
    shift_end_time: Optional[str] = Field(None, description="shift end time", title="shift end time")
    time_zone: Optional[str] = Field(None, description="time zone", title="time zone")
    campaign: Optional[str] = Field(None, description="campaign", title="campaign")
    client_manager: Optional[str] = Field(None, description="bpo manager", title="bpo manager")
    reporting_department: Optional[str] = Field(None, description="reporting department", title="reporting department")


class TalentClientInfoResponse(TalentClientInfoCreate):
    id: int = Field(..., description="Primary key")
    start_date: Optional[datetime] = Field(
        None, description="Start date of the position", title="start date"
    )
    end_date: Optional[datetime] = Field(
        None, description="End date of the position", title="end date"
    )
