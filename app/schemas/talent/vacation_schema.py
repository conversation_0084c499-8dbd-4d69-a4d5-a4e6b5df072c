"""Vacation mapping schema definitions for talent management.

This module contains Pydantic models for vacation mapping validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import BaseModel, Field


class TalentVacationMappingCreate(BaseModel):
    """Schema for creating new talent vacation mapping.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        year_number: Year number since start date
        accrued_paid_time_off_days: Accrued paid time off days
        used_paid_time_off_days: Used paid time off days
        available_paid_time_off_days: Available paid time off days
        available_bereavement_days: Available bereavement days for the year
        remaining_bereavement_days: Remaining bereavement days for the year
        available_vacation_days: Available vacation days for the year
        remaining_vacation_days: Remaining vacation days for the year
        available_parental_days: Available parental days for the year
        remaining_parental_days: Remaining parental days for the year
    """
    
    id: Optional[int] = Field(None, description="Primary key")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    year_number: int = Field(..., description="Year number since start date")
    available_vacation_days: int = Field(..., description="Available vacation days")
    remaining_vacation_days: int = Field(..., description="Remaining vacation days")
    used_vacation_days: int = Field(..., description="Used vacation days")

class TalentVacationMappingResponse(TalentVacationMappingCreate):
   pass
