"""Position schema for talent management.

This module contains Pydantic models for talent position request/response validation.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class TalentPositionCreate(BaseModel):
    """Schema for creating a new talent position.
    
    Attributes:
        talent_profile_id: ID of the talent profile
    """
    
    id: Optional[int] = Field(None, description="Unique identifier for the talent position")
    talent_profile_id: int = Field(..., description="ID of the talent profile")
    reporting_manager: Optional[str] = Field(None, description="Reporting manager of the talent")
    reporting_department: Optional[str] = Field(None, description="Reporting department of the talent")
    campaign: Optional[str] = Field(None, description="Campaign of the talent")
    bpo_email: Optional[str] = Field(None, description="BPO email of the talent")
    position: Optional[str] = Field(None, description="Position of the talent")
    contract_end_date: Optional[str] = Field(None, description="Contract end date of the talent")
    role: Optional[str] = Field(None, max_length=20, description="Role of the talent")
    title: Optional[str] = Field(None, max_length=20, description="Title of the talent position")
    shift_start_time: Optional[str] = Field(None, description="shift start time", title="shift start time")
    shift_end_time: Optional[str] = Field(None, description="shift end time", title="shift end time")
    start_date: Optional[str] = Field(
        default=None, description="Start date of the talent position"
    )
    working_week_days: Optional[list[str]] = Field(None, description="Working week days of the talent")
    termination_date: Optional[str] = Field(
        default=None, description="Termination date of the talent"
    )
    reason_for_termination: Optional[str] = Field(None, description="Reason for termination of the talent")
    hours_per_week: Optional[int] = Field(None, description="Hours per week of the talent")
    hourly_rate: Optional[int] = Field(None, description="Hourly rate of the talent")
    wage_base: Optional[int] = Field(None, description="Wage base of the talent")
    wage_currency: Optional[str] = Field(None, description="Wage currency of the talent")
    wage_frequency: Optional[str] = Field(None, description="Wage frequency of the talent")
    notes: Optional[str] = Field(None, max_length=500, description="Notes for the talent position")
    location: Optional[str] = Field(None, description="Location of the talent")
    location_duration: Optional[int] = Field(None, description="Location duration")

class TalentPositionResponse(TalentPositionCreate):
    """Schema for talent position response.
    
    Includes all fields from the database model.
    """

    contract_end_date: Optional[datetime] = Field(None, description="Contract end date of the talent")
    termination_date: Optional[datetime] = Field(None, description="Termination date of the talent")
    start_date: Optional[datetime] = Field(
        default=None, description="Start date of the talent position"
    )
