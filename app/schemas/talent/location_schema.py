"""Pydantic schemas for talent location mapping."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class TalentLocationMappingCreate(BaseModel):
    """Schema for creating a new talent location mapping."""

    talent_profile_id: int = Field(
        ..., description="ID of the talent profile", gt=0
    )
    location: str = Field(
        ..., description="Location information", max_length=20, min_length=1
    )
    duration_of_stay: str = Field(
        ..., description="Duration of stay at location", max_length=20, min_length=1
    )


class TalentLocationMappingUpdate(BaseModel):
    """Schema for updating an existing talent location mapping."""

    location: Optional[str] = Field(
        None, description="Location information", max_length=20, min_length=1
    )
    duration_of_stay: Optional[str] = Field(
        None, description="Duration of stay at location", max_length=20, min_length=1
    )


class TalentLocationMappingResponse(BaseModel):
    """Schema for talent location mapping response."""

    id: int = Field(..., description="Unique identifier for the location mapping")
    created_at: Optional[datetime] = Field(
        None, description="Timestamp when the record was created"
    )
    updated_at: Optional[datetime] = Field(
        None, description="Timestamp when the record was last updated"
    )
    talent_profile_id: int = Field(
        ..., description="ID of the talent profile"
    )
    location: str = Field(
        ..., description="Location information"
    )
    duration_of_stay: str = Field(
        ..., description="Duration of stay at location"
    )

    class Config:
        """Pydantic configuration."""
        from_attributes = True