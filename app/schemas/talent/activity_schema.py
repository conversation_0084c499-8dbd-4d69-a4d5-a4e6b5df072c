"""Activity schema definitions for talent management.

This module contains Pydantic models for talent activity validation,
including create, update, and response schemas.
"""

from typing import Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class TalentActivityCreate(BaseModel):
    """Schema for creating new talent activity information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        user_id: Foreign key reference to the user who performed the action
        activity_type: Type of activity (CREATE, UPDATE, DELETE, STATUS_CHANGE)
        activity: Description of the activity performed
        old_data: JSON string of the old data (for updates)
        new_data: JSON string of the new data (for creates/updates)
        ip_address: IP address of the user
        user_agent: User agent of the user
        browser: Browser used by the user
        os: Operating system used by the user
        device: Device used by the user
        status: Status of the activity
    """
    
    talent_profile_id: Optional[int] = Field(None, description="Foreign key reference to talent profile")
    user_id: Optional[int] = Field(None, description="Foreign key reference to the user who performed the action")
    activity_type: str = Field(..., max_length=20, description="Type of activity (CREATE, UPDATE, DELETE, STATUS_CHANGE)")
    activity: str = Field(..., max_length=200, description="Description of the activity performed")
    old_data: Optional[str] = Field(None, description="JSON string of the old data (for updates)")
    new_data: Optional[str] = Field(None, description="JSON string of the new data (for creates/updates)")
    ip_address: Optional[str] = Field(None, max_length=20, description="IP address of the user")
    user_agent: Optional[str] = Field(None, max_length=200, description="User agent of the user")
    browser: Optional[str] = Field(None, max_length=20, description="Browser used by the user")
    os: Optional[str] = Field(None, max_length=20, description="Operating system used by the user")
    device: Optional[str] = Field(None, max_length=20, description="Device used by the user")
    status: Optional[bool] = Field(None, description="Status of the activity")


class TalentActivityUpdate(BaseModel):
    """Schema for updating talent activity information.
    
    All fields are optional for partial updates.
    """
    
    activity_type: Optional[str] = Field(None, max_length=20, description="Type of activity")
    activity: Optional[str] = Field(None, max_length=200, description="Description of the activity performed")
    old_data: Optional[str] = Field(None, max_length=200, description="JSON string of the old data")
    new_data: Optional[str] = Field(None, max_length=200, description="JSON string of the new data")
    ip_address: Optional[str] = Field(None, max_length=20, description="IP address of the user")
    user_agent: Optional[str] = Field(None, max_length=200, description="User agent of the user")
    browser: Optional[str] = Field(None, max_length=20, description="Browser used by the user")
    os: Optional[str] = Field(None, max_length=20, description="Operating system used by the user")
    device: Optional[str] = Field(None, max_length=20, description="Device used by the user")
    status: Optional[bool] = Field(None, description="Status of the activity")


class TalentActivityResponse(BaseModel):
    """Schema for talent activity API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key for the talent activity")
    created_at: Optional[datetime] = Field(None, description="Timestamp when activity was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when activity was last updated")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    user_id: int = Field(..., description="Foreign key reference to the user who performed the action")
    activity_type: str = Field(..., description="Type of activity")
    activity: str = Field(..., description="Description of the activity performed")
    old_data: Optional[str] = Field(None, description="JSON string of the old data")
    new_data: Optional[str] = Field(None, description="JSON string of the new data")
    ip_address: Optional[str] = Field(None, description="IP address of the user")
    user_agent: Optional[str] = Field(None, description="User agent of the user")
    browser: Optional[str] = Field(None, description="Browser used by the user")
    os: Optional[str] = Field(None, description="Operating system used by the user")
    device: Optional[str] = Field(None, description="Device used by the user")
    status: Optional[bool] = Field(None, description="Status of the activity")


class ActivityTrackingData(BaseModel):
    """Schema for activity tracking data comparison.
    
    Used internally for comparing old and new data.
    """
    
    talent_profile_id: int = Field(..., description="Talent profile ID")
    activity_type: str = Field(..., description="Type of activity")
    activity_description: str = Field(..., description="Description of what changed")
    old_data: Optional[dict[str, Any]] = Field(None, description="Old data dictionary")
    new_data: Optional[dict[str, Any]] = Field(None, description="New data dictionary")
    changed_fields: Optional[list[str]] = Field(None, description="List of fields that changed")


class ActivityDescription(BaseModel):
    """Schema for activity description with before/after values."""
    
    field: str = Field(..., description="Field name that changed")
    value: str = Field(..., description="Value of the field")
    label: str = Field(..., description="Label indicating 'Before' or 'After'")


class ActivityLogResponse(BaseModel):
    """Schema for formatted activity log response.
    
    Matches the sample response structure provided.
    """
    
    date: str = Field(..., description="Date of the activity in MM/DD/YYYY format")
    time: str = Field(..., description="Time of the activity in HH:MM format")
    event: str = Field(..., description="Type of event (Create, Update, Delete)")
    user: str = Field(..., description="Full name of the user who performed the action")
    user_role: str = Field(..., description="Role of the user (Admin, Manager, etc.)")
    descriptions: list[ActivityDescription] = Field(..., description="List of field changes with before/after values")
    status: str = Field(..., description="Status of the operation (Succeeded, Failed)")
