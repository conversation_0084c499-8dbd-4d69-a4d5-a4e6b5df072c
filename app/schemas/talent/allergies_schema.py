"""Schemas for talent allergies API."""

from typing import Optional
from pydantic import BaseModel, Field


class AllergiesBase(BaseModel):
    """Base schema for allergies data."""
    
    talent_profile_id: int = Field(..., description="The talent profile ID")
    allergy: Optional[str] = Field(None, max_length=255, description="Name of the allergy")
    medication: Optional[str] = Field(None, max_length=500, description="Medication for the allergy")


class AllergiesCreate(AllergiesBase):
    """Schema for creating a new allergy record."""
    pass


class AllergiesResponse(AllergiesBase):
    """Schema for allergy response data."""
    
    id: int = Field(..., description="Unique identifier for the allergy record")
