"""Schemas for master equipment management.

This module contains Pydantic schemas for master equipment CRUD operations.
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class MasterEquipmentCreate(BaseModel):
    """Schema for creating a new master equipment record."""
    
    name: str = Field(..., max_length=200, description="Equipment name")
    equipment_id: Optional[str] = Field(None, description="Equipment ID")
    category: Optional[str] = Field(None, max_length=200, description="Equipment category")
    serial_number: Optional[str] = Field(None, max_length=200, description="Equipment serial number")
    model: Optional[str] = Field(None, max_length=200, description="Equipment model")
    purchase_date: Optional[datetime] = Field(None, description="Purchase date")
    is_under_repair: bool = Field(default=False, description="Equipment repair status")
    is_active: bool = Field(default=True, description="Equipment active status")


class MasterEquipmentUpdate(BaseModel):
    """Schema for updating an existing master equipment record."""
    
    name: Optional[str] = Field(None, max_length=200, description="Equipment name")
    equipment_id: Optional[int] = Field(None, description="Equipment ID")
    category: Optional[str] = Field(None, max_length=200, description="Equipment category")
    serial_number: Optional[str] = Field(None, max_length=200, description="Equipment serial number")
    model: Optional[str] = Field(None, max_length=200, description="Equipment model")
    purchase_date: Optional[datetime] = Field(None, description="Purchase date")
    is_under_repair: Optional[bool] = Field(None, description="Equipment repair status")
    is_active: Optional[bool] = Field(None, description="Equipment active status")


class MasterEquipmentResponse(BaseModel):
    """Schema for master equipment API responses."""
    
    id: int = Field(..., description="Equipment ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    name: str = Field(..., description="Equipment name")
    equipment_id: Optional[str] = Field(None, description="Equipment ID")
    category: Optional[str] = Field(None, description="Equipment category")
    serial_number: Optional[str] = Field(None, description="Equipment serial number")
    model: Optional[str] = Field(None, description="Equipment model")
    assigned_to: Optional[str] = Field(None, description="Assigned to")
    purchase_date: Optional[datetime] = Field(None, description="Purchase date")
    is_active: bool = Field(..., description="Equipment active status")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
