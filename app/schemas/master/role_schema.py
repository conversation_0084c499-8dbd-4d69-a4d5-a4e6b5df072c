"""Schemas for master role management.

This module contains Pydantic schemas for master role CRUD operations.
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class ModulePermission(BaseModel):
    """Schema for module permission mapping."""
    
    module_id: int = Field(..., description="Module ID")
    view: bool = Field(default=False, description="View permission")
    edit: bool = Field(default=False, description="Edit permission")
    create: bool = Field(default=False, description="Create permission")
    list: bool = Field(default=False, description="List permission")


class MasterRoleCreate(BaseModel):
    """Schema for creating a new master role record."""
    
    name: str = Field(..., max_length=20, description="Role name")
    description: Optional[str] = Field(None, description="Role description")
    module: List[ModulePermission] = Field(..., description="Module permissions")


class MasterRoleUpdate(BaseModel):
    """Schema for updating an existing master role record."""
    
    name: Optional[str] = Field(None, max_length=20, description="Role name")
    description: Optional[str] = Field(None, max_length=200, description="Role description")
    module: Optional[List[ModulePermission]] = Field(None, description="Module permissions")
    role_id: Optional[int] = Field(None, description="Role ID for update")


class MasterRoleResponse(BaseModel):
    """Schema for master role API responses."""
    
    id: int = Field(..., description="Role ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    name: str = Field(..., description="Role name")
    description: str = Field(..., description="Role description")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class RoleWithPermissionsResponse(BaseModel):
    """Schema for role response with module permissions."""
    
    id: int = Field(..., description="Role ID")
    role: str = Field(..., description="Role name")
    description: str = Field(..., description="Role description")
    module: List[ModulePermission] = Field(..., description="Module permissions")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
