"""Schema definitions for MasterClient model."""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class MasterClientCreate(BaseModel):
    """Schema for creating new master client.
    
    Attributes:
        name: Client name
        client_id: Client ID reference
        start_date: Client start date
        address: Client address
        city: Client city
        country: Client country
        zip_code: Client zip code
        contact_manager: Contact manager name
        contact_manager_email: Contact manager email
        contact_manager_phone: Contact manager phone
        notes: Additional notes
        is_active: Whether the client is active
    """
    
    name: str = Field(..., min_length=1, max_length=200, description="Client name")
    client_id: str = Field(..., description="Client ID reference")
    start_date: Optional[datetime] = Field(None, description="Client start date")
    street_address: Optional[str] = Field(None, max_length=200, description="Client address")
    city: Optional[str] = Field(None, max_length=200, description="Client city")
    country: Optional[str] = Field(None, max_length=200, description="Client country")
    zip_code: Optional[str] = Field(None, max_length=200, description="Client zip code")
    contact_manager: Optional[str] = Field(None, max_length=200, description="Contact manager name")
    contact_manager_email: Optional[str] = Field(None, max_length=200, description="Contact manager email")
    contact_manager_phone: Optional[str] = Field(None, max_length=200, description="Contact manager phone")
    notes: Optional[str] = Field(None, max_length=200, description="Additional notes")
    is_active: bool = Field(default=True, description="Whether the client is active")


class MasterClientUpdate(BaseModel):
    """Schema for updating master client.
    
    All fields are optional for partial updates.
    """
    
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Client name")
    client_id: Optional[str] = Field(None, description="Client ID reference")
    start_date: Optional[datetime] = Field(None, description="Client start date")
    address: Optional[str] = Field(None, max_length=200, description="Client address")
    city: Optional[str] = Field(None, max_length=200, description="Client city")
    country: Optional[str] = Field(None, max_length=200, description="Client country")
    zip_code: Optional[str] = Field(None, max_length=200, description="Client zip code")
    contact_manager: Optional[str] = Field(None, max_length=200, description="Contact manager name")
    contact_manager_email: Optional[str] = Field(None, max_length=200, description="Contact manager email")
    contact_manager_phone: Optional[str] = Field(None, max_length=200, description="Contact manager phone")
    notes: Optional[str] = Field(None, max_length=200, description="Additional notes")
    is_active: Optional[bool] = Field(None, description="Whether the client is active")


class MasterClientResponse(BaseModel):
    """Schema for master client API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    name: str = Field(..., description="Client name")
    client_id: str = Field(..., description="Client ID reference")
    start_date: Optional[datetime] = Field(None, description="Client start date")
    end_date: Optional[datetime] = Field(None, description="Client end date")
    street_address: Optional[str] = Field(None, description="Client street address")
    city: Optional[str] = Field(None, description="Client city")
    country: Optional[str] = Field(None, description="Client country")
    zip_code: Optional[str] = Field(None, description="Client zip code")
    contact_manager: Optional[str] = Field(None, description="Contact manager name")
    contact_manager_email: Optional[str] = Field(None, description="Contact manager email")
    contact_manager_phone: Optional[str] = Field(None, description="Contact manager phone")
    notes: Optional[str] = Field(None, description="Additional notes")
    is_active: bool = Field(..., description="Whether the client is active")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    emp_count: Optional[int] = Field(None, description="Employee count")

class TalentClientInfoResponse(BaseModel):
    """Schema for talent client info response."""
    id: int = Field(..., description="Primary key")
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    position: str = Field(..., description="Position")
    start_date: Optional[datetime] = Field(None, description="Start date")
