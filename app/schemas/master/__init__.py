"""Master data schemas package.

This package contains Pydantic schemas for master data entities
including client information and other reference data.
"""

from .client_info_schema import MasterClientInfoCreate, MasterClientInfoUpdate, MasterClientInfoResponse
from .equipment_schema import MasterEquipmentCreate, MasterE<PERSON>pmentUpdate, MasterEquipmentResponse
from .role_schema import MasterRoleCreate, MasterRoleUpdate, MasterRoleResponse
from .module_schema import MasterModuleResponse
from .role_module_permission_mapping_schema import (
    RoleModulePermissionMappingCreate,
    RoleModulePermissionMappingUpdate,
    RoleModulePermissionMappingResponse
)

__all__ = [
    "MasterClientInfoCreate",
    "MasterClientInfoUpdate", 
    "MasterClientInfoResponse",
    "MasterEquipmentCreate",
    "MasterEquipmentUpdate",
    "MasterEquipmentResponse",
    "MasterRoleCreate",
    "MasterRoleUpdate",
    "MasterRoleResponse",
    "MasterModuleResponse",
    "RoleModulePermissionMappingCreate",
    "RoleModulePermissionMappingUpdate",
    "RoleModulePermissionMappingResponse",
]