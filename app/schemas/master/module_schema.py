"""Schema for master module management.

This module contains Pydantic schemas for master module operations.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class MasterModuleResponse(BaseModel):
    """Schema for master module API responses."""
    
    id: int = Field(..., description="Module ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    name: str = Field(..., description="Module name")
    is_active: bool = Field(..., description="Module status")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True