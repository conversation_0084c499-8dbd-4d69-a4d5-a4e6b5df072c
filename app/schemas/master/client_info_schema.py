"""Client information schema definitions for master data management.

This module contains Pydantic models for client information validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import Field, EmailStr
from app.schemas.base import CamelCaseModel


class MasterClientInfoCreate(CamelCaseModel):
    """Schema for creating new client information.
    
    Attributes:
        name: Name of the client
        address: Address of the client
        city: City of the client
        state: State of the client
        country: Country of the client
        zip_code: Zip code of the client
        phone: Phone number of the client
        email: Email of the client
        website: Website of the client
        notes: Notes of the client
        is_active: Whether the client is active
    """
    
    name: str = Field(..., min_length=1, max_length=20, description="Name of the client")
    address: Optional[str] = Field(None, max_length=200, description="Address of the client")
    city: Optional[str] = Field(None, max_length=20, description="City of the client")
    state: Optional[str] = Field(None, max_length=20, description="State of the client")
    country: Optional[str] = Field(None, max_length=20, description="Country of the client")
    zip_code: Optional[str] = Field(None, max_length=10, description="Zip code of the client")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number of the client")
    email: Optional[EmailStr] = Field(None, max_length=50, description="Email of the client")
    website: Optional[str] = Field(None, max_length=200, description="Website of the client")
    notes: Optional[str] = Field(None, max_length=200, description="Notes of the client")
    is_active: bool = Field(default=True, description="Whether the client is active")


class MasterClientInfoUpdate(CamelCaseModel):
    """Schema for updating client information.
    
    All fields are optional for partial updates.
    """
    
    name: Optional[str] = Field(None, min_length=1, max_length=20, description="Name of the client")
    address: Optional[str] = Field(None, max_length=200, description="Address of the client")
    city: Optional[str] = Field(None, max_length=20, description="City of the client")
    state: Optional[str] = Field(None, max_length=20, description="State of the client")
    country: Optional[str] = Field(None, max_length=20, description="Country of the client")
    zip_code: Optional[str] = Field(None, max_length=10, description="Zip code of the client")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number of the client")
    email: Optional[EmailStr] = Field(None, max_length=50, description="Email of the client")
    website: Optional[str] = Field(None, max_length=200, description="Website of the client")
    notes: Optional[str] = Field(None, max_length=200, description="Notes of the client")
    is_active: Optional[bool] = Field(None, description="Whether the client is active")


class MasterClientInfoResponse(CamelCaseModel):
    """Schema for client information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    name: str = Field(..., description="Name of the client")
    address: Optional[str] = Field(None, description="Address of the client")
    city: Optional[str] = Field(None, description="City of the client")
    state: Optional[str] = Field(None, description="State of the client")
    country: Optional[str] = Field(None, description="Country of the client")
    zip_code: Optional[str] = Field(None, description="Zip code of the client")
    phone: Optional[str] = Field(None, description="Phone number of the client")
    email: Optional[str] = Field(None, description="Email of the client")
    website: Optional[str] = Field(None, description="Website of the client")
    notes: Optional[str] = Field(None, description="Notes of the client")
    is_active: bool = Field(..., description="Whether the client is active")
