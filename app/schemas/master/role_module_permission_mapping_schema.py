"""Schemas for role module permission mapping management.

This module contains Pydantic schemas for role module permission mapping CRUD operations.
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class RoleModulePermissionMappingCreate(BaseModel):
    """Schema for creating a new role module permission mapping record."""
    
    role_id: int = Field(..., description="Role ID")
    module_id: int = Field(..., description="Module ID")
    can_list: bool = Field(default=False, description="Can list permission")
    can_create: bool = Field(default=False, description="Can create permission")
    can_update: bool = Field(default=False, description="Can update permission")
    can_delete: bool = Field(default=False, description="Can delete permission")


class RoleModulePermissionMappingUpdate(BaseModel):
    """Schema for updating an existing role module permission mapping record."""
    
    can_list: Optional[bool] = Field(None, description="Can list permission")
    can_create: Optional[bool] = Field(None, description="Can create permission")
    can_update: Optional[bool] = Field(None, description="Can update permission")
    can_delete: Optional[bool] = Field(None, description="Can delete permission")


class RoleModulePermissionMappingResponse(BaseModel):
    """Schema for role module permission mapping API responses."""
    
    id: int = Field(..., description="Mapping ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    role_id: int = Field(..., description="Role ID")
    module_id: int = Field(..., description="Module ID")
    can_list: bool = Field(..., description="Can list permission")
    can_create: bool = Field(..., description="Can create permission")
    can_update: bool = Field(..., description="Can update permission")
    can_delete: bool = Field(..., description="Can delete permission")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True