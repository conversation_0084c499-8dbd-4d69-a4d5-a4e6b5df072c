"""User schemas for request/response validation."""

from typing import Optional
from pydantic import BaseModel, EmailStr


class UserCreate(BaseModel):
    """Schema for creating a new user."""

    name: str
    email: EmailStr
    phone: str
    password: str
    is_superuser: bool = False
    role_id: Optional[int] = None
    department: Optional[str] = None


class UserUpdate(BaseModel):
    """Schema for updating user data."""

    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    password: Optional[str] = None
    is_superuser: Optional[bool] = None
    role_id: Optional[int] = None
    department: Optional[str] = None


class UserResponse(BaseModel):
    """Schema for user response data."""

    id: int
    name: str
    email: str
    phone: str
    pic: Optional[str] = None
    department: Optional[str] = None
    role_id: Optional[int] = None
    is_active: bool = False
    is_superuser: bool = False
