"""Base schema classes for consistent API formatting.

This module contains base classes that provide consistent camelCase formatting
for API requests and responses while maintaining snake_case field access in Python.
"""

from typing import Dict, Any
from pydantic import BaseModel
from sqlmodel import SQLModel


def to_camel_case(snake_str: str) -> str:
    """Convert snake_case string to camelCase.
    
    Args:
        snake_str: String in snake_case format
        
    Returns:
        String in camelCase format
    """
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


class CamelCaseModel(BaseModel):
    """Base model class that provides camelCase serialization.
    
    This class automatically converts snake_case field names to camelCase
    for API responses while allowing both formats for input validation.
    """
    
    def model_dump(self, **kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Override model_dump to return camelCase keys."""
        original_dict = super().model_dump(mode='python', **kwargs)
        return {to_camel_case(key): value for key, value in original_dict.items()}
    
    class Config:
        alias_generator = to_camel_case
        populate_by_name = True
        from_attributes = True


class CamelCaseSQLModel(SQLModel):
    """Base SQLModel class that provides camelCase serialization.
    
    This class automatically converts snake_case field names to camelCase
    for API responses while allowing both formats for input validation.
    """
    
    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Override model_dump to return camelCase keys."""
        original_dict = super().model_dump(**kwargs)
        return {to_camel_case(key): value for key, value in original_dict.items()}
    
    class Config:
        alias_generator = to_camel_case
        populate_by_name = True
        from_attributes = True
