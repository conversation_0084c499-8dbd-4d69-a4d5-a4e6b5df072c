"""User repository for database operations."""

from typing import Annotated, Optional, List, Dict, Any
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import User
from app.db.session import get_session


class UserRepository:
    """Repository for user-related database operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        self.db = db

    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        statement = select(User).where(User.email == email)
        return self.db.exec(statement).first()

    def get_by_id(self, user_id: int) -> User:
        """Get user by ID."""
        user = self.db.get(User, user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
        return user

    def get_all(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all users with pagination."""
        statement = select(User).offset(skip).limit(limit).order_by(User.id.desc()) 
        return list(self.db.exec(statement).all())

    def create(self, user_data: Dict[str, Any]) -> User:
        """Create a new user."""
        user = User(**user_data)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user

    def update(self, user: User):
        """Update an existing user."""
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)

    def delete(self, user: User):
        """Delete a user."""
        self.db.delete(user)
        self.db.commit()

    def toggle_status(self, user: User) -> User:
        """Toggle user active status."""
        user.is_active = not user.is_active
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user
