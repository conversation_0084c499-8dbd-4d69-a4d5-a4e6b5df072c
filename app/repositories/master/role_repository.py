"""Repository for master role management.

This module contains the repository class for master role CRUD operations.
"""

from typing import List, Optional, Annotated, Dict, Any
from sqlmodel import Session, select
from fastapi import Depends
from app.db import get_session, MasterRole, RoleModulePermissionMapping
from app.schemas.master.role_schema import MasterRoleC<PERSON>, MasterRoleUpdate
from app.core.logs import log_database_error


class RoleRepository:
    """Repository class for master role CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def create(self, role_data: MasterRoleCreate) -> MasterRole:
        """Create a new master role record.
        
        Args:
            role_data: Role data to create
            
        Returns:
            Created role record
            
        Raises:
            Exception: If creation fails
        """
        try:
            role = MasterRole(**role_data.model_dump())
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="CREATE",
                table="MasterRole",
                error=e,
                additional_context={"role_data": role_data.model_dump()}
            )
            raise
    
    def get_by_id(self, role_id: int) -> Optional[MasterRole]:
        """Get role by ID.
        
        Args:
            role_id: Role ID
            
        Returns:
            Role record if found, None otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.id == role_id)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id}
            )
            return None
    
    def update_role_with_permissions(self, role_id: int, role_data: MasterRoleUpdate) -> Optional[MasterRole]:
        """Update role with its module permissions.
        
        Args:
            role_id: Role ID to update
            role_data: Updated role data including module permissions
            
        Returns:
            Updated role record if successful, None otherwise
            
        Raises:
            Exception: If update fails
        """
        try:
            # Get the existing role
            role = self.get_by_id(role_id)
            if not role:
                return None
            
            # Update role basic information
            update_data = role_data.model_dump(exclude={'module', 'role_id'}, exclude_none=True)
            for field, value in update_data.items():
                if field in role.__fields__:
                    setattr(role, field, value)
            
            self.db.add(role)
            self.db.flush()  # Get updated role without committing
            
            # Update module permissions if provided
            if role_data.module is not None:
                # Delete existing permissions for this role
                delete_statement = select(RoleModulePermissionMapping).where(
                    RoleModulePermissionMapping.role_id == role_id
                )
                existing_permissions = self.db.exec(delete_statement).all()
                for perm in existing_permissions:
                    self.db.delete(perm)
                
                # Add new permissions
                for module_perm in role_data.module:
                    permission_mapping = RoleModulePermissionMapping(
                        role_id=role_id,
                        module_id=module_perm.module_id,
                        can_list=module_perm.list,
                        can_create=module_perm.create,
                        can_update=module_perm.edit,
                        can_view=module_perm.view
                    )
                    self.db.add(permission_mapping)
            
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE_WITH_PERMISSIONS",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id, "role_data": role_data.model_dump()}
            )
            raise
    
    def get_role_with_permissions(self, role_id: int) -> Optional[Dict[str, Any]]:
        """Get role with its module permissions.
        
        Args:
            role_id: Role ID
            
        Returns:
            Dictionary with role data and permissions, None if not found
        """
        try:
            # Get the role first
            role = self.get_by_id(role_id)
            if not role:
                return None
            
            # Get role permissions
            permissions_statement = select(RoleModulePermissionMapping).where(
                RoleModulePermissionMapping.role_id == role_id
            )
            permissions_result = self.db.exec(permissions_statement)
            permissions = list(permissions_result.all())
            
            # Build the response structure
            module_permissions: List[Dict[str, Any]] = []
            for perm in permissions:
                module_permissions.append({
                    "module_id": perm.module_id,
                    "view": perm.can_view,
                    "edit": perm.can_update,
                    "create": perm.can_create,
                    "list": perm.can_list
                })
            
            return {
                "id": role.id,
                "role": role.name,
                "description": role.description,
                "module": module_permissions
            }
        except Exception as e:
            log_database_error(
                operation="SELECT_WITH_PERMISSIONS",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id}
            )
            return None
    
    def create_role_with_permissions(self, role_data: MasterRoleCreate) -> MasterRole:
        """Create a new role with associated module permissions.
        
        Args:
            role_data: Role data including module permissions
            
        Returns:
            Created role record
            
        Raises:
            Exception: If creation fails
        """
        try:
            # Create the role first
            role_dict = role_data.model_dump(exclude={'module'})
            role = MasterRole(**role_dict)
            self.db.add(role)
            self.db.flush()  # Get the role ID without committing
            
            # Create permission mappings
            if role_data.module and role.id is not None:
                for module_perm in role_data.module:
                    permission_mapping = RoleModulePermissionMapping(
                        role_id=role.id,
                        module_id=module_perm.module_id,
                        can_list=module_perm.list,
                        can_create=module_perm.create,
                        can_update=module_perm.edit,
                        can_view=module_perm.view
                    )
                    self.db.add(permission_mapping)
            
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="CREATE_WITH_PERMISSIONS",
                table="MasterRole",
                error=e,
                additional_context={"role_data": role_data.model_dump()}
            )
            raise
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterRole]:
        """Get all role records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of role records
        """
        try:
            statement = select(MasterRole).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, role_id: int, role_data: MasterRoleUpdate) -> Optional[MasterRole]:
        """Update an existing role record.
        
        Args:
            role_id: Role ID to update
            role_data: Updated role data
            
        Returns:
            Updated role record if successful, None otherwise
            
        Raises:
            Exception: If update fails
        """
        try:
            role = self.get_by_id(role_id)
            if not role:
                return None
            
            update_data = role_data.model_dump(exclude_none=True)
            for field, value in update_data.items():
                setattr(role, field, value)
            
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id, "role_data": role_data.model_dump()}
            )
            raise
    
    def delete(self, role_id: int) -> bool:
        """Delete a role record.
        
        Args:
            role_id: Role ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
            
        Raises:
            Exception: If deletion fails
        """
        try:
            role = self.get_by_id(role_id)
            if not role:
                return False
            
            self.db.delete(role)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id}
            )
            raise
    
    def check_if_exists(self, role_id: int) -> bool:
        """Check if role exists.
        
        Args:
            role_id: Role ID to check
            
        Returns:
            True if role exists, False otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.id == role_id)
            result = self.db.exec(statement)
            return result.first() is not None
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id, "operation": "check_if_exists"}
            )
            return False
    
    def get_by_name(self, name: str) -> Optional[MasterRole]:
        """Get role by name.
        
        Args:
            name: Role name
            
        Returns:
            Role record if found, None otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.name == name)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"name": name}
            )
            return None
