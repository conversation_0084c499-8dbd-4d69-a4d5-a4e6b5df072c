"""Repository for MasterClient database operations."""

from typing import Annotated, Dict, Any, List, Optional, Sequence
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import MasterClient, TalentClientInfo
from app.db.session import get_session
from app.core.logs import log_database_error


class MasterClientRepository:
    """Repository for master client-related database operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        self.db = db

    def get_by_id(self, client_id: int) -> Optional[MasterClient]:
        """Get master client by ID."""
        try:
            client = self.db.get(MasterClient, client_id)
            return client
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"client_id": client_id}
            )
            return None

    def get_by_name(self, name: str) -> Optional[MasterClient]:
        """Get master client by name."""
        try:
            statement = select(MasterClient).where(MasterClient.name == name)
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"name": name}
            )
            return None

    def get_by_client_id(self, client_id: str) -> Optional[MasterClient]:
        """Get master client by client_id field."""
        try:
            statement = select(MasterClient).where(MasterClient.client_id == client_id)
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"client_id": client_id}
            )
            return None

    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterClient]:
        """Get all master clients with pagination."""
        try:
            statement = select(MasterClient).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []

    def get_active_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClient]:
        """Get all active master clients with pagination."""
        try:
            statement = (
                select(MasterClient)
                .where(MasterClient.is_active == True)
                .offset(skip)
                .limit(limit)
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"skip": skip, "limit": limit, "is_active": True}
            )
            return []

    def get_inactive_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClient]:
        """Get all inactive master clients with pagination."""
        try:
            statement = (
                select(MasterClient)
                .where(MasterClient.is_active == False)
                .offset(skip)
                .limit(limit)
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"skip": skip, "limit": limit, "is_active": False}
            )
            return []

    def create(self, client_data: Dict[str, Any]) -> MasterClient:
        """Create a new master client."""
        try:
            client = MasterClient(**client_data)
            self.db.add(client)
            self.db.commit()
            self.db.refresh(client)
            return client
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="INSERT",
                table="MasterClient",
                error=e,
                additional_context=client_data
            )
            raise e

    def update(self, client: MasterClient, update_data: Dict[str, Any]) -> MasterClient:
        """Update an existing master client."""
        try:
            for field, value in update_data.items():
                if hasattr(client, field) and value is not None:
                    setattr(client, field, value)
            
            self.db.add(client)
            self.db.commit()
            self.db.refresh(client)
            return client
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="MasterClient",
                error=e,
                additional_context={"client_id": client.id, "update_data": update_data}
            )
            raise e

    def get_emp_count_by_client_id(self, client_id: int, is_count: bool = True) -> Sequence[TalentClientInfo] | int:
        """Get total employee count."""
        try:
            statement = select(TalentClientInfo).where(TalentClientInfo.client_id == client_id)
            if is_count:
                return len(self.db.exec(statement).all())
            return self.db.exec(statement).all()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="Employee",
                error=e,
                additional_context={"operation": "get_emp_count_by_client_id"}
            )
            return 0

    def delete(self, client: MasterClient) -> None:
        """Delete a master client."""
        try:
            self.db.delete(client)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="MasterClient",
                error=e,
                additional_context={"client_id": client.id}
            )
            raise e

    def toggle_status(self, client: MasterClient) -> MasterClient:
        """Toggle master client active status."""
        try:
            client.is_active = not client.is_active
            self.db.add(client)
            self.db.commit()
            self.db.refresh(client)
            return client
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="MasterClient",
                error=e,
                additional_context={"client_id": client.id, "toggle_status": True}
            )
            raise e

    def search_by_name(self, name_pattern: str, skip: int = 0, limit: int = 100) -> List[MasterClient]:
        """Search master clients by name pattern (simple contains check)."""
        try:
            all_clients = self.get_all(skip=0, limit=1000)  # Get more records for filtering
            filtered_clients = [
                client for client in all_clients 
                if name_pattern.lower() in client.name.lower()
            ]
            
            # Apply pagination to filtered results
            start_idx = skip
            end_idx = skip + limit
            return filtered_clients[start_idx:end_idx]
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterClient",
                error=e,
                additional_context={"name_pattern": name_pattern, "skip": skip, "limit": limit}
            )
            return []
