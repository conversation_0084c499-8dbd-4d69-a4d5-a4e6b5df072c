"""Repository for master equipment management.

This module contains the repository class for master equipment CRUD operations.
"""

from typing import List, Optional, Annotated
from sqlmodel import Session, select
from fastapi import Depends
from app.db import get_session, MasterEquipment
from app.db.models import TalentEquipmentMapping, TalentProfile
from app.schemas.master.equipment_schema import MasterEquipmentCreate, MasterEquipmentUpdate
from app.core.logs import log_database_error


class EquipmentRepository:
    """Repository class for master equipment CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def create(self, equipment_data: MasterEquipmentCreate) -> MasterEquipment:
        """Create a new master equipment record.
        
        Args:
            equipment_data: Equipment data to create
            
        Returns:
            Created equipment record
            
        Raises:
            Exception: If creation fails
        """
        try:
            equipment = MasterEquipment(**equipment_data.model_dump())
            self.db.add(equipment)
            self.db.commit()
            self.db.refresh(equipment)
            return equipment
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="CREATE",
                table="MasterEquipment",
                error=e,
                additional_context={"equipment_data": equipment_data.model_dump()}
            )
            raise
    
    def get_by_id(self, equipment_id: int) -> Optional[MasterEquipment]:
        """Get master equipment record by ID.
        
        Args:
            equipment_id: Equipment ID to search for
            
        Returns:
            Equipment record if found, None otherwise
            
        Raises:
            Exception: If query fails
        """
        try:
            statement = select(MasterEquipment).where(MasterEquipment.id == equipment_id)
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="GET_BY_ID",
                table="MasterEquipment",
                error=e,
                additional_context={"equipment_id": equipment_id}
            )
            raise
    
    def get_by_equipment_id(self, id: int) -> Optional[MasterEquipment]:
        """Get master equipment record by equipment ID.
        
        Args:
            id: Equipment ID to search for
            
        Returns:
            Equipment record if found, None otherwise
            
        Raises:
            Exception: If query fails
        """
        try:
            statement = select(MasterEquipment).where(MasterEquipment.id == id)
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="GET_BY_ID",
                table="MasterEquipment",
                error=e,
                additional_context={"id": id}
            )
            raise
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterEquipment]:
        """Get all master equipment records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment records
            
        Raises:
            Exception: If query fails
        """
        try:
            statement = select(MasterEquipment).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="GET_ALL",
                table="MasterEquipment",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            raise
    
    def update(self, equipment_id: int, equipment_data: MasterEquipmentUpdate | MasterEquipment) -> Optional[MasterEquipment]:
        """Update master equipment record.
        
        Args:
            equipment_id: Equipment ID to update
            equipment_data: Equipment data to update
            
        Returns:
            Updated equipment record if found, None otherwise
            
        Raises:
            Exception: If update fails
        """
        try:
            if isinstance(equipment_data, MasterEquipmentUpdate):
                equipment = self.get_by_id(equipment_id)
                update_data = equipment_data.model_dump(exclude_unset=True)
                if not equipment:
                    return None
                for field, value in update_data.items():
                    setattr(equipment, field, value)
            else:
                equipment = equipment_data
            
            self.db.add(equipment)
            self.db.commit()
            self.db.refresh(equipment)
            return equipment
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="MasterEquipment",
                error=e,
                additional_context={
                    "equipment_id": equipment_id,
                    "equipment_data": equipment_data.model_dump(exclude_unset=True)
                }
            )
            raise
    
    def delete(self, equipment_id: int) -> bool:
        """Delete master equipment record.
        
        Args:
            equipment_id: Equipment ID to delete
            
        Returns:
            True if deleted, False if not found
            
        Raises:
            Exception: If deletion fails
        """
        try:
            equipment = self.get_by_id(equipment_id)
            if not equipment:
                return False
            
            self.db.delete(equipment)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="MasterEquipment",
                error=e,
                additional_context={"equipment_id": equipment_id}
            )
            raise
    
    def count(self) -> int:
        """Get total count of master equipment records.
        
        Returns:
            Total count of equipment records
            
        Raises:
            Exception: If count fails
        """
        try:
            statement = select(MasterEquipment)
            return len(list(self.db.exec(statement).all()))
        except Exception as e:
            log_database_error(
                operation="COUNT",
                table="MasterEquipment",
                error=e
            )
            raise

    def get_talent_by_equipment_id(self, equipment_id: int) -> str | None:
        """Get talent name by equipment ID.
        
        Args:
            equipment_id: Equipment ID to search for
            
        Returns:
            Talent name if found, None otherwise
            
        Raises:
            Exception: If query fails
        """
        try:
            statement = select(TalentEquipmentMapping).where(TalentEquipmentMapping.master_equipment_id == equipment_id).where(TalentEquipmentMapping.is_active == True)
            talent = self.db.exec(statement).first()
            if not talent:
                return None
            talent = select(TalentProfile).where(TalentProfile.id == talent.talent_profile_id)
            talent = self.db.exec(talent).first()
            if not talent:
                return None
            return talent.first_name + " " + talent.last_name

        except Exception as e:
            log_database_error(
                operation="GET_TALENT_BY_EQUIPMENT_ID",
                table="Talent",
                error=e,
                additional_context={"equipment_id": equipment_id}
            )
            raise
