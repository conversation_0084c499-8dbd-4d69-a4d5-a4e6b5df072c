"""Repository for role module permission mapping management.

This module contains the repository class for role module permission mapping CRUD operations.
"""

from typing import List, Optional, Annotated
from sqlmodel import Session, select
from fastapi import Depends
from app.db import get_session, RoleModulePermissionMapping
from app.schemas.master.role_module_permission_mapping_schema import (
    RoleModulePermissionMappingCreate,
    RoleModulePermissionMappingUpdate
)
from app.core.logs import log_database_error


class RoleModulePermissionMappingRepository:
    """Repository class for role module permission mapping CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def create(self, mapping_data: RoleModulePermissionMappingCreate) -> RoleModulePermissionMapping:
        """Create a new role module permission mapping record.
        
        Args:
            mapping_data: Mapping data to create
            
        Returns:
            Created mapping record
            
        Raises:
            Exception: If database operation fails
        """
        try:
            db_mapping = RoleModulePermissionMapping.model_validate(mapping_data.model_dump())
            self.db.add(db_mapping)
            self.db.commit()
            self.db.refresh(db_mapping)
            return db_mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="INSERT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"mapping_data": mapping_data.model_dump()}
            )
            raise
    
    def get_by_id(self, mapping_id: int) -> Optional[RoleModulePermissionMapping]:
        """Get mapping by ID.
        
        Args:
            mapping_id: Mapping ID
            
        Returns:
            Mapping record if found, None otherwise
        """
        try:
            return self.db.get(RoleModulePermissionMapping, mapping_id)
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"mapping_id": mapping_id}
            )
            raise
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMapping]:
        """Get all mapping records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping records
        """
        try:
            statement = select(RoleModulePermissionMapping).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            raise
    
    def get_by_role_id(self, role_id: int, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMapping]:
        """Get mappings by role ID.
        
        Args:
            role_id: Role ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping records for the role
        """
        try:
            statement = select(RoleModulePermissionMapping).where(
                RoleModulePermissionMapping.role_id == role_id
            ).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"role_id": role_id, "skip": skip, "limit": limit}
            )
            raise
    
    def get_by_module_id(self, module_id: int, skip: int = 0, limit: int = 100) -> List[RoleModulePermissionMapping]:
        """Get mappings by module ID.
        
        Args:
            module_id: Module ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of mapping records for the module
        """
        try:
            statement = select(RoleModulePermissionMapping).where(
                RoleModulePermissionMapping.module_id == module_id
            ).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"module_id": module_id, "skip": skip, "limit": limit}
            )
            raise
    
    def get_by_role_and_module(self, role_id: int, module_id: int) -> Optional[RoleModulePermissionMapping]:
        """Get mapping by role ID and module ID.
        
        Args:
            role_id: Role ID
            module_id: Module ID
            
        Returns:
            Mapping record if found, None otherwise
        """
        try:
            statement = select(RoleModulePermissionMapping).where(
                RoleModulePermissionMapping.role_id == role_id,
                RoleModulePermissionMapping.module_id == module_id
            )
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"role_id": role_id, "module_id": module_id}
            )
            raise
    
    def update(self, mapping_id: int, mapping_data: RoleModulePermissionMappingUpdate) -> Optional[RoleModulePermissionMapping]:
        """Update an existing mapping record.
        
        Args:
            mapping_id: Mapping ID to update
            mapping_data: Updated mapping data
            
        Returns:
            Updated mapping record if found, None otherwise
            
        Raises:
            Exception: If database operation fails
        """
        try:
            db_mapping = self.db.get(RoleModulePermissionMapping, mapping_id)
            if not db_mapping:
                return None
            
            update_data = mapping_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_mapping, field, value)
            
            self.db.add(db_mapping)
            self.db.commit()
            self.db.refresh(db_mapping)
            return db_mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"mapping_id": mapping_id, "mapping_data": mapping_data.model_dump()}
            )
            raise
    
    def delete(self, mapping_id: int) -> bool:
        """Delete a mapping record.
        
        Args:
            mapping_id: Mapping ID to delete
            
        Returns:
            True if deleted successfully, False if not found
            
        Raises:
            Exception: If database operation fails
        """
        try:
            db_mapping = self.db.get(RoleModulePermissionMapping, mapping_id)
            if not db_mapping:
                return False
            
            self.db.delete(db_mapping)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"mapping_id": mapping_id}
            )
            raise
    
    def check_if_exists(self, mapping_id: int) -> bool:
        """Check if mapping exists.
        
        Args:
            mapping_id: Mapping ID to check
            
        Returns:
            True if mapping exists, False otherwise
        """
        try:
            statement = select(RoleModulePermissionMapping).where(RoleModulePermissionMapping.id == mapping_id)
            result = self.db.exec(statement)
            return result.first() is not None
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="RoleModulePermissionMapping",
                error=e,
                additional_context={"mapping_id": mapping_id}
            )
            raise