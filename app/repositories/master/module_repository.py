"""Repository for master module management.

This module contains the repository class for master module read operations.
"""

from typing import List, Optional, Annotated
from sqlmodel import Session, select
from fastapi import Depends
from app.db import get_session, MasterModule
from app.core.logs import log_database_error


class ModuleRepository:
    """Repository class for master module read operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def get_by_id(self, module_id: int) -> Optional[MasterModule]:
        """Get module by ID.
        
        Args:
            module_id: Module ID
            
        Returns:
            Module record if found, None otherwise
        """
        try:
            statement = select(MasterModule).where(MasterModule.id == module_id)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterModule",
                error=e,
                additional_context={"module_id": module_id}
            )
            return None
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterModule]:
        """Get all module records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of module records
        """
        try:
            statement = select(MasterModule).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterModule",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def get_active_modules(self, skip: int = 0, limit: int = 100) -> List[MasterModule]:
        """Get all active module records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of active module records
        """
        try:
            statement = select(MasterModule).where(MasterModule.is_active == True).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterModule",
                error=e,
                additional_context={"skip": skip, "limit": limit, "filter": "active_only"}
            )
            return []
    
    def check_if_exists(self, module_id: int) -> bool:
        """Check if module exists.
        
        Args:
            module_id: Module ID to check
            
        Returns:
            True if module exists, False otherwise
        """
        try:
            statement = select(MasterModule).where(MasterModule.id == module_id)
            result = self.db.exec(statement)
            return result.first() is not None
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterModule",
                error=e,
                additional_context={"module_id": module_id, "operation": "check_if_exists"}
            )
            return False
    
    def get_by_name(self, name: str) -> Optional[MasterModule]:
        """Get module by name.
        
        Args:
            name: Module name
            
        Returns:
            Module record if found, None otherwise
        """
        try:
            statement = select(MasterModule).where(MasterModule.name == name)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterModule",
                error=e,
                additional_context={"name": name}
            )
            return None