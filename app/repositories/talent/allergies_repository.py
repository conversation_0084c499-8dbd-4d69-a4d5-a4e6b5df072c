"""Repository for managing talent allergies data."""

from typing import Annotated, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentAllergies
from app.db.session import get_session


class TalentAllergiesRepository:
    """Repository class for talent allergies CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_allergy(
        self, allergy: TalentAllergies
    ) -> TalentAllergies:
        """Create a new allergy record.
        
        Args:
            allergy: The allergy data to create
            
        Returns:
            The created allergy record
        """
        self.db.add(allergy)
        self.db.commit()
        self.db.refresh(allergy)
        return allergy

    def get_allergy_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentAllergies]:
        """Get an allergy for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            Allergy record
        """
        statement = select(TalentAllergies).where(
            TalentAllergies.talent_profile_id == talent_id
        )
        return self.db.exec(statement).first()

    def update_allergy(
        self, allergy: TalentAllergies
    ) -> TalentAllergies:
        """Update an existing allergy record.
        
        Args:
            allergy: The updated allergy data
            
        Returns:
            The updated allergy record
        """
        self.db.add(allergy)
        self.db.commit()
        self.db.refresh(allergy)
        return allergy
