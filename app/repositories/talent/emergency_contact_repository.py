"""Repository for talent emergency contact information operations."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentEmergencyContactMapping
from app.schemas.talent import TalentEmergency<PERSON>ontactCreate, TalentEmergencyContactUpdate
from app.db.session import get_session


class EmergencyContactRepository:
    """Repository for talent emergency contact information CRUD operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session
        """
        self.db = db

    def create_emergency_contact(
        self, emergency_contact_data: TalentEmergencyContactCreate
    ) -> TalentEmergencyContactMapping:
        """Create a new emergency contact record.
        
        Args:
            emergency_contact_data: Emergency contact data to create
            
        Returns:
            Created emergency contact record
        """
        emergency_contact = TalentEmergencyContactMapping(**emergency_contact_data.model_dump())
        self.db.add(emergency_contact)
        self.db.commit()
        self.db.refresh(emergency_contact)
        return emergency_contact

    def get_emergency_contact_by_id(self, emergency_contact_id: int) -> Optional[TalentEmergencyContactMapping]:
        """Get emergency contact by ID.
        
        Args:
            emergency_contact_id: Emergency contact ID
            
        Returns:
            Emergency contact record if found, None otherwise
        """
        statement = select(TalentEmergencyContactMapping).where(
            TalentEmergencyContactMapping.id == emergency_contact_id
        )
        return self.db.exec(statement).first()

    def get_emergency_contacts_by_talent_id(self, talent_id: int) -> List[TalentEmergencyContactMapping]:
        """Get all emergency contacts for a specific talent.
        
        Args:
            talent_id: Talent profile ID
            
        Returns:
            List of emergency contact records
        """
        statement = select(TalentEmergencyContactMapping).where(
            TalentEmergencyContactMapping.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def update_emergency_contact(
        self, 
        emergency_contact_id: int, 
        emergency_contact_data: TalentEmergencyContactUpdate
    ) -> Optional[TalentEmergencyContactMapping]:
        """Update an existing emergency contact record.
        
        Args:
            emergency_contact_id: Emergency contact ID to update
            emergency_contact_data: Updated emergency contact data
            
        Returns:
            Updated emergency contact record if found, None otherwise
        """
        emergency_contact = self.get_emergency_contact_by_id(emergency_contact_id)
        if not emergency_contact:
            return None
            
        update_data = emergency_contact_data.model_dump()
        for field, value in update_data.items():
            setattr(emergency_contact, field, value)
            
        self.db.commit()
        self.db.refresh(emergency_contact)
        return emergency_contact

    def delete_emergency_contact(self, emergency_contact_id: int) -> bool:
        """Delete an emergency contact record.
        
        Args:
            emergency_contact_id: Emergency contact ID to delete
            
        Returns:
            True if deleted successfully, False if not found
        """
        emergency_contact = self.get_emergency_contact_by_id(emergency_contact_id)
        if not emergency_contact:
            return False
            
        self.db.delete(emergency_contact)
        self.db.commit()
        return True
