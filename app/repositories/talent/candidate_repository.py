"""Repository for talent profile management."""

from typing import Annotated, List, Optional

from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentProfile
from app.db.session import get_session


class TalentRepository:
    """Repository class for talent profile database operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the talent repository.
        
        Args:
            db: Database session
        """
        self.db = db

    def get_talent_by_id(self, talent_id: int) -> Optional[TalentProfile]:
        """Get a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            TalentProfile if found, None otherwise
        """
        statement = select(TalentProfile).where(TalentProfile.id == talent_id)
        return self.db.exec(statement).first()
    
    def get_all_talents(self) -> List[TalentProfile]:
        """Get all talent profiles.
        
        Returns:
            List of all talent profiles
        """
        statement = select(TalentProfile)
        return list(self.db.exec(statement).all())
    
    def create_talent_profile(self, talent_profile: TalentProfile) -> TalentProfile:
        """Create a new talent profile.
        
        Args:
            talent_profile: The talent profile data to create
            
        Returns:
            The created talent profile
        """
        self.db.add(talent_profile)
        self.db.commit()
        self.db.refresh(talent_profile)
        return talent_profile
    
    def update_talent_profile(self, talent_profile: TalentProfile) -> TalentProfile:
        """Update an existing talent profile.
        
        Args:
            talent_profile: The talent profile data to update
            
        Returns:
            The updated talent profile
        """
        self.db.add(talent_profile)
        self.db.commit()
        return talent_profile
    
    def delete_talent_profile(self, talent_id: int) -> bool:
        """Delete a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID to delete
            
        Returns:
            True if deleted successfully, False if not found
        """
        talent = self.get_talent_by_id(talent_id)
        if talent:
            self.db.delete(talent)
            self.db.commit()
            return True
        return False
