"""Talent software mapping repository for database operations."""

from typing import Annotated, Optional, List, Dict, Any
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentSoftwareMapping
from app.db.session import get_session
from app.core.logs import log_database_error


class TalentSoftwareMappingRepository:
    """Repository for talent software mapping-related database operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        self.db = db

    def get_by_id(self, mapping_id: int) -> Optional[TalentSoftwareMapping]:
        """Get software mapping by ID."""
        try:
            mapping = self.db.get(TalentSoftwareMapping, mapping_id)
            return mapping
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"mapping_id": mapping_id}
            )
            return None

    def get_by_talent_profile_id(self, talent_profile_id: int) -> List[TalentSoftwareMapping]:
        """Get all software mappings for a specific talent profile."""
        try:
            statement = select(TalentSoftwareMapping).where(
                TalentSoftwareMapping.talent_profile_id == talent_profile_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []

    def get_by_software(self, software: str) -> List[TalentSoftwareMapping]:
        """Get all mappings for a specific software."""
        try:
            statement = select(TalentSoftwareMapping).where(
                TalentSoftwareMapping.software == software
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"software": software}
            )
            return []

    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentSoftwareMapping]:
        """Get all software mappings with pagination."""
        try:
            statement = select(TalentSoftwareMapping).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []

    def create(self, mapping_data: Dict[str, Any]) -> TalentSoftwareMapping:
        """Create a new software mapping."""
        try:
            mapping = TalentSoftwareMapping(**mapping_data)
            self.db.add(mapping)
            self.db.commit()
            self.db.refresh(mapping)
            return mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="INSERT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context=mapping_data
            )
            raise e

    def update(self, mapping: TalentSoftwareMapping, update_data: Dict[str, Any]) -> TalentSoftwareMapping:
        """Update an existing software mapping."""
        try:
            for field, value in update_data.items():
                if hasattr(mapping, field) and value is not None:
                    setattr(mapping, field, value)
            
            self.db.add(mapping)
            self.db.commit()
            self.db.refresh(mapping)
            return mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"mapping_id": mapping.id, "update_data": update_data}
            )
            raise e

    def delete(self, mapping: TalentSoftwareMapping) -> None:
        """Delete a software mapping."""
        try:
            self.db.delete(mapping)
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"mapping_id": mapping.id}
            )
            raise e

    def search_by_software(self, software_pattern: str, skip: int = 0, limit: int = 100) -> List[TalentSoftwareMapping]:
        """Search software mappings by software name pattern."""
        try:
            all_mappings = self.get_all(skip=0, limit=1000)  # Get more records for filtering
            filtered_mappings = [
                mapping for mapping in all_mappings 
                if software_pattern.lower() in mapping.software.lower()
            ]
            # Apply pagination to filtered results
            return filtered_mappings[skip:skip + limit]
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"software_pattern": software_pattern, "skip": skip, "limit": limit}
            )
            return []

    def get_by_talent_and_software(self, talent_profile_id: int, software: str) -> Optional[TalentSoftwareMapping]:
        """Get software mapping by talent profile ID and software name."""
        try:
            statement = select(TalentSoftwareMapping).where(
                TalentSoftwareMapping.talent_profile_id == talent_profile_id,
                TalentSoftwareMapping.software == software
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id, "software": software}
            )
            return None

    def get_by_software_version(self, software: str, version: str) -> List[TalentSoftwareMapping]:
        """Get all mappings for a specific software and version."""
        try:
            statement = select(TalentSoftwareMapping).where(
                TalentSoftwareMapping.software == software,
                TalentSoftwareMapping.software_version == version
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"software": software, "version": version}
            )
            return []

    def count_total(self) -> int:
        """Get total count of software mappings."""
        try:
            statement = select(TalentSoftwareMapping)
            return len(list(self.db.exec(statement).all()))
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"operation": "count_total"}
            )
            return 0

    def count_by_talent_profile(self, talent_profile_id: int) -> int:
        """Get count of software mappings for a specific talent profile."""
        try:
            mappings = self.get_by_talent_profile_id(talent_profile_id)
            return len(mappings)
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="TalentSoftwareMapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id, "operation": "count_by_talent_profile"}
            )
            return 0