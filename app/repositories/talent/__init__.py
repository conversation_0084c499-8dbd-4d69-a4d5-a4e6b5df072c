"""Talent repositories package."""

from .candidate_repository import TalentRepository
from .chronic_conditions_repository import TalentChronicConditionsRepository
from .documents_repository import TalentDocumentsRepository
from .ongoing_health_repository import TalentOngoingHealthRepository
from .past_health_repository import TalentPastHealthRepository
from .banking_repository import BankingRepository
from .payroll_repository import PayrollRepository
from .emergency_contact_repository import EmergencyContactRepository
from .client_info_repository import TalentClientInfoRepository
from .skill_set_repository import TalentSkillSetRepository
from .position_repository import TalentPositionRepository
from .vacation_repository import VacationRepository
from .location_repository import LocationRepository

from .allergies_repository import TalentAllergiesRepository
from .activity_repository import TalentActivityRepository
from .third_party_integration_repository import TalentThirdPartyIntegrationRepository

__all__ = [
    "TalentRepository",
    "TalentChronicConditionsRepository", 
    "TalentDocumentsRepository",
    "TalentOngoingHealthRepository",
    "TalentPastHealthRepository",
    "BankingRepository",
    "PayrollRepository",
    "EmergencyContactRepository",
    "TalentClientInfoRepository",
    "TalentSkillSetRepository",
    "TalentPositionRepository",
    "VacationRepository",
    "LocationRepository",
    "TalentAllergiesRepository",
    "TalentActivityRepository",
    "TalentThirdPartyIntegrationRepository",
]
