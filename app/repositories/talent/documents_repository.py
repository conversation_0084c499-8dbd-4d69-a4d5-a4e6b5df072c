from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentDocumentsCollected
from app.db.session import get_session


class TalentDocumentsRepository:
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        self.db = db

    def get_documents_by_talent_id(self, talent_id: int) -> List[TalentDocumentsCollected]:
        """Get all documents for a specific talent."""
        statement = select(TalentDocumentsCollected).where(
            TalentDocumentsCollected.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def get_document_by_id(self, document_id: int) -> Optional[TalentDocumentsCollected]:
        """Get a specific document by ID."""
        statement = select(TalentDocumentsCollected).where(
            TalentDocumentsCollected.id == document_id
        )
        return self.db.exec(statement).first()

    def create_document(self, document: TalentDocumentsCollected) -> TalentDocumentsCollected:
        """Create a new talent document."""
        self.db.add(document)
        self.db.commit()
        self.db.refresh(document)
        return document

    def get_documents_by_type(self, talent_id: int, doc_type: str) -> List[TalentDocumentsCollected]:
        """Get documents by talent ID and document type."""
        statement = select(TalentDocumentsCollected).where(
            TalentDocumentsCollected.talent_profile_id == talent_id,
            TalentDocumentsCollected.doc_type == doc_type
        )
        return list(self.db.exec(statement).all())
