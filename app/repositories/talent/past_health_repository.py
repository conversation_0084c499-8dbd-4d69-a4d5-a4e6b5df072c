"""Repository for managing talent past health issues data."""

from typing import Annotated, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentPastHealthIssues
from app.db.session import get_session


class TalentPastHealthRepository:
    """Repository class for talent past health issues CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_or_update_past_health_issue(
        self, past_health_issue: TalentPastHealthIssues
    ) -> TalentPastHealthIssues:
        """Create a new past health issue record.
        
        Args:
            past_health_issue: The past health issue data to create
            
        Returns:
            The created past health issue record
        """
        self.db.add(past_health_issue)
        self.db.commit()
        self.db.refresh(past_health_issue)
        return past_health_issue

    def get_past_health_issues_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentPastHealthIssues]:
        """Get all past health issues for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of past health issue records
        """
        statement = select(TalentPastHealthIssues).where(
            TalentPastHealthIssues.talent_profile_id == talent_id
        )
        return self.db.exec(statement).first()
