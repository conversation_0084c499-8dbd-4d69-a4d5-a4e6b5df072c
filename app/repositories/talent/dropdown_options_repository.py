"""Skills repository for talent management.

This module contains the repository class for skills data access operations.
"""

from typing import List, Optional
from sqlmodel import Session, select
from fastapi import Depends

from app.db import get_session, TalentSkillSetMapping
from app.core.logs import log_database_error


class DropdownOptionsRepository:
    """Repository class for dropdown options data access operations.
    
    Handles all database operations related to dropdown options extraction and management.
    """
    
    def __init__(self, session: Session = Depends(get_session)):
        """Initialize the repository with a database session.
        
        Args:
            session: Database session dependency
        """
        self.session = session
    
    def get_skills_arrays(self) -> List[Optional[List[str]]]:
        """Get all non-null skills arrays from talent skill set mappings.
        
        This method performs a simple database query to retrieve all skills arrays
        without any business logic processing.
        
        Returns:
            List of skills arrays from the database (may contain None values)
            
        Raises:
            Exception: If database operation fails
        """
        try:
            # Query to get all non-null skills arrays
            statement = select(TalentSkillSetMapping.skills).where( # type: ignore
                TalentSkillSetMapping.skills.is_not(None)
            )
            
            result = self.session.exec(statement).all() # type: ignore
            return result # type: ignore
            
        except Exception as e:
            log_database_error(
                operation="get_skills_arrays",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"method": "get_skills_arrays"}
            )
            raise
