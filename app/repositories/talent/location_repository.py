"""Repository for talent location mapping data access."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.core import logger
from app.db import TalentLocationMapping
from app.db.session import get_session
from app.schemas.talent.location_schema import (
    TalentLocationMappingCreate,
    TalentLocationMappingUpdate,
)


class LocationRepository:
    """Repository class for talent location mapping operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_location_mapping(
        self, location_data: TalentLocationMappingCreate
    ) -> Optional[TalentLocationMapping]:
        """Create a new talent location mapping.
        
        Args:
            location_data: Location mapping data to create
            
        Returns:
            Created location mapping or None if creation failed
        """
        try:
            location_mapping = TalentLocationMapping(**location_data.model_dump())
            self.db.add(location_mapping)
            self.db.commit()
            self.db.refresh(location_mapping)
            logger.info(f"Created location mapping with ID: {location_mapping.id}")
            return location_mapping
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating location mapping: {str(e)}")
            return None

    def get_location_mapping_by_id(self, location_id: int) -> Optional[TalentLocationMapping]:
        """Get a location mapping by its ID.
        
        Args:
            location_id: ID of the location mapping
            
        Returns:
            Location mapping if found, None otherwise
        """
        try:
            statement = select(TalentLocationMapping).where(
                TalentLocationMapping.id == location_id
            )
            result = self.db.exec(statement).first()
            return result
        except Exception as e:
            logger.error(f"Error getting location mapping by ID {location_id}: {str(e)}")
            return None

    def get_location_mappings_by_talent_id(
        self, talent_profile_id: int
    ) -> List[TalentLocationMapping]:
        """Get all location mappings for a specific talent.
        
        Args:
            talent_profile_id: ID of the talent profile
            
        Returns:
            List of location mappings for the talent
        """
        try:
            statement = select(TalentLocationMapping).where(
                TalentLocationMapping.talent_profile_id == talent_profile_id
            )
            result = self.db.exec(statement).all()
            return list(result)
        except Exception as e:
            logger.error(
                f"Error getting location mappings for talent {talent_profile_id}: {str(e)}"
            )
            return []

    def update_location_mapping(
        self, location_id: int, location_data: TalentLocationMappingUpdate
    ) -> Optional[TalentLocationMapping]:
        """Update an existing location mapping.
        
        Args:
            location_id: ID of the location mapping to update
            location_data: Updated location mapping data
            
        Returns:
            Updated location mapping or None if update failed
        """
        try:
            location_mapping = self.get_location_mapping_by_id(location_id)
            if not location_mapping:
                logger.warning(f"Location mapping with ID {location_id} not found")
                return None

            update_data = location_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(location_mapping, field, value)

            self.db.add(location_mapping)
            self.db.commit()
            self.db.refresh(location_mapping)
            logger.info(f"Updated location mapping with ID: {location_id}")
            return location_mapping
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating location mapping {location_id}: {str(e)}")
            return None

    def delete_location_mapping(self, location_id: int) -> bool:
        """Delete a location mapping.
        
        Args:
            location_id: ID of the location mapping to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            location_mapping = self.get_location_mapping_by_id(location_id)
            if not location_mapping:
                logger.warning(f"Location mapping with ID {location_id} not found")
                return False

            self.db.delete(location_mapping)
            self.db.commit()
            logger.info(f"Deleted location mapping with ID: {location_id}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting location mapping {location_id}: {str(e)}")
            return False

    def location_mapping_exists(self, location_id: int) -> bool:
        """Check if a location mapping exists.
        
        Args:
            location_id: ID of the location mapping
            
        Returns:
            True if location mapping exists, False otherwise
        """
        try:
            statement = select(TalentLocationMapping).where(
                TalentLocationMapping.id == location_id
            )
            result = self.db.exec(statement).first()
            return result is not None
        except Exception as e:
            logger.error(f"Error checking location mapping existence {location_id}: {str(e)}")
            return False

    def get_location_mapping_by_talent_and_location(
        self, talent_profile_id: int, location: str
    ) -> Optional[TalentLocationMapping]:
        """Get location mapping by talent ID and location.
        
        Args:
            talent_profile_id: ID of the talent profile
            location: Location information
            
        Returns:
            Location mapping if found, None otherwise
        """
        try:
            statement = select(TalentLocationMapping).where(
                TalentLocationMapping.talent_profile_id == talent_profile_id,
                TalentLocationMapping.location == location
            )
            result = self.db.exec(statement).first()
            return result
        except Exception as e:
            logger.error(
                f"Error getting location mapping for talent {talent_profile_id} and location {location}: {str(e)}"
            )
            return None