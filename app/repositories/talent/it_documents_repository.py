"""Repository for talent IT documents information operations."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentITDocumentsMapping
from app.schemas.talent import TalentITDocumentsUpdate
from app.db.session import get_session


class ITDocumentsRepository:
    """Repository for talent IT documents information CRUD operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session
        """
        self.db = db

    def create_it_document(
        self, talent_profile_id: int, document_type: str, url: str, notes: Optional[str] = None
    ) -> TalentITDocumentsMapping:
        """Create a new IT document record.
        
        Args:
            it_document_data: IT document data to create
            
        Returns:
            Created IT document record
        """
        it_document = TalentITDocumentsMapping(
            talent_profile_id=talent_profile_id,
            document_type=document_type,
            url=url,
            notes=notes,
        )
        self.db.add(it_document)
        self.db.commit()
        self.db.refresh(it_document)
        return it_document

    def get_it_document_by_id(self, it_document_id: int) -> Optional[TalentITDocumentsMapping]:
        """Get IT document by ID.
        
        Args:
            it_document_id: IT document ID
            
        Returns:
            IT document record if found, None otherwise
        """
        statement = select(TalentITDocumentsMapping).where(
            TalentITDocumentsMapping.id == it_document_id
        )
        return self.db.exec(statement).first()

    def get_it_documents_by_talent_id(self, talent_id: int) -> List[TalentITDocumentsMapping]:
        """Get all IT documents for a specific talent.
        
        Args:
            talent_id: Talent profile ID
            
        Returns:
            List of IT document records
        """
        statement = select(TalentITDocumentsMapping).where(
            TalentITDocumentsMapping.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def update_it_document(
        self, 
        it_document_id: int, 
        it_document_data: TalentITDocumentsUpdate
    ) -> Optional[TalentITDocumentsMapping]:
        """Update an existing IT document record.
        
        Args:
            it_document_id: IT document ID to update
            it_document_data: Updated IT document data
            
        Returns:
            Updated IT document record if found, None otherwise
        """
        statement = select(TalentITDocumentsMapping).where(
            TalentITDocumentsMapping.id == it_document_id
        )
        it_document = self.db.exec(statement).first()
        
        if it_document:
            update_data = it_document_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(it_document, field, value)
            
            self.db.add(it_document)
            self.db.commit()
            self.db.refresh(it_document)
            
        return it_document

    def delete_it_document(self, it_document_id: int) -> bool:
        """Delete an IT document record.
        
        Args:
            it_document_id: IT document ID to delete
            
        Returns:
            True if deleted successfully, False if not found
        """
        statement = select(TalentITDocumentsMapping).where(
            TalentITDocumentsMapping.id == it_document_id
        )
        it_document = self.db.exec(statement).first()
        
        if it_document:
            self.db.delete(it_document)
            self.db.commit()
            return True
            
        return False
