"""Talent Third Party Integration Repository."""

from typing import Optional, Annotated, Sequence
from sqlmodel import Session, select
from fastapi import Depends

from app.db.session import get_session
from app.db.models import TalentThirdPartyIntegration
from app.schemas.talent.third_party_integration_schema import (
    TalentThirdPartyIntegrationCreate,
    TalentThirdPartyIntegrationUpdate
)
from app.core.logs import log_database_error


class TalentThirdPartyIntegrationRepository:
    """Repository for talent third party integration operations."""
    
    def __init__(self, db_session: Annotated[Session, Depends(get_session)]):
        """Initialize repository with database session."""
        self.db_session = db_session
    
    def get_by_id(self, integration_id: int) -> Optional[TalentThirdPartyIntegration]:
        """Get third party integration by ID."""
        try:
            statement = select(TalentThirdPartyIntegration).where(
                TalentThirdPartyIntegration.id == integration_id
            )
            result = self.db_session.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="get_by_id",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def get_by_talent_profile_id(self, talent_profile_id: int) -> Sequence[TalentThirdPartyIntegration]:
        """Get all third party integrations for a talent profile."""
        try:
            statement = select(TalentThirdPartyIntegration).where(
                TalentThirdPartyIntegration.talent_profile_id == talent_profile_id
            )
            result = self.db_session.exec(statement).all()
            return result
        except Exception as e:
            log_database_error(
                operation="get_by_talent_profile_id",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def create(self, integration_data: TalentThirdPartyIntegrationCreate) -> TalentThirdPartyIntegration:
        """Create a new third party integration."""
        try:
            db_integration = TalentThirdPartyIntegration(
                **integration_data.model_dump()
            )
            self.db_session.add(db_integration)
            self.db_session.commit()
            self.db_session.refresh(db_integration)
            return db_integration
        except Exception as e:
            self.db_session.rollback()
            log_database_error(
                operation="create",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def update(self, integration_id: int, integration_data: TalentThirdPartyIntegrationUpdate) -> Optional[TalentThirdPartyIntegration]:
        """Update an existing third party integration."""
        try:
            db_integration = self.get_by_id(integration_id)
            if db_integration:
                update_data = integration_data.model_dump(exclude_unset=True)
                for field, value in update_data.items():
                    setattr(db_integration, field, value)
                
                self.db_session.add(db_integration)
                self.db_session.commit()
                self.db_session.refresh(db_integration)
            return db_integration
        except Exception as e:
            self.db_session.rollback()
            log_database_error(
                operation="update",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def delete(self, integration_id: int) -> bool:
        """Delete a third party integration."""
        try:
            db_integration = self.get_by_id(integration_id)
            if db_integration:
                self.db_session.delete(db_integration)
                self.db_session.commit()
                return True
            return False
        except Exception as e:
            self.db_session.rollback()
            log_database_error(
                operation="delete",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def count_total(self) -> int:
        """Count total third party integrations."""
        try:
            statement = select(TalentThirdPartyIntegration)
            result = self.db_session.exec(statement)
            return len(result.all())
        except Exception as e:
            log_database_error(
                operation="count_total",
                table="talent_third_party_integration",
                error=e
            )
            raise
    
    def count_by_talent_profile(self, talent_profile_id: int) -> int:
        """Count third party integrations by talent profile."""
        try:
            statement = select(TalentThirdPartyIntegration).where(
                TalentThirdPartyIntegration.talent_profile_id == talent_profile_id
            )
            result = self.db_session.exec(statement)
            return len(result.all())
        except Exception as e:
            log_database_error(
                operation="count_by_talent_profile",
                table="talent_third_party_integration",
                error=e
            )
            raise
