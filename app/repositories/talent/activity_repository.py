"""Activity repository for talent management.

This module contains the repository class for talent activity data access operations.
"""

from typing import List, Optional
from sqlmodel import Session, select
from fastapi import Depends

from app.db import get_session
from app.db.models import TalentActivity
from app.schemas.talent.activity_schema import TalentActivity<PERSON>reate, TalentActivityUpdate
from app.core.logs import log_database_error


class TalentActivityRepository:
    """Repository class for talent activity data access operations.
    
    Handles all database operations related to talent activities.
    """
    
    def __init__(self, session: Session = Depends(get_session)):
        """Initialize the repository with a database session.
        
        Args:
            session: Database session dependency
        """
        self.session = session
    
    def create(self, activity_data: TalentActivityCreate) -> Optional[TalentActivity]:
        """Create a new talent activity record.
        
        Args:
            activity_data: Activity data to create
            
        Returns:
            Created activity record or None if creation fails
        """
        try:
            db_activity = TalentActivity(**activity_data.model_dump())
            self.session.add(db_activity)
            self.session.commit()
            self.session.refresh(db_activity)
            return db_activity
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="create_activity",
                table="talent_activity",
                error=e,
                additional_context=activity_data.model_dump()
            )
            return None
    
    def get_by_id(self, activity_id: int) -> Optional[TalentActivity]:
        """Get a talent activity by ID.
        
        Args:
            activity_id: Activity ID
            
        Returns:
            Activity record or None if not found
        """
        try:
            statement = select(TalentActivity).where(TalentActivity.id == activity_id)
            return self.session.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="get_activity_by_id",
                table="talent_activity",
                error=e,
                additional_context={"activity_id": activity_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int, skip: int = 0, limit: int = 100) -> List[TalentActivity]:
        """Get all activities for a specific talent with pagination.
        
        Args:
            talent_profile_id: Talent profile ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of activity records for the talent
        """
        try:
            statement = (
                select(TalentActivity)
                .where(TalentActivity.talent_profile_id == talent_profile_id)
                .order_by(TalentActivity.id.desc()) # type: ignore
                .offset(skip)
                .limit(limit)
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_activities_by_talent_id",
                table="talent_activity",
                error=e,
                additional_context={
                    "talent_profile_id": talent_profile_id,
                    "skip": skip,
                    "limit": limit
                }
            )
            return []
    
    def get_by_user_id(self, user_id: int, skip: int = 0, limit: int = 100) -> List[TalentActivity]:
        """Get all activities performed by a specific user with pagination.
        
        Args:
            user_id: User ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of activity records performed by the user
        """
        try:
            statement = (
                select(TalentActivity)
                .where(TalentActivity.user_id == user_id)
                .offset(skip)
                .limit(limit)
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_activities_by_user_id",
                table="talent_activity",
                error=e,
                additional_context={
                    "user_id": user_id,
                    "skip": skip,
                    "limit": limit
                }
            )
            return []
    
    def get_by_activity_type(self, activity_type: str, skip: int = 0, limit: int = 100) -> List[TalentActivity]:
        """Get all activities of a specific type with pagination.
        
        Args:
            activity_type: Type of activity to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of activity records of the specified type
        """
        try:
            statement = (
                select(TalentActivity)
                .where(TalentActivity.activity_type == activity_type)
                .offset(skip)
                .limit(limit)
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_activities_by_type",
                table="talent_activity",
                error=e,
                additional_context={
                    "activity_type": activity_type,
                    "skip": skip,
                    "limit": limit
                }
            )
            return []
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentActivity]:
        """Get all talent activities with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of all activity records
        """
        try:
            statement = (
                select(TalentActivity)
                .offset(skip)
                .limit(limit)
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_all_activities",
                table="talent_activity",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, activity_id: int, activity_data: TalentActivityUpdate) -> Optional[TalentActivity]:
        """Update an existing talent activity record.
        
        Args:
            activity_id: Activity ID to update
            activity_data: Updated activity data
            
        Returns:
            Updated activity record or None if update fails
        """
        try:
            db_activity = self.get_by_id(activity_id)
            if not db_activity:
                return None
            
            update_data = activity_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_activity, field, value)
            
            self.session.commit()
            self.session.refresh(db_activity)
            return db_activity
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="update_activity",
                table="talent_activity",
                error=e,
                additional_context={
                    "activity_id": activity_id,
                    "update_data": activity_data.model_dump(exclude_unset=True)
                }
            )
            return None
    
    def delete(self, activity_id: int) -> bool:
        """Delete a talent activity record.
        
        Args:
            activity_id: Activity ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_activity = self.get_by_id(activity_id)
            if not db_activity:
                return False
            
            self.session.delete(db_activity)
            self.session.commit()
            return True
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="delete_activity",
                table="talent_activity",
                error=e,
                additional_context={"activity_id": activity_id}
            )
            return False
    
    def count_by_talent_id(self, talent_profile_id: int) -> int:
        """Count total activities for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Total count of activities for the talent
        """
        try:
            statement = select(TalentActivity).where(
                TalentActivity.talent_profile_id == talent_profile_id
            )
            return len(list(self.session.exec(statement).all()))
        except Exception as e:
            log_database_error(
                operation="count_activities_by_talent_id",
                table="talent_activity",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return 0
