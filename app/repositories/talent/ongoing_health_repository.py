"""Repository for managing talent ongoing health issues data."""

from typing import Annotated, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentOngoingHealthIssues
from app.db.session import get_session


class TalentOngoingHealthRepository:
    """Repository class for talent ongoing health issues CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_or_update_ongoing_health_issue(
        self, ongoing_health_issue: TalentOngoingHealthIssues
    ) -> TalentOngoingHealthIssues:
        """Create a new ongoing health issue record.
        
        Args:
            ongoing_health_issue: The ongoing health issue data to create
            
        Returns:
            The created ongoing health issue record
        """
        self.db.add(ongoing_health_issue)
        self.db.commit()
        self.db.refresh(ongoing_health_issue)
        return ongoing_health_issue

    def get_ongoing_health_issues_by_talent_id(
        self, talent_id: int
    ) -> Optional[TalentOngoingHealthIssues]:
        """Get all ongoing health issues for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of ongoing health issue records
        """
        statement = select(TalentOngoingHealthIssues).where(
            TalentOngoingHealthIssues.talent_profile_id == talent_id
        )
        return self.db.exec(statement).first()
