"""Payroll information repository for talent management.

This module contains the repository class for payroll information CRUD operations.
"""

from typing import Annotated, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentPayrollInformationMapping
from app.db.session import get_session
from app.schemas.talent.payroll_schema import (
    TalentPayrollInformationCreate
)
from app.core.logs import log_database_error


class PayrollRepository:
    """Repository class for talent payroll information operations.
    
    Handles all database operations related to talent payroll information.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, payroll_data: TalentPayrollInformationCreate) -> Optional[TalentPayrollInformationMapping]:
        """Create a new payroll information record.
        
        Args:
            payroll_data: Payroll information data to create
            
        Returns:
            Created payroll information record or None if creation fails
        """
        try:
            db_payroll = TalentPayrollInformationMapping(**payroll_data.model_dump())
            self.db.add(db_payroll)
            self.db.commit()
            self.db.refresh(db_payroll)
            return db_payroll
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_payroll_information",
                table="TalentPayrollInformationMapping",
                additional_context=payroll_data.model_dump()
            )
            return None
    
    def get_by_id(self, payroll_id: int) -> Optional[TalentPayrollInformationMapping]:
        """Get payroll information by ID.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            Payroll information record or None if not found
        """
        try:
            statement = select(TalentPayrollInformationMapping).where(
                TalentPayrollInformationMapping.id == payroll_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_payroll_information_by_id",
                table="TalentPayrollInformationMapping",
                additional_context={"payroll_id": payroll_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> Optional[TalentPayrollInformationMapping]:
        """Get payroll information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Payroll information record or None if not found
        """
        try:
            statement = select(TalentPayrollInformationMapping).where(
                TalentPayrollInformationMapping.talent_profile_id == talent_profile_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_payroll_information_by_talent_id",
                table="TalentPayrollInformationMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return None
    
    def update(self, payroll_id: int, payroll_data: TalentPayrollInformationMapping) -> Optional[TalentPayrollInformationMapping]:
        """Update payroll information.
        
        Args:
            payroll_id: Payroll information ID
            payroll_data: Updated payroll information data
            
        Returns:
            Updated payroll information record or None if update fails
        """
        try:
            db_payroll = self.get_by_id(payroll_id)
            if not db_payroll:
                return None
            
            update_data = payroll_data.model_dump()
            for field, value in update_data.items():
                setattr(db_payroll, field, value)
            
            self.db.add(db_payroll)
            self.db.commit()
            self.db.refresh(db_payroll)
            return db_payroll
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="update_payroll_information",
                table="TalentPayrollInformationMapping",
                additional_context={
                    "payroll_id": payroll_id,
                    "update_data": payroll_data.model_dump()
                }
            )
            return None
    
    def delete(self, payroll_id: int) -> bool:
        """Delete payroll information.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_payroll = self.get_by_id(payroll_id)
            if not db_payroll:
                return False
            
            self.db.delete(db_payroll)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="delete_payroll_information",
                table="TalentPayrollInformationMapping",
                additional_context={"payroll_id": payroll_id}
            )
            return False
    
    def exists_by_talent_id(self, talent_profile_id: int) -> bool:
        """Check if payroll information exists for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if payroll information exists, False otherwise
        """
        try:
            statement = select(TalentPayrollInformationMapping).where(
                TalentPayrollInformationMapping.talent_profile_id == talent_profile_id
            )
            return self.db.exec(statement).first() is not None
        except Exception as e:
            log_database_error(
                error=e,
                operation="check_payroll_information_exists",
                table="TalentPayrollInformationMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            raise e
