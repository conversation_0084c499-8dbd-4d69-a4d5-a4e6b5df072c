"""Vacation mapping repository for talent management.

This module contains the repository class for vacation mapping CRUD operations.
"""

from typing import Annotated, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentVacationMapping
from app.db.session import get_session
from app.schemas.talent.vacation_schema import (
    TalentVacationMappingCreate
)
from app.core.logs import log_database_error


class VacationRepository:
    """Repository class for talent vacation mapping operations.
    
    Handles all database operations related to talent vacation mapping.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, vacation_data: TalentVacationMappingCreate | TalentVacationMapping) -> Optional[TalentVacationMapping]:
        """Create a new vacation mapping record.
        
        Args:
            vacation_data: Vacation mapping data to create
            
        Returns:
            Created vacation mapping record or None if creation fails
        """
        try:
            if isinstance(vacation_data, TalentVacationMappingCreate):
                db_vacation = TalentVacationMapping(**vacation_data.model_dump())
            else:
                db_vacation = vacation_data
            self.db.add(db_vacation)
            self.db.commit()
            self.db.refresh(db_vacation)
            return db_vacation
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_vacation_mapping",
                table="TalentVacationMapping",
                additional_context=vacation_data.model_dump()
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> Optional[TalentVacationMapping]:
        """Get vacation mapping by ID.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            Vacation mapping record or None if not found
        """
        try:
            statement = select(TalentVacationMapping).where(
                TalentVacationMapping.talent_profile_id == talent_profile_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_vacation_mapping_by_id",
                table="TalentVacationMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return None
    