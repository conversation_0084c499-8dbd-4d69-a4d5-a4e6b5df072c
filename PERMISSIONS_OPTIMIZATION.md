# Permissions API Optimization

## Problem
The `profile/get-user-permissions` API was being called multiple times on page refresh, causing unnecessary network requests and potential performance issues.

## Root Cause
The permissions were being fetched in multiple places without any caching mechanism:
1. **App.vue** - `initializePermissions()` on mounted
2. **Router Guard** - `fetchUserPermissions()` if permissions not loaded
3. **Login Flow** - `fetchUserPermissions()` after successful login

## Solution Implemented

### 1. Added Caching to Permissions Store
- Added `isInitialized` flag to track if permissions have been loaded
- Added `fetchPromise` to prevent multiple simultaneous API calls
- Modified `fetchUserPermissions()` to return cached data if already loaded
- Added `refreshUserPermissions()` method to force refresh when needed

### 2. Updated Store State Management
```typescript
// New state variables
const isInitialized = ref(false)
const fetchPromise = ref<Promise<any> | null>(null)

// Enhanced fetchUserPermissions with caching
const fetchUserPermissions = async (forceRefresh: boolean = false) => {
  // Return cached data if available and not forcing refresh
  if (!forceRefresh && isInitialized.value && userPermissions.value) {
    return userPermissions.value
  }

  // Prevent multiple simultaneous calls
  if (!forceRefresh && fetchPromise.value) {
    return fetchPromise.value
  }

  // Make API call and cache result
  // ...
}
```

### 3. Updated Router Guard
Changed from checking `userPermissions` to checking `isInitialized` flag:
```typescript
// Before
if (!permissionsStore.userPermissions) {
  await permissionsStore.fetchUserPermissions()
}

// After  
if (!permissionsStore.isInitialized) {
  await permissionsStore.fetchUserPermissions()
}
```

### 4. Enhanced Logout Functionality
Added permissions cleanup on logout to ensure fresh permissions on next login:
```typescript
const handleLogout = () => {
  // ... existing logout code
  clearPermissions() // Clear permissions cache on logout
  // ... rest of logout code
}
```

### 5. Added Composable Methods
- `refreshPermissions()` - Force reload permissions from API
- Enhanced `clearPermissions()` - Clear all cached data

## Benefits

1. **Reduced API Calls**: Permissions are only fetched once per session
2. **Improved Performance**: Subsequent permission checks use cached data
3. **Better UX**: Faster page loads and navigation
4. **Concurrent Safety**: Multiple simultaneous calls don't cause duplicate requests
5. **Flexible Refresh**: Can force refresh when needed (e.g., role changes)

## Testing

### Manual Testing
1. Open browser dev tools (Network tab)
2. Login to the application
3. Navigate between pages and refresh
4. Verify only one `profile/get-user-permissions` call is made per session

### Automated Testing
Use the test utility:
```typescript
// In browser console
await testPermissionsCaching()
```

This will test:
- First API call timing
- Cached call timing (should be much faster)
- Simultaneous calls handling
- Force refresh functionality

## Files Modified

1. `src/store/permissions/usePermissionsStore.ts` - Added caching logic
2. `src/composables/permissions/usePermissions.ts` - Added refresh method
3. `src/router/index.ts` - Updated guard to use isInitialized flag
4. `src/App.vue` - Added permissions cleanup on logout
5. `src/utils/permissions-test.ts` - Added test utility (new file)

## Migration Notes

- Existing code using `fetchUserPermissions()` will continue to work
- New `refreshUserPermissions()` method available for force refresh scenarios
- `isInitialized` flag can be used to check if permissions are loaded
- No breaking changes to existing permission checking logic
