# 🚨 MANDATORY READING - BPO Admin Backend

## ⚠️ CRITICAL NOTICE

**STOP! READ THIS FIRST!**

Before starting ANY development work, code changes, or new features, you MUST read the following documents in order:

## 📚 Required Reading Order

### 1. Project Foundation (READ FIRST)
- **[Memory Bank README](./memory-bank/README.md)** - Overview of project documentation structure
- **[Project Brief](./memory-bank/projectbrief.md)** - Core project understanding and requirements
- **[Product Context](./memory-bank/productContext.md)** - Business context and user needs

### 2. Current State (READ SECOND)
- **[Active Context](./memory-bank/activeContext.md)** - Current development status and priorities
- **[Progress](./memory-bank/progress.md)** - Implementation status and completed features
- **[Tech Context](./memory-bank/techContext.md)** - Technology decisions and architecture

### 3. Development Guidelines (READ THIRD)
- **[System Patterns](./memory-bank/systemPatterns.md)** - Coding patterns and architectural decisions
- **[Development Rules](./DEVELOPMENT_RULES.md)** - Mandatory coding standards and practices
- **[Project Documentation](./PROJECT_DOCUMENTATION.md)** - Quick start and architecture overview

## 🎯 Why This Matters

### Understanding Context
- **Business Logic**: Know WHY features exist, not just HOW they work
- **User Needs**: Understand who uses the system and their pain points
- **Technical Decisions**: Learn from past architectural choices

### Avoiding Mistakes
- **Consistency**: Follow established patterns instead of creating new ones
- **Quality**: Maintain code standards that have been proven to work
- **Efficiency**: Don't reinvent solutions that already exist

### Faster Development
- **Patterns**: Reuse established patterns for faster implementation
- **Context**: Understand existing code structure before making changes
- **Dependencies**: Know what libraries and tools are already in use

## ✅ Pre-Development Checklist

Before writing ANY code, confirm you have:

- [ ] Read all Memory Bank documents
- [ ] Understood the current project state
- [ ] Reviewed existing patterns for similar functionality
- [ ] Checked the progress document for implementation status
- [ ] Understood the technical architecture and constraints
- [ ] Reviewed development rules and coding standards

## 🚫 What Happens If You Skip This

### Code Quality Issues
- Inconsistent patterns across the codebase
- Violation of established architectural principles
- Poor error handling and logging practices
- Missing type hints and documentation

### Development Problems
- Duplicate functionality that already exists
- Breaking changes to existing features
- Incompatible technology choices
- Security vulnerabilities

### Project Impact
- Delayed development due to rework
- Increased technical debt
- Harder maintenance and debugging
- Poor user experience

## 🎯 Quick Reference

### Current Tech Stack
- **Backend**: FastAPI + SQLModel + MySQL
- **Authentication**: JWT tokens
- **Database**: MySQL with Alembic migrations
- **Package Management**: uv
- **Type Checking**: basedpyright
- **Testing**: pytest

### Key Architectural Patterns
- **Layer Separation**: API → Service → Repository → Database
- **Response Format**: Always use GeneralResponse
- **Error Handling**: Comprehensive try/catch with logging
- **Type Safety**: Full type hints on all functions
- **Authentication**: JWT-based with role checking

### Current Implementation Status
- ✅ **Completed**: Authentication, Talent Management, Banking, Wages
- 🚧 **In Progress**: Advanced reporting, audit trails
- 📋 **Planned**: Performance optimization, advanced analytics

## 📞 Need Help?

If you have questions after reading the documentation:

1. **Check existing code** for similar patterns
2. **Review test files** for usage examples
3. **Look at recent commits** for implementation patterns
4. **Ask specific questions** rather than general ones

## 🔄 Keeping Documentation Updated

When you make changes:

1. **Update relevant memory bank files** if you change architecture
2. **Update progress.md** when completing features
3. **Update system patterns** if you establish new patterns
4. **Update tech context** if you add new dependencies

---

**Remember**: 15 minutes of reading can save hours of rework!

**Last Updated**: January 2025
**Enforcement**: Mandatory for all development work