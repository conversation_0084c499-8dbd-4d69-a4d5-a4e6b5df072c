"""Initial migration with all talent management models

Revision ID: 187da11e3471
Revises: 
Create Date: 2025-07-28 20:12:24.087277

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '187da11e3471'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('talent_profiles', sa.Column('full_name', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False))
    op.alter_column('talent_profiles', 'hire_date',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.drop_column('talent_profiles', 'name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('talent_profiles', sa.Column('name', mysql.VARCHAR(length=20), nullable=False))
    op.alter_column('talent_profiles', 'hire_date',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.drop_column('talent_profiles', 'full_name')
    # ### end Alembic commands ###
