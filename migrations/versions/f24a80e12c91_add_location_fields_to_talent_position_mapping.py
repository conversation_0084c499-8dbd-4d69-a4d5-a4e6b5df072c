"""Add location fields to talent position mapping

Revision ID: f24a80e12c91
Revises: 9332d5237670
Create Date: 2025-01-25 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f24a80e12c91'
down_revision: Union[str, Sequence[str], None] = '9332d5237670'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add location fields to talent_position_mapping table
    op.add_column('talent_position_mapping', sa.Column('location', sa.VARCHAR(length=20), nullable=True))
    op.add_column('talent_position_mapping', sa.Column('is_location_permanent', sa.<PERSON>(), nullable=True))
    op.add_column('talent_position_mapping', sa.Column('location_duration', sa.VARCHAR(length=20), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove location fields from talent_position_mapping table
    op.drop_column('talent_position_mapping', 'location_duration')
    op.drop_column('talent_position_mapping', 'is_location_permanent')
    op.drop_column('talent_position_mapping', 'location')