"""Update activity old_data and new_data to TEXT fields

Revision ID: 9332d5237670
Revises: 187da11e3471
Create Date: 2025-08-21 10:20:33.496016

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9332d5237670'
down_revision: Union[str, Sequence[str], None] = '187da11e3471'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Update talent_activity table fields to TEXT
    op.alter_column('talent_activity', 'old_data',
               existing_type=mysql.VARCHAR(length=200),
               type_=sa.Text(),
               existing_nullable=True)
    op.alter_column('talent_activity', 'new_data',
               existing_type=mysql.VARCHAR(length=200),
               type_=sa.Text(),
               existing_nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    # Revert talent_activity table fields back to VARCHAR(200)
    op.alter_column('talent_activity', 'old_data',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=200),
               existing_nullable=True)
    op.alter_column('talent_activity', 'new_data',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(length=200),
               existing_nullable=True)