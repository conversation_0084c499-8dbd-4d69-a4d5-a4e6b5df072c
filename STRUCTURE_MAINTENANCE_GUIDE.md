# Structure Maintenance Guide - BPO Admin Backend

## 🎯 Purpose

This guide ensures that all future development maintains the established architectural patterns, coding standards, and implementation consistency across the BPO Admin backend codebase.

## 📋 Mandatory Pre-Development Checklist

### Before Starting ANY Development Task:

1. ✅ **Read Documentation** (in this exact order):
   - [MANDATORY_READING.md](./MANDATORY_READING.md)
   - [Memory Bank README](./memory-bank/README.md)
   - [Project Brief](./memory-bank/projectbrief.md)
   - [Active Context](./memory-bank/activeContext.md)
   - [System Patterns](./memory-bank/systemPatterns.md)
   - [Progress](./memory-bank/progress.md)
   - [Development Rules](./DEVELOPMENT_RULES.md)

2. ✅ **Understand Current Implementation**:
   - Review existing similar implementations in the codebase
   - Check established patterns in `app/api/v1/talent/position_router.py`
   - Review service patterns in `app/services/talent/position_service.py`
   - Understand dependency injection patterns

3. ✅ **Verify Compliance**:
   - Ensure your changes follow the established layer separation
   - Confirm error handling patterns match existing implementations
   - Validate that response formats are consistent

## 🏗️ Established Architecture Patterns

### Layer Structure (MUST BE MAINTAINED)

```
┌─────────────────────────────────────────┐
│              API Layer                  │
│    (FastAPI Routes + Request/Response)  │
├─────────────────────────────────────────┤
│            Service Layer                │
│     (Business Logic + Validation)       │
├─────────────────────────────────────────┤
│          Repository Layer               │
│        (Data Access + Queries)          │
├─────────────────────────────────────────┤
│            Model Layer                  │
│      (SQLModel + Database Schema)       │
└─────────────────────────────────────────┘
```

### API Router Pattern (MANDATORY TEMPLATE)

```python
"""[Module] router for talent management API.

This module contains the FastAPI router for [entity] endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    [Entity]Create,
    [Entity]Update,
)
from app.services.talent.[service_name] import [ServiceClass]


router = APIRouter(prefix="/[endpoint]")

service_dependency = Annotated[[ServiceClass], Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_[entity](
    request: Request,
    [entity]_data: [Entity]Create,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new [entity] record.
    
    Args:
        request: FastAPI request object
        [entity]_data: The [entity] data to create
        service: Injected [entity] service
        
    Returns:
        GeneralResponse with created [entity] data
    """
    try:
        [entity] = service.create_[entity]([entity]_data)
        
        if not [entity]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create [entity]"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="[Entity] created successfully",
            data=[entity].model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
```

### Service Layer Pattern (MANDATORY TEMPLATE)

```python
"""[Entity] service for talent management.

This module contains the service class for [entity] business logic operations."""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.[repository_name] import [RepositoryClass]
from app.schemas.talent.[schema_name] import (
    [Entity]Create,
    [Entity]Update,
    [Entity]Response
)
from app.core.logs import log_error_with_context


class [ServiceClass]:
    """Service class for [entity] business logic.
    
    Handles all business operations related to [entity].
    """
    
    def __init__(
        self, 
        repository: Annotated[[RepositoryClass], Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_[entity](self, [entity]_data: [Entity]Create) -> Optional[[Entity]Response]:
        """Create a new [entity].
        
        Args:
            [entity]_data: [Entity] data to create
            
        Returns:
            Created [entity] response or None if creation fails
        """
        try:
            # Validate business rules if needed
            if not self._validate_[entity]_data([entity]_data):
                return None
            
            db_[entity] = self.repository.create([entity]_data)
            if db_[entity]:
                return [Entity]Response.model_validate(db_[entity])
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_[entity]",
                    "service": "[ServiceClass]",
                    "data": [entity]_data.model_dump()
                }
            )
            return None
```

## 🔧 Implementation Standards

### 1. Import Organization (MANDATORY)

```python
# 1. Standard library imports
from typing import Annotated, List, Optional

# 2. Third-party imports
from fastapi import APIRouter, Depends, HTTPException, Request, status

# 3. Local application imports (grouped by layer)
from app.core import log_api_error
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.repository_name import RepositoryClass
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import CreateSchema, UpdateSchema
from app.services.talent.service_name import ServiceClass
```

### 2. Dependency Injection (MANDATORY)

```python
# API Layer
service_dependency = Annotated[ServiceClass, Depends()]

# Service Layer
def __init__(
    self, 
    repository: Annotated[RepositoryClass, Depends()],
    current_user: Annotated[User, Depends(get_current_user)]
):
```

### 3. Error Handling (MANDATORY)

#### API Layer Error Handling
```python
try:
    result = service.method(data)
    return GeneralResponse(
        status_code=status.HTTP_200_OK,
        message="Operation successful",
        data=result.model_dump() if result else None
    )
except HTTPException:
    raise  # Re-raise HTTP exceptions
except Exception as e:
    log_api_error(
        endpoint=request.url.path,
        method=request.method,
        status_code=500,
        error=str(e),
        user_id=str(service.current_user.id),
    )
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Internal server error",
    )
```

#### Service Layer Error Handling
```python
try:
    # Business logic
    result = self.repository.operation(data)
    return ResponseSchema.model_validate(result)
except Exception as e:
    log_error_with_context(
        error=e,
        context={
            "operation": "operation_name",
            "service": "ServiceClass",
            "data": data.model_dump() if hasattr(data, 'model_dump') else str(data)
        }
    )
    return None
```

### 4. Response Patterns (MANDATORY)

```python
# Success Response
return GeneralResponse(
    status_code=status.HTTP_200_OK,
    message="Operation completed successfully",
    data=result.model_dump() if result else None
)

# Error Response (after logging)
raise HTTPException(
    status_code=status.HTTP_400_BAD_REQUEST,
    detail="Specific error message"
)
```

### 5. Documentation Standards (MANDATORY)

```python
def method_name(
    self,
    param1: Type1,
    param2: Type2,
) -> ReturnType:
    """Brief description of what the method does.
    
    Args:
        param1: Description of param1
        param2: Description of param2
        
    Returns:
        Description of return value
        
    Raises:
        HTTPException: When specific condition occurs
    """
```

## 🚨 Critical Maintenance Rules

### DO NOT BREAK THESE PATTERNS:

1. **Layer Separation**: Never access database directly from API layer
2. **Dependency Injection**: Always use Annotated dependencies
3. **Error Handling**: Always include try-catch with proper logging
4. **Response Format**: Always use GeneralResponse for API endpoints
5. **Authentication**: Always inject current_user in service layer
6. **Type Hints**: Always include complete type annotations
7. **Import Organization**: Always follow the three-section import pattern
8. **Documentation**: Always include comprehensive docstrings

### WHEN ADDING NEW FEATURES:

1. **Copy Existing Patterns**: Use `position_router.py` and `position_service.py` as templates
2. **Follow Naming Conventions**: Use consistent naming across similar components
3. **Maintain Error Handling**: Include both API and service layer error handling
4. **Update Documentation**: Update progress.md and activeContext.md when completing features
5. **Test Patterns**: Ensure new code follows established patterns before committing

## 📊 Quality Assurance Checklist

### Before Committing Code:

- [ ] ✅ Follows established import organization
- [ ] ✅ Uses proper dependency injection patterns
- [ ] ✅ Includes comprehensive error handling
- [ ] ✅ Uses GeneralResponse format consistently
- [ ] ✅ Includes complete type annotations
- [ ] ✅ Has proper docstrings for all public methods
- [ ] ✅ Follows layer separation rules
- [ ] ✅ Includes proper logging with context
- [ ] ✅ Handles authentication correctly
- [ ] ✅ Updates relevant documentation

### Code Review Standards:

- **Architecture Compliance**: Verify layer separation is maintained
- **Pattern Consistency**: Ensure new code matches existing patterns
- **Error Handling**: Confirm comprehensive error handling is implemented
- **Type Safety**: Validate all type hints are present and correct
- **Documentation**: Check that docstrings are complete and accurate

## 🔄 Continuous Improvement

### When Patterns Evolve:

1. **Document Changes**: Update systemPatterns.md with new patterns
2. **Update Templates**: Modify this guide with improved patterns
3. **Refactor Consistently**: Apply pattern changes across all similar components
4. **Update Documentation**: Ensure all memory bank files reflect current patterns

### Performance Monitoring:

- Monitor API response times
- Track error rates and patterns
- Review database query performance
- Assess code maintainability metrics

---

**Remember**: This structure has been carefully designed to ensure maintainability, scalability, and consistency. Following these patterns will ensure that the codebase remains clean, predictable, and easy to work with as the project grows.

**Last Updated**: January 2025
**Version**: 1.0