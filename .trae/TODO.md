# TODO:

- [x] examine_chronic_conditions: Examine chronic_conditions_router.py to identify direct model creation/updates that need to be moved to service layer (priority: High)
- [x] refactor_chronic_conditions_service: Update chronic_conditions_service.py to handle TalentChronicConditions object creation and field updates internally (priority: High)
- [x] refactor_chronic_conditions_router: Update chronic_conditions_router.py to remove direct model instantiation and pass schema objects to service (priority: High)
- [x] examine_ongoing_health: Examine ongoing_health_router.py to identify direct model creation/updates that need to be moved to service layer (priority: High)
- [x] refactor_ongoing_health_service: Update ongoing_health_service.py to handle TalentOngoingHealth object creation and field updates internally (priority: High)
- [x] refactor_ongoing_health_router: Update ongoing_health_router.py to remove direct model instantiation and pass schema objects to service (priority: High)
- [x] examine_past_health: Examine past_health_router.py to identify direct model creation/updates that need to be moved to service layer (priority: High)
- [x] refactor_past_health_service: Update past_health_service.py to handle TalentPastHealth object creation and field updates internally (priority: High)
- [x] refactor_past_health_router: Update past_health_router.py to remove direct model instantiation and pass schema objects to service (priority: High)
