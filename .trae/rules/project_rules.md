# AI Project Rules - <PERSON>O Admin Backend

## 🚨 <PERSON><PERSON><PERSON><PERSON>Y AI ASSISTANT REQUIREMENTS

**CRITICAL**: These rules MUST be followed by ALL AI assistants working on this project. Violation of these rules will result in incomplete or incorrect assistance.

## 📋 Pre-Development Documentation Review

### MANDATORY READING SEQUENCE

Before providing ANY development assistance, AI assistants MUST:

1. **Read Project Foundation** (in this exact order):
   - [MANDATORY_READING.md](../../MANDATORY_READING.md) - Critical pre-development requirements
   - [Memory Bank README](../../memory-bank/README.md) - Documentation structure overview
   - [Project Brief](../../memory-bank/projectbrief.md) - Core project requirements and goals
   - [Product Context](../../memory-bank/productContext.md) - Business context and user needs

2. **Understand Current State**:
   - [Active Context](../../memory-bank/activeContext.md) - Current development status and priorities
   - [Progress](../../memory-bank/progress.md) - Implementation status and completed features
   - [Tech Context](../../memory-bank/techContext.md) - Technology decisions and architecture

3. **Review Development Standards**:
   - [System Patterns](../../memory-bank/systemPatterns.md) - Coding patterns and architectural decisions
   - [Development Rules](../../DEVELOPMENT_RULES.md) - Mandatory coding standards and practices
   - [Project Documentation](../../PROJECT_DOCUMENTATION.md) - Quick start and architecture overview

### VERIFICATION CHECKLIST

Before starting any development task, AI assistants MUST confirm:

- [ ] ✅ Read all memory bank files in the specified order
- [ ] ✅ Understood the current project implementation status
- [ ] ✅ Reviewed existing architectural patterns for similar functionality
- [ ] ✅ Checked the progress document for what's already implemented
- [ ] ✅ Understood the technical constraints and technology stack
- [ ] ✅ Reviewed mandatory development rules and coding standards
- [ ] ✅ Identified which layer(s) the requested changes affect

## 🏗️ Architectural Compliance Rules

### Layer Separation (STRICTLY ENFORCED)

AI assistants MUST respect the established architecture:

```
┌─────────────────┐
│   API Layer     │  ← FastAPI routers, request/response handling
├─────────────────┤
│  Service Layer  │  ← Business logic, validation, orchestration
├─────────────────┤
│Repository Layer │  ← Data access, database operations
├─────────────────┤
│  Database Layer │  ← SQLModel models, database schema
└─────────────────┘
```

### Code Quality Requirements

AI assistants MUST ensure all generated code includes:

- **Type Hints**: Full type annotations for all functions and methods
- **Error Handling**: Comprehensive try/catch blocks with proper logging
- **Response Patterns**: Consistent GeneralResponse format for all API endpoints
- **Logging**: Structured logging using established patterns
- **Documentation**: Docstrings for all public methods and complex logic

### Pattern Consistency

AI assistants MUST:

- Follow existing patterns found in the codebase
- Use established imports and dependency injection patterns
- Maintain consistent naming conventions
- Follow the established file and folder structure

## 🔍 Context Analysis Requirements

### Before Making Changes

AI assistants MUST:

1. **Analyze Existing Code**: Review similar implementations in the codebase
2. **Check Dependencies**: Understand what libraries and utilities are already available
3. **Review Related Files**: Examine connected components that might be affected
4. **Validate Approach**: Ensure the proposed solution aligns with existing patterns

### Implementation Standards

AI assistants MUST:

- Use existing utilities and helper functions when available
- Follow established error handling patterns
- Implement proper database transaction management
- Use consistent response formatting
- Add appropriate logging at all levels

## 📝 Documentation Update Requirements

### When Making Changes

AI assistants MUST update relevant documentation:

- **Progress.md**: Update implementation status when completing features
- **Active Context**: Update current development focus if priorities change
- **System Patterns**: Document new patterns if established
- **Tech Context**: Update if new dependencies or technologies are added

### Documentation Standards

AI assistants MUST:

- Keep documentation current with code changes
- Use consistent formatting and structure
- Provide clear, actionable information
- Include examples and usage patterns

## 🚫 Prohibited Actions

### AI assistants MUST NOT:

- **Skip Documentation Review**: Never start development without reading the required files
- **Violate Layer Separation**: Don't mix concerns between architectural layers
- **Ignore Existing Patterns**: Don't create new patterns when established ones exist
- **Skip Error Handling**: Never implement functionality without proper error management
- **Use Inconsistent Responses**: Don't deviate from GeneralResponse format
- **Skip Type Hints**: Never create functions without proper type annotations
- **Ignore Security**: Don't implement features without considering security implications
- **Create Duplicate Code**: Don't reimplement existing functionality

## 🎯 Quality Assurance

### Before Completing Tasks

AI assistants MUST verify:

- Code follows established architectural patterns
- All functions have proper type hints
- Error handling is comprehensive and consistent
- Logging is implemented using established patterns
- Response formats match existing standards
- Security considerations are addressed
- Documentation is updated appropriately

### Code Review Standards

AI assistants should ensure code meets:

- **Readability**: Clear, self-documenting code with appropriate comments
- **Maintainability**: Follows established patterns for easy future modifications
- **Testability**: Code structure supports unit and integration testing
- **Performance**: Efficient database queries and API response times
- **Security**: Proper authentication, authorization, and data protection

## 🔧 Technology Stack Compliance

### Required Technologies

AI assistants MUST use the established tech stack:

- **Backend Framework**: FastAPI with async/await patterns
- **Database ORM**: SQLModel for type-safe database operations
- **Database**: MySQL with proper connection management
- **Authentication**: JWT-based authentication system
- **Package Management**: uv for dependency management
- **Type Checking**: basedpyright for static type analysis
- **Code Formatting**: Black for consistent code style
- **Logging**: Loguru for structured application logging

### Dependency Management

AI assistants MUST:

- Use existing dependencies when possible
- Justify new dependency additions
- Update pyproject.toml when adding new packages
- Consider security implications of new dependencies

## 📊 Progress Tracking

### Implementation Updates

When completing features, AI assistants MUST:

- Update the progress.md file with completion status
- Mark features as completed in activeContext.md
- Document any new patterns in systemPatterns.md
- Update PROJECT_DOCUMENTATION.md if architecture changes

### Status Communication

AI assistants should:

- Clearly communicate what was implemented
- Explain any deviations from requested functionality
- Highlight any dependencies or follow-up work needed
- Provide clear next steps for continued development

## 🆘 Escalation Procedures

### When to Seek Clarification

AI assistants should ask for clarification when:

- Requested changes conflict with established patterns
- Implementation requires architectural modifications
- Security implications are unclear
- Business logic requirements are ambiguous

### Information to Provide

When seeking clarification, include:

- Specific conflict or ambiguity identified
- Relevant documentation references
- Proposed alternative approaches
- Potential impact on existing functionality

## 🔄 Continuous Improvement

### Learning from the Codebase

AI assistants should:

- Study existing implementations for patterns
- Learn from established error handling approaches
- Understand the reasoning behind architectural decisions
- Adapt recommendations based on project-specific context

### Pattern Recognition

AI assistants should:

- Identify recurring patterns in the codebase
- Suggest improvements based on observed patterns
- Recommend consistency improvements
- Highlight potential refactoring opportunities

---

**Remember**: These rules exist to maintain code quality, architectural consistency, and project momentum. Following them ensures that AI assistance aligns with project goals and standards.

**Enforcement**: Mandatory for all AI development assistance
**Last Updated**: January 2025
**Version**: 1.0