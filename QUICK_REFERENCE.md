# 🚀 Quick Reference - BPO Admin Backend

## 📚 Essential Documentation Links

### 🚨 Start Here (MANDATORY)
- **[MANDATORY READING](./MANDATORY_READING.md)** - Must read before any development
- **[Development Rules](./DEVELOPMENT_RULES.md)** - Coding standards and architectural patterns

### 📖 Project Understanding
- **[Project Documentation](./PROJECT_DOCUMENTATION.md)** - Quick start and architecture overview
- **[Memory Bank README](./memory-bank/README.md)** - Documentation structure overview
- **[Project Brief](./memory-bank/projectbrief.md)** - Core project requirements and goals

### 🎯 Current State
- **[Active Context](./memory-bank/activeContext.md)** - Current development status and priorities
- **[Progress](./memory-bank/progress.md)** - Implementation status and completed features
- **[Tech Context](./memory-bank/techContext.md)** - Technology decisions and architecture

### 🏗️ Development Patterns
- **[System Patterns](./memory-bank/systemPatterns.md)** - Coding patterns and architectural decisions
- **[Product Context](./memory-bank/productContext.md)** - Business context and user needs

## ⚡ Quick Commands

### Development Setup
```bash
# Install dependencies
uv sync

# Run development server
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run type checking
uv run basedpyright

# Run tests
uv run pytest

# Format code
uv run black .
```

### Database Operations
```bash
# Create migration
uv run alembic revision --autogenerate -m "Description"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1
```

## 🏗️ Architecture Quick Reference

### Layer Structure
```
API Layer (Routers)     → HTTP handling, authentication, response formatting
    ↓
Service Layer          → Business logic, validation, orchestration
    ↓
Repository Layer       → Data access, database operations
    ↓
Database Layer (Models) → SQLModel entities, relationships
```

### Response Pattern
```python
return GeneralResponse(
    status_code=status.HTTP_200_OK,
    message="Operation successful",
    data=result,
)
```

### Error Handling Pattern
```python
try:
    result = service.method(data)
    return GeneralResponse(...)
except HTTPException:
    raise
except Exception as e:
    log_api_error(endpoint=request.url.path, method=request.method, status_code=500, error=str(e))
    raise HTTPException(status_code=500, detail="Internal server error")
```

## 🔧 Tech Stack

### Core Technologies
- **Backend Framework**: FastAPI
- **Database ORM**: SQLModel
- **Database**: MySQL
- **Migrations**: Alembic
- **Authentication**: JWT
- **Package Manager**: uv
- **Type Checking**: basedpyright
- **Code Formatting**: Black
- **Testing**: pytest

### Key Dependencies
```toml
fastapi = "^0.104.1"
sqlmodel = "^0.0.14"
mysql-connector-python = "^8.2.0"
alembic = "^1.13.1"
pyjwt = "^2.8.0"
passlib = "^1.7.4"
python-multipart = "^0.0.6"
uvicorn = "^0.24.0"
```

## 📁 Project Structure

```
backend/
├── app/
│   ├── api/                 # API routers
│   │   ├── auth/           # Authentication endpoints
│   │   └── talent/         # Talent management endpoints
│   ├── core/               # Core utilities and config
│   ├── db/                 # Database models and connection
│   ├── schemas/            # Pydantic schemas
│   ├── services/           # Business logic layer
│   └── repositories/       # Data access layer
├── memory-bank/            # Project documentation
├── migrations/             # Alembic migrations
└── tests/                  # Test files
```

## 🎯 Current Implementation Status

### ✅ Completed
- Authentication system
- Talent profile management
- Banking information management
- Wage management system
- Documentation framework
- Development rules and patterns

### 🚧 In Progress
- Code quality improvements
- Comprehensive logging
- Type safety enhancements

### 📋 Planned
- Performance optimization
- Advanced reporting
- Audit trails
- Advanced analytics

## 🔗 Important URLs

### Development
- **API Server**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc

### Database
- **Host**: localhost:3306
- **Database**: bpo_admin
- **Connection**: MySQL Connector

## 🆘 Common Issues & Solutions

### Type Errors
- Ensure all functions have type hints
- Use `basedpyright` for type checking
- Convert schemas to models properly

### Database Issues
- Check migration status: `uv run alembic current`
- Verify database connection in config
- Use proper session management

### Import Errors
- Organize imports: stdlib → third-party → local
- Use absolute imports from app root
- Check circular import issues

### Authentication Issues
- Verify JWT token format
- Check token expiration
- Ensure proper middleware setup

## 📞 Getting Help

1. **Check Documentation**: Start with memory bank files
2. **Review Existing Code**: Look for similar patterns
3. **Check Tests**: See usage examples
4. **Review Recent Commits**: Understand recent changes
5. **Ask Specific Questions**: Provide context and error details

---

**Remember**: Always read the mandatory documentation before starting development!

**Last Updated**: January 2025