{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port=3500 --host", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vuepic/vue-datepicker": "^11.0.2", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "dayjs": "^1.11.13", "lucide-vue-next": "^0.534.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "vue": "^3.4.38", "vue-router": "^4.5.1", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^5.1.3", "typescript": "^5.5.3", "vite": "^6.0.0", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^2.1.4"}}