# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Python files
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# JavaScript/TypeScript files
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS/SCSS files
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Configuration files
[*.{toml,ini,cfg}]
indent_style = space
indent_size = 2

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 2

# Makefile
[Makefile]
indent_style = tab
indent_size = 4