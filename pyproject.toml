[project]
name = "bpo-admin"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.2",
    "apscheduler>=3.10.4",
    "bcrypt>=4.3.0",
    "cryptography>=45.0.5",
    "email-validator>=2.0.0",
    "fastapi>=0.115.14",
    "jinja2>=3.1.0",
    "loguru>=0.7.3",
    "mysql-connector-python>=9.3.0",
    "pydantic-settings>=2.10.1",
    "pyjwt>=2.10.1",
    "python-multipart>=0.0.20",
    "requests>=2.32.5",
    "sqlmodel>=0.0.24",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "basedpyright>=1.0.0",
    "black>=24.0.0",
]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
